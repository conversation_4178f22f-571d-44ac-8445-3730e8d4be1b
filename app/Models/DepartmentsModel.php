<?php

namespace App\Models;

use CodeIgniter\Model;

class DepartmentsModel extends Model
{
    protected $table            = 'departments';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'department_name',
        'department_description',
        'faculty_id',
        'head_of_department_id',
        'department_image',
        'profile',
        'programmes_curriculum',
        'research_groups',
        'contact_info',
        'created_by'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'department_created';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'department_name' => 'required|max_length[100]|is_unique[departments.department_name,id,{id}]',
        'department_description' => 'permit_empty',
        'faculty_id' => 'required|integer',
        'head_of_department_id' => 'permit_empty|integer',
        'department_image' => 'permit_empty|max_length[200]',
        'profile' => 'permit_empty',
        'programmes_curriculum' => 'permit_empty',
        'research_groups' => 'permit_empty',
        'contact_info' => 'permit_empty',
        'created_by' => 'permit_empty|max_length[100]'
    ];

    protected $validationMessages = [
        'department_name' => [
            'required' => 'Department name is required',
            'max_length' => 'Department name cannot exceed 100 characters',
            'is_unique' => 'Department name already exists'
        ],
        'faculty_id' => [
            'required' => 'Faculty selection is required',
            'integer' => 'Faculty ID must be a valid integer'
        ],
        'head_of_department_id' => [
            'integer' => 'Head of department ID must be a valid integer'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get all departments with faculty information
     */
    public function getAllDepartments()
    {
        return $this->select('departments.*, faculties.faculty_name, faculty_members.full_name as head_name')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->join('faculty_members', 'departments.head_of_department_id = faculty_members.record_id', 'left')
                    ->findAll();
    }

    /**
     * Get department with head and faculty information
     */
    public function getDepartmentWithHead($id)
    {
        return $this->select('departments.*, faculties.faculty_name, faculty_members.full_name as head_name, faculty_members.designation as head_designation')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->join('faculty_members', 'departments.head_of_department_id = faculty_members.record_id', 'left')
                    ->find($id);
    }

    /**
     * Get departments by faculty
     */
    public function getDepartmentsByFaculty($facultyId)
    {
        return $this->where('faculty_id', $facultyId)->findAll();
    }

    /**
     * Get faculty members from this department
     */
    public function getFacultyMembers($departmentId)
    {
        $db = \Config\Database::connect();
        return $db->table('faculty_members')
                  ->where('department_id', $departmentId)
                  ->where('status', 'active')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get current students from this department
     */
    public function getCurrentStudents($departmentId)
    {
        $db = \Config\Database::connect();
        return $db->table('students')
                  ->where('department_id', $departmentId)
                  ->where('actual_completion_year IS NULL')
                  ->where('status', 'current')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get alumni from this department
     */
    public function getAlumni($departmentId)
    {
        $db = \Config\Database::connect();
        return $db->table('students')
                  ->where('department_id', $departmentId)
                  ->where('actual_completion_year IS NOT NULL')
                  ->where('status', 'alumni')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get news and events from this department
     */
    public function getDepartmentNews($departmentId)
    {
        $db = \Config\Database::connect();
        return $db->table('events_news-events')
                  ->where('department_id', $departmentId)
                  ->where('is_active', 'active')
                  ->orderBy('created_at', 'DESC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get available heads for this department
     */
    public function getAvailableHeads($departmentId)
    {
        $db = \Config\Database::connect();
        return $db->table('faculty_members')
                  ->select('record_id, full_name, designation')
                  ->where('department_id', $departmentId)
                  ->where('status', 'active')
                  ->orderBy('designation', 'ASC')
                  ->orderBy('full_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Update head of department
     */
    public function updateHead($departmentId, $headId)
    {
        // Validate that the head belongs to this department
        $availableHeads = $this->getAvailableHeads($departmentId);
        $headIds = array_column($availableHeads, 'record_id');
        
        if (!in_array($headId, $headIds)) {
            return false;
        }

        return $this->update($departmentId, ['head_of_department_id' => $headId]);
    }

    /**
     * Remove head from department
     */
    public function removeHead($departmentId)
    {
        return $this->update($departmentId, ['head_of_department_id' => null]);
    }

    /**
     * Get department showcase data
     */
    public function getDepartmentShowcase($id)
    {
        $department = $this->getDepartmentWithHead($id);
        if (!$department) {
            return null;
        }

        // Get aggregated data with counts
        $facultyMembers = $this->getFacultyMembers($id);
        $currentStudents = $this->getCurrentStudents($id);
        $alumni = $this->getAlumni($id);
        $newsEvents = $this->getDepartmentNews($id);

        // Get PhD awardees from faculty members
        $phdAwardees = array_filter($facultyMembers, function($member) {
            return $member['highest_qualification'] === 'PhD';
        });

        return [
            'department' => $department,
            'faculty_members' => $facultyMembers,
            'phd_awardees' => array_values($phdAwardees),
            'current_students' => $currentStudents,
            'alumni' => $alumni,
            'news_events' => $newsEvents,
            'statistics' => [
                'total_faculty_members' => count($facultyMembers),
                'total_phd_awardees' => count($phdAwardees),
                'total_current_students' => count($currentStudents),
                'total_alumni' => count($alumni),
                'total_news_events' => count($newsEvents)
            ]
        ];
    }

    /**
     * Get department showcase data with caching
     */
    public function getDepartmentShowcaseWithCache($id, $cacheDuration = 3600)
    {
        $cache = \Config\Services::cache();
        $cacheKey = "department_showcase_{$id}";
        
        $showcaseData = $cache->get($cacheKey);
        
        if ($showcaseData === null) {
            $showcaseData = $this->getDepartmentShowcase($id);
            if ($showcaseData) {
                $cache->save($cacheKey, $showcaseData, $cacheDuration);
            }
        }
        
        return $showcaseData;
    }

    /**
     * Clear department showcase cache
     */
    public function clearShowcaseCache($id)
    {
        $cache = \Config\Services::cache();
        $cacheKey = "department_showcase_{$id}";
        return $cache->delete($cacheKey);
    }

    /**
     * Check if department can be deleted (no faculty members or students)
     */
    public function canDelete($id)
    {
        $facultyMembers = $this->getFacultyMembers($id);
        $students = $this->getCurrentStudents($id);
        $alumni = $this->getAlumni($id);
        
        return empty($facultyMembers) && empty($students) && empty($alumni);
    }

    /**
     * Create department with validation
     */
    public function createDepartment($data)
    {
        // Head is optional during creation
        if (isset($data['head_of_department_id']) && empty($data['head_of_department_id'])) {
            unset($data['head_of_department_id']);
        }

        // Validate faculty exists
        $facultyModel = new \App\Models\FacultyModel();
        if (!$facultyModel->find($data['faculty_id'])) {
            return false;
        }

        return $this->insert($data);
    }

    /**
     * Update department with validation
     */
    public function updateDepartment($id, $data)
    {
        // Handle head assignment validation if head_of_department_id is provided
        if (isset($data['head_of_department_id']) && !empty($data['head_of_department_id'])) {
            $availableHeads = $this->getAvailableHeads($id);
            $headIds = array_column($availableHeads, 'record_id');
            
            if (!in_array($data['head_of_department_id'], $headIds)) {
                return false;
            }
        }

        // Validate faculty exists if faculty_id is being updated
        if (isset($data['faculty_id'])) {
            $facultyModel = new \App\Models\FacultyModel();
            if (!$facultyModel->find($data['faculty_id'])) {
                return false;
            }
        }

        // Set validation rules for update (exclude current ID from unique check)
        $this->setValidationRules([
            'department_name' => "required|max_length[100]|is_unique[departments.department_name,id,{$id}]",
            'department_description' => 'permit_empty',
            'faculty_id' => 'required|integer',
            'head_of_department_id' => 'permit_empty|integer',
            'department_image' => 'permit_empty|max_length[200]',
            'profile' => 'permit_empty',
            'programmes_curriculum' => 'permit_empty',
            'research_groups' => 'permit_empty',
            'contact_info' => 'permit_empty',
            'created_by' => 'permit_empty|max_length[100]'
        ]);

        return $this->update($id, $data);
    }

    /**
     * Enhanced search for global search functionality
     */
    public function searchForGlobalSearch(string $query, int $limit = null): array
    {
        $builder = $this->select('departments.*, faculties.faculty_name, faculty_members.full_name as head_name')
                        ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                        ->join('faculty_members', 'departments.head_of_department_id = faculty_members.record_id', 'left')
                        ->groupStart()
                            ->like('departments.department_name', $query)
                            ->orLike('departments.department_description', $query)
                            ->orLike('departments.profile', $query)
                            ->orLike('departments.contact_info', $query)
                            ->orLike('faculties.faculty_name', $query)
                            ->orLike('faculty_members.full_name', $query)
                        ->groupEnd()
                        ->orderBy('departments.department_name', 'ASC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get all faculties for dropdown
     */
    public function getFacultiesList()
    {
        $facultyModel = new \App\Models\FacultyModel();
        return $facultyModel->select('id, faculty_name')->findAll();
    }

    /**
     * Get department data optimized for navigation
     */
    public function getForNavigation()
    {
        return $this->select('departments.id, departments.department_name, departments.department_description, departments.faculty_id, departments.head_of_department_id, departments.department_image, departments.department_created, departments.updated_at, faculties.faculty_name')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->orderBy('faculties.faculty_name', 'ASC')
                    ->orderBy('departments.department_name', 'ASC')
                    ->findAll();
    }

    /**
     * Find department by name or slug
     */
    public function findByNameSlug($name)
    {
        // Try exact name match first
        $department = $this->where('department_name', $name)->first();
        
        if ($department) {
            return $department;
        }

        // Try case-insensitive match
        $department = $this->like('department_name', $name, 'both', false)->first();
        
        if ($department) {
            return $department;
        }

        // Try slug-based matching
        $slug = $this->generateSlug($name);
        $departments = $this->findAll();
        
        foreach ($departments as $department) {
            if ($this->generateSlug($department['department_name']) === $slug) {
                return $department;
            }
        }

        return null;
    }

    /**
     * Generate slug from department name
     */
    protected function generateSlug($name)
    {
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name), '-'));
    }
}