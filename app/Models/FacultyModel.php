<?php

namespace App\Models;

use CodeIgniter\Model;

class FacultyModel extends Model
{
    protected $table            = 'faculties';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'faculty_name',
        'faculty_description', 
        'dean_id',
        'faculty_image',
        'profile',
        'programmes_curriculum',
        'research_groups',
        'contact_info',
        'created_by'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules = [
        'faculty_name' => 'required|max_length[100]|is_unique[faculties.faculty_name,id,{id}]',
        'faculty_description' => 'permit_empty',
        'dean_id' => 'permit_empty|integer',
        'faculty_image' => 'permit_empty|max_length[200]',
        'profile' => 'permit_empty',
        'programmes_curriculum' => 'permit_empty',
        'research_groups' => 'permit_empty',
        'contact_info' => 'permit_empty',
        'created_by' => 'permit_empty|max_length[100]'
    ];

    protected $validationMessages = [
        'faculty_name' => [
            'required' => 'Faculty name is required',
            'max_length' => 'Faculty name cannot exceed 100 characters',
            'is_unique' => 'Faculty name already exists'
        ],
        'dean_id' => [
            'integer' => 'Dean ID must be a valid integer'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get all faculties with basic information
     */
    public function getAllFaculties()
    {
        return $this->select('id, faculty_name, faculty_description, dean_id, created_at, updated_at')
                    ->findAll();
    }

    /**
     * Get faculty with dean information
     */
    public function getFacultyWithDean($id)
    {
        return $this->select('faculties.*, faculty_members.full_name as dean_name, faculty_members.designation as dean_designation')
                    ->join('faculty_members', 'faculties.dean_id = faculty_members.record_id', 'left')
                    ->find($id);
    }

    /**
     * Get all departments belonging to a faculty
     */
    public function getDepartments($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('departments')
                  ->where('faculty_id', $facultyId)
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get all faculty members from departments within this faculty
     */
    public function getFacultyMembers($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('faculty_members fm')
                  ->select('fm.*, d.department_name')
                  ->join('departments d', 'fm.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('fm.status', 'active')
                  ->orderBy('fm.designation', 'ASC')
                  ->orderBy('fm.full_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get PhD awardees from this faculty
     */
    public function getPhdAwardees($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('faculty_members fm')
                  ->select('fm.*, d.department_name')
                  ->join('departments d', 'fm.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('fm.highest_qualification', 'PhD')
                  ->where('fm.status', 'active')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get current students from this faculty
     */
    public function getCurrentStudents($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('students s')
                  ->select('s.*, d.department_name')
                  ->join('departments d', 's.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('s.actual_completion_year IS NULL')
                  ->where('s.status', 'current')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get alumni from this faculty
     */
    public function getAlumni($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('students s')
                  ->select('s.*, d.department_name')
                  ->join('departments d', 's.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('s.actual_completion_year IS NOT NULL')
                  ->where('s.status', 'alumni')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get news and events from this faculty (via departments)
     */
    public function getFacultyNews($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('events_news-events ne')
                  ->select('ne.*, d.department_name')
                  ->join('departments d', 'ne.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('ne.is_active', 'active')
                  ->orderBy('ne.created_at', 'DESC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get available faculty members who can be dean for this faculty
     */
    public function getAvailableDeans($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('faculty_members fm')
                  ->select('fm.record_id, fm.full_name, fm.designation, d.department_name')
                  ->join('departments d', 'fm.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('fm.status', 'active')
                  ->orderBy('fm.designation', 'ASC')
                  ->orderBy('fm.full_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Update dean for a faculty
     */
    public function updateDean($facultyId, $deanId)
    {
        // Validate that the dean belongs to this faculty's departments
        $availableDeans = $this->getAvailableDeans($facultyId);
        $deanIds = array_column($availableDeans, 'record_id');
        
        if (!in_array($deanId, $deanIds)) {
            return false;
        }

        return $this->update($facultyId, ['dean_id' => $deanId]);
    }

    /**
     * Remove dean from faculty
     */
    public function removeDean($facultyId)
    {
        return $this->update($facultyId, ['dean_id' => null]);
    }

    /**
     * Get faculty showcase data (aggregated information)
     */
    public function getFacultyShowcase($id)
    {
        $faculty = $this->getFacultyWithDean($id);
        if (!$faculty) {
            return null;
        }

        // Get aggregated data with counts for performance
        $departments = $this->getDepartments($id);
        $facultyMembers = $this->getFacultyMembers($id);
        $phdAwardees = $this->getPhdAwardees($id);
        $currentStudents = $this->getCurrentStudents($id);
        $alumni = $this->getAlumni($id);
        $newsEvents = $this->getFacultyNews($id);

        return [
            'faculty' => $faculty,
            'departments' => $departments,
            'faculty_members' => $facultyMembers,
            'phd_awardees' => $phdAwardees,
            'current_students' => $currentStudents,
            'alumni' => $alumni,
            'news_events' => $newsEvents,
            'statistics' => [
                'total_departments' => count($departments),
                'total_faculty_members' => count($facultyMembers),
                'total_phd_awardees' => count($phdAwardees),
                'total_current_students' => count($currentStudents),
                'total_alumni' => count($alumni),
                'total_news_events' => count($newsEvents)
            ]
        ];
    }

    /**
     * Get faculty showcase data with caching
     */
    public function getFacultyShowcaseWithCache($id, $cacheDuration = 3600)
    {
        $cache = \Config\Services::cache();
        $cacheKey = "faculty_showcase_{$id}";
        
        $showcaseData = $cache->get($cacheKey);
        
        if ($showcaseData === null) {
            $showcaseData = $this->getFacultyShowcase($id);
            if ($showcaseData) {
                $cache->save($cacheKey, $showcaseData, $cacheDuration);
            }
        }
        
        return $showcaseData;
    }

    /**
     * Clear faculty showcase cache
     */
    public function clearShowcaseCache($id)
    {
        $cache = \Config\Services::cache();
        $cacheKey = "faculty_showcase_{$id}";
        return $cache->delete($cacheKey);
    }

    /**
     * Get faculty data optimized for navigation
     */
    public function getForNavigation()
    {
        return $this->select('id, faculty_name, faculty_description, dean_id, faculty_image, created_at, updated_at')
                    ->orderBy('faculty_name', 'ASC')
                    ->findAll();
    }

    /**
     * Find faculty by name or slug
     */
    public function findByNameSlug($name)
    {
        // Try exact name match first
        $faculty = $this->where('faculty_name', $name)->first();
        
        if ($faculty) {
            return $faculty;
        }

        // Try case-insensitive match
        $faculty = $this->like('faculty_name', $name, 'both', false)->first();
        
        if ($faculty) {
            return $faculty;
        }

        // Try slug-based matching
        $slug = $this->generateSlug($name);
        $faculties = $this->findAll();
        
        foreach ($faculties as $faculty) {
            if ($this->generateSlug($faculty['faculty_name']) === $slug) {
                return $faculty;
            }
        }

        return null;
    }

    /**
     * Generate slug from faculty name
     */
    protected function generateSlug($name)
    {
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name), '-'));
    }

    /**
     * Check if faculty can be deleted (no departments)
     */
    public function canDelete($id)
    {
        $departments = $this->getDepartments($id);
        return empty($departments);
    }

    /**
     * Create faculty with validation
     */
    public function createFaculty($data)
    {
        // Dean is optional during creation
        if (isset($data['dean_id']) && empty($data['dean_id'])) {
            unset($data['dean_id']);
        }

        return $this->insert($data);
    }

    /**
     * Update faculty with validation
     */
    public function updateFaculty($id, $data)
    {
        // Handle dean assignment validation if dean_id is provided
        if (isset($data['dean_id']) && !empty($data['dean_id'])) {
            $availableDeans = $this->getAvailableDeans($id);
            $deanIds = array_column($availableDeans, 'record_id');
            
            if (!in_array($data['dean_id'], $deanIds)) {
                return false;
            }
        }

        // Set validation rules for update (exclude current ID from unique check)
        $this->setValidationRules([
            'faculty_name' => "required|max_length[100]|is_unique[faculties.faculty_name,id,{$id}]",
            'faculty_description' => 'permit_empty',
            'dean_id' => 'permit_empty|integer',
            'faculty_image' => 'permit_empty|max_length[200]',
            'profile' => 'permit_empty',
            'programmes_curriculum' => 'permit_empty',
            'research_groups' => 'permit_empty',
            'contact_info' => 'permit_empty',
            'created_by' => 'permit_empty|max_length[100]'
        ]);

        return $this->update($id, $data);
    }
}
