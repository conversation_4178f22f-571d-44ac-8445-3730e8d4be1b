<?php

namespace App\Models;

use CodeIgniter\Model;

class FacultyMembersModel extends Model
{
    protected $table            = 'faculty_members';
    protected $primaryKey       = 'record_id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'full_name',
        'designation',
        'contact_number',
        'email',
        'department_id',
        'employee_id',
        'highest_qualification',
        'discipline',
        'year_completion',
        'university_name',
        'photo',
        'bio',
        'status'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'full_name' => 'required|max_length[100]',
        'designation' => 'required|in_list[Professor,Associate Professor,Assistant Professor,Lecturer]',
        'contact_number' => 'required|max_length[10]|numeric',
        'email' => 'required|max_length[100]|valid_email|is_unique[faculty_members.email,record_id,{record_id}]',
        'department_id' => 'required|integer',
        'employee_id' => 'required|max_length[20]|is_unique[faculty_members.employee_id,record_id,{record_id}]',
        'highest_qualification' => 'required|in_list[PhD,MPhil,Master\'s,Bachelor\'s]',
        'discipline' => 'required|max_length[50]',
        'year_completion' => 'required|max_length[4]|numeric',
        'university_name' => 'required|max_length[100]',
        'photo' => 'permit_empty|max_length[255]',
        'bio' => 'permit_empty',
        'status' => 'permit_empty|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'full_name' => [
            'required' => 'Full name is required',
            'max_length' => 'Full name cannot exceed 100 characters'
        ],
        'designation' => [
            'required' => 'Designation is required',
            'in_list' => 'Please select a valid designation'
        ],
        'contact_number' => [
            'required' => 'Contact number is required',
            'max_length' => 'Contact number cannot exceed 10 digits',
            'numeric' => 'Contact number must contain only numbers'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email already exists'
        ],
        'department_id' => [
            'required' => 'Department selection is required',
            'integer' => 'Department ID must be a valid integer'
        ],
        'employee_id' => [
            'required' => 'Employee ID is required',
            'is_unique' => 'Employee ID already exists'
        ],
        'highest_qualification' => [
            'required' => 'Highest qualification is required',
            'in_list' => 'Please select a valid qualification'
        ],
        'discipline' => [
            'required' => 'Discipline is required',
            'max_length' => 'Discipline cannot exceed 50 characters'
        ],
        'year_completion' => [
            'required' => 'Year of completion is required',
            'numeric' => 'Year must be a valid number'
        ],
        'university_name' => [
            'required' => 'University name is required',
            'max_length' => 'University name cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Get all faculty members with department and faculty information
     */
    public function getAllFacultyMembers()
    {
        return $this->select('faculty_members.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'faculty_members.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->where('faculty_members.status', 'active')
                    ->orderBy('faculty_members.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get faculty member with department and faculty information
     */
    public function getFacultyMemberWithDetails($id)
    {
        return $this->select('faculty_members.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'faculty_members.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->find($id);
    }

    /**
     * Get faculty members by department
     */
    public function getByDepartment($departmentId)
    {
        return $this->where('department_id', $departmentId)
                    ->where('status', 'active')
                    ->orderBy('designation', 'ASC')
                    ->orderBy('full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get faculty members by faculty (via departments)
     */
    public function getByFaculty($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('faculty_members fm')
                  ->select('fm.*, d.department_name')
                  ->join('departments d', 'fm.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->where('fm.status', 'active')
                  ->orderBy('fm.designation', 'ASC')
                  ->orderBy('fm.full_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get faculty members by designation
     */
    public function getByDesignation($designation)
    {
        return $this->where('designation', $designation)
                    ->where('status', 'active')
                    ->orderBy('full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get faculty members by qualification
     */
    public function getByQualification($qualification)
    {
        return $this->select('faculty_members.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'faculty_members.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->where('faculty_members.highest_qualification', $qualification)
                    ->where('faculty_members.status', 'active')
                    ->orderBy('faculty_members.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get PhD holders
     */
    public function getPhdHolders()
    {
        return $this->getByQualification('PhD');
    }

    /**
     * Search faculty members
     */
    public function searchFacultyMembers($searchTerm)
    {
        return $this->select('faculty_members.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'faculty_members.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->groupStart()
                        ->like('faculty_members.full_name', $searchTerm)
                        ->orLike('faculty_members.employee_id', $searchTerm)
                        ->orLike('faculty_members.email', $searchTerm)
                        ->orLike('departments.department_name', $searchTerm)
                        ->orLike('faculties.faculty_name', $searchTerm)
                    ->groupEnd()
                    ->where('faculty_members.status', 'active')
                    ->orderBy('faculty_members.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Enhanced search for global search functionality
     */
    public function searchForGlobalSearch(string $query, int $limit = null): array
    {
        $builder = $this->select('faculty_members.*, departments.department_name, faculties.faculty_name')
                        ->join('departments', 'faculty_members.department_id = departments.id', 'left')
                        ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                        ->groupStart()
                            ->like('faculty_members.full_name', $query)
                            ->orLike('faculty_members.employee_id', $query)
                            ->orLike('faculty_members.email', $query)
                            ->orLike('faculty_members.designation', $query)
                            ->orLike('faculty_members.discipline', $query)
                            ->orLike('faculty_members.bio', $query)
                            ->orLike('departments.department_name', $query)
                            ->orLike('faculties.faculty_name', $query)
                        ->groupEnd()
                        ->where('faculty_members.status', 'active')
                        ->orderBy('faculty_members.full_name', 'ASC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get departments list for dropdown
     */
    public function getDepartmentsList()
    {
        $db = \Config\Database::connect();
        return $db->table('departments d')
                  ->select('d.id, d.department_name, f.faculty_name')
                  ->join('faculties f', 'd.faculty_id = f.id', 'left')
                  ->orderBy('f.faculty_name', 'ASC')
                  ->orderBy('d.department_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Check if faculty member can be deleted
     */
    public function canDelete($id)
    {
        // Check if member is assigned as dean
        $db = \Config\Database::connect();
        $deanAssignments = $db->table('faculties')
                              ->where('dean_id', $id)
                              ->countAllResults();

        // Check if member is assigned as head of department
        $headAssignments = $db->table('departments')
                              ->where('head_of_department_id', $id)
                              ->countAllResults();

        return ($deanAssignments === 0 && $headAssignments === 0);
    }

    /**
     * Get assignments for a faculty member (dean/head positions)
     */
    public function getAssignments($id)
    {
        $db = \Config\Database::connect();
        
        $assignments = [];

        // Check dean assignments
        $deanAssignments = $db->table('faculties')
                              ->select('id, faculty_name')
                              ->where('dean_id', $id)
                              ->get()
                              ->getResultArray();

        foreach ($deanAssignments as $assignment) {
            $assignments[] = [
                'type' => 'dean',
                'entity_id' => $assignment['id'],
                'entity_name' => $assignment['faculty_name']
            ];
        }

        // Check head assignments
        $headAssignments = $db->table('departments')
                              ->select('id, department_name')
                              ->where('head_of_department_id', $id)
                              ->get()
                              ->getResultArray();

        foreach ($headAssignments as $assignment) {
            $assignments[] = [
                'type' => 'head',
                'entity_id' => $assignment['id'],
                'entity_name' => $assignment['department_name']
            ];
        }

        return $assignments;
    }

    /**
     * Create faculty member with validation
     */
    public function createFacultyMember($data)
    {
        // Validate department exists
        $departmentsModel = new \App\Models\DepartmentsModel();
        if (!$departmentsModel->find($data['department_id'])) {
            return false;
        }

        // Set default status if not provided
        if (!isset($data['status'])) {
            $data['status'] = 'active';
        }

        return $this->insert($data);
    }

    /**
     * Update faculty member with validation
     */
    public function updateFacultyMember($id, $data)
    {
        // Validate department exists if department_id is being updated
        if (isset($data['department_id'])) {
            $departmentsModel = new \App\Models\DepartmentsModel();
            if (!$departmentsModel->find($data['department_id'])) {
                return false;
            }
        }

        // Set validation rules for update (exclude current ID from unique checks)
        $this->setValidationRules([
            'full_name' => 'required|max_length[100]',
            'designation' => 'required|in_list[Professor,Associate Professor,Assistant Professor,Lecturer]',
            'contact_number' => 'required|max_length[10]|numeric',
            'email' => "required|max_length[100]|valid_email|is_unique[faculty_members.email,record_id,{$id}]",
            'department_id' => 'required|integer',
            'employee_id' => "required|max_length[20]|is_unique[faculty_members.employee_id,record_id,{$id}]",
            'highest_qualification' => 'required|in_list[PhD,MPhil,Master\'s,Bachelor\'s]',
            'discipline' => 'required|max_length[50]',
            'year_completion' => 'required|max_length[4]|numeric',
            'university_name' => 'required|max_length[100]',
            'photo' => 'permit_empty|max_length[255]',
            'bio' => 'permit_empty',
            'status' => 'permit_empty|in_list[active,inactive]'
        ]);

        return $this->update($id, $data);
    }

    /**
     * Deactivate faculty member (soft delete alternative)
     */
    public function deactivateFacultyMember($id)
    {
        return $this->update($id, ['status' => 'inactive']);
    }

    /**
     * Activate faculty member
     */
    public function activateFacultyMember($id)
    {
        return $this->update($id, ['status' => 'active']);
    }

    /**
     * Get faculty member statistics
     */
    public function getStatistics()
    {
        $db = \Config\Database::connect();
        
        $stats = [];

        // Total active faculty members
        $stats['total_active'] = $this->where('status', 'active')->countAllResults();

        // By designation
        $designations = ['Professor', 'Associate Professor', 'Assistant Professor', 'Lecturer'];
        foreach ($designations as $designation) {
            $stats['by_designation'][$designation] = $this->where('designation', $designation)
                                                          ->where('status', 'active')
                                                          ->countAllResults();
        }

        // By qualification
        $qualifications = ['PhD', 'MPhil', 'Master\'s', 'Bachelor\'s'];
        foreach ($qualifications as $qualification) {
            $stats['by_qualification'][$qualification] = $this->where('highest_qualification', $qualification)
                                                              ->where('status', 'active')
                                                              ->countAllResults();
        }

        // By faculty
        $facultyStats = $db->table('faculty_members fm')
                           ->select('f.faculty_name, COUNT(*) as count')
                           ->join('departments d', 'fm.department_id = d.id')
                           ->join('faculties f', 'd.faculty_id = f.id')
                           ->where('fm.status', 'active')
                           ->groupBy('f.id, f.faculty_name')
                           ->orderBy('count', 'DESC')
                           ->get()
                           ->getResultArray();

        $stats['by_faculty'] = $facultyStats;

        return $stats;
    }
}