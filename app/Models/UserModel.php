<?php

namespace App\Models;
use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [ 
        'username', 
        'email', 
        'password',
        'role',
        'department_id',
        'profile_picture',
        'otp_code',
        'otp_expiry',
        'last_login',
        'status', 
        'created_at', 
    ];

    protected $useTimestamps = false; // Set to true if using automatic timestamps
}
