<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticesModel extends Model
{
    protected $table            = 'notices';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['title', 'content', 'department_id', 'files', 'is_active', 'notice_type', 'url_link'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Enhanced search for global search functionality
     */
    public function searchForGlobalSearch(string $query, int $limit = null): array
    {
        $builder = $this->select('notices.*, departments.department_name')
                        ->join('departments', 'notices.department_id = departments.id', 'left')
                        ->groupStart()
                            ->like('notices.title', $query)
                            ->orLike('notices.content', $query)
                            ->orLike('notices.notice_type', $query)
                            ->orLike('departments.department_name', $query)
                        ->groupEnd()
                        ->where('notices.is_active', 'active')
                        ->orderBy('notices.created_at', 'DESC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get recent notices
     */
    public function getRecentNotices(int $limit = 10): array
    {
        return $this->select('notices.*, departments.department_name')
                    ->join('departments', 'notices.department_id = departments.id', 'left')
                    ->where('notices.is_active', 'active')
                    ->orderBy('notices.created_at', 'DESC')
                    ->limit($limit)
                    ->findAll();
    }
}