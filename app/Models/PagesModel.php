<?php

namespace App\Models;

use CodeIgniter\Model;

class PagesModel extends Model
{
    protected $table = 'pages';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = ['pageName', 'pageData'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'pageName' => 'required|max_length[255]|is_unique[pages.pageName,id,{id}]',
        'pageData' => 'required'
    ];

    protected $validationMessages = [
        'pageName' => [
            'required' => 'Page name is required',
            'max_length' => 'Page name cannot exceed 255 characters',
            'is_unique' => 'Page name already exists'
        ],
        'pageData' => [
            'required' => 'Page data is required'
        ]
    ];

    /**
     * Get all pages with basic info
     */
    public function getAllPages()
    {
        return $this->select('id, pageName, created_at, updated_at')->findAll();
    }

    /**
     * Get page by name
     */
    public function getPageByName($pageName)
    {
        return $this->where('pageName', $pageName)->first();
    }

    /**
     * Create new page
     */
    public function createPage($data)
    {
        // Validate JSON structure
        if (isset($data['pageData']) && is_array($data['pageData'])) {
            $data['pageData'] = json_encode($data['pageData']);
        }

        return $this->insert($data);
    }

    /**
     * Update page
     */
    public function updatePage($id, $data)
    {
        // Validate JSON structure
        if (isset($data['pageData']) && is_array($data['pageData'])) {
            $data['pageData'] = json_encode($data['pageData']);
        }

        // Set validation rules for update (exclude current ID from unique check)
        $this->setValidationRules([
            'pageName' => "required|max_length[255]|is_unique[pages.pageName,id,{$id}]",
            'pageData' => 'required'
        ]);

        return $this->update($id, $data);
    }

    /**
     * Get page with decoded JSON data
     */
    public function getPageWithData($id)
    {
        $page = $this->find($id);
        if ($page && isset($page['pageData'])) {
            $page['pageData'] = json_decode($page['pageData'], true);
        }
        return $page;
    }

    /**
     * Get page by name with decoded JSON data
     */
    public function getPageByNameWithData($pageName)
    {
        $page = $this->getPageByName($pageName);
        if ($page && isset($page['pageData'])) {
            $page['pageData'] = json_decode($page['pageData'], true);
        }
        return $page;
    }

    /**
     * Enhanced search for global search functionality
     */
    public function searchForGlobalSearch(string $query, int $limit = null): array
    {
        $builder = $this->select('id, pageName, pageData, created_at, updated_at')
                        ->groupStart()
                            ->like('pageName', $query)
                            ->orLike('pageData', $query)
                        ->groupEnd()
                        ->orderBy('pageName', 'ASC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        $results = $builder->findAll();

        // Process results to extract text from JSON pageData for better search relevance
        return array_map(function($page) use ($query) {
            if (isset($page['pageData'])) {
                $pageData = json_decode($page['pageData'], true);
                if ($pageData) {
                    // Extract text content from JSON structure for search relevance
                    $page['content_preview'] = $this->extractTextFromPageData($pageData, 200);
                }
            }
            return $page;
        }, $results);
    }

    /**
     * Extract text content from page data JSON for search preview
     */
    private function extractTextFromPageData(array $pageData, int $maxLength = 200): string
    {
        $text = '';
        
        if (isset($pageData['sections']) && is_array($pageData['sections'])) {
            foreach ($pageData['sections'] as $section) {
                if (isset($section['content'])) {
                    if (is_string($section['content'])) {
                        $text .= strip_tags($section['content']) . ' ';
                    } elseif (is_array($section['content'])) {
                        $text .= $this->extractTextFromArray($section['content']) . ' ';
                    }
                }
                
                if (isset($section['title'])) {
                    $text .= strip_tags($section['title']) . ' ';
                }
                
                if (isset($section['description'])) {
                    $text .= strip_tags($section['description']) . ' ';
                }
            }
        }
        
        // Also check for direct content fields
        if (isset($pageData['content'])) {
            $text .= strip_tags($pageData['content']) . ' ';
        }
        
        if (isset($pageData['title'])) {
            $text .= strip_tags($pageData['title']) . ' ';
        }
        
        $text = trim($text);
        
        if (strlen($text) > $maxLength) {
            $text = substr($text, 0, $maxLength) . '...';
        }
        
        return $text;
    }

    /**
     * Recursively extract text from array structures
     */
    private function extractTextFromArray(array $data): string
    {
        $text = '';
        
        foreach ($data as $value) {
            if (is_string($value)) {
                $text .= strip_tags($value) . ' ';
            } elseif (is_array($value)) {
                $text .= $this->extractTextFromArray($value) . ' ';
            }
        }
        
        return $text;
    }

    /**
     * Check if a page is marked as permanent
     * 
     * @param int $id Page ID
     * @return bool True if page is permanent, false otherwise
     */
    public function isPagePermanent($id)
    {
        $page = $this->select('permanent')->find($id);
        
        if (!$page) {
            return false;
        }
        
        // Handle backward compatibility - treat null as false
        return (bool) ($page['permanent'] ?? false);
    }
}