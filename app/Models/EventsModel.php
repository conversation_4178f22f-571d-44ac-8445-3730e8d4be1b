<?php

namespace App\Models;

use CodeIgniter\Model;

class EventsModel extends Model
{
    protected $table            = 'events_upcoming';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['title', 'description', 'event_date', 'location', 'department_id', 'files', 'is_active'];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Validation
    protected $validationRules      = [];
    protected $validationMessages   = [];
    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = [];
    protected $afterInsert    = [];
    protected $beforeUpdate   = [];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Enhanced search for global search functionality
     */
    public function searchForGlobalSearch(string $query, int $limit = null): array
    {
        $builder = $this->select('events_upcoming.*, departments.department_name')
                        ->join('departments', 'events_upcoming.department_id = departments.id', 'left')
                        ->groupStart()
                            ->like('events_upcoming.title', $query)
                            ->orLike('events_upcoming.description', $query)
                            ->orLike('events_upcoming.location', $query)
                            ->orLike('departments.department_name', $query)
                        ->groupEnd()
                        ->where('events_upcoming.is_active', 'active')
                        ->orderBy('events_upcoming.event_date', 'DESC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get upcoming events
     */
    public function getUpcomingEvents(int $limit = null): array
    {
        $builder = $this->select('events_upcoming.*, departments.department_name')
                        ->join('departments', 'events_upcoming.department_id = departments.id', 'left')
                        ->where('events_upcoming.is_active', 'active')
                        ->where('events_upcoming.event_date >=', date('Y-m-d'))
                        ->orderBy('events_upcoming.event_date', 'ASC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }
}
