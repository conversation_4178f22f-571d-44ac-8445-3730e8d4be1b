<?php

namespace App\Models;

use CodeIgniter\Model;

class StudentsModel extends Model
{
    protected $table            = 'students';
    protected $primaryKey       = 'record_id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'full_name',
        'student_id',
        'email',
        'contact_number',
        'department_id',
        'program_type',
        'admission_year',
        'expected_completion_year',
        'actual_completion_year',
        'current_semester',
        'cgpa',
        'status',
        'photo'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'full_name' => 'required|max_length[100]',
        'student_id' => 'required|max_length[20]|is_unique[students.student_id,record_id,{record_id}]',
        'email' => 'required|max_length[100]|valid_email|is_unique[students.email,record_id,{record_id}]',
        'contact_number' => 'permit_empty|max_length[10]|numeric',
        'department_id' => 'required|integer',
        'program_type' => 'required|in_list[Bachelor\'s,Master\'s,MPhil,PhD]',
        'admission_year' => 'required|max_length[4]|numeric',
        'expected_completion_year' => 'permit_empty|max_length[4]|numeric',
        'actual_completion_year' => 'permit_empty|max_length[4]|numeric',
        'current_semester' => 'permit_empty|integer',
        'cgpa' => 'permit_empty|decimal',
        'status' => 'permit_empty|in_list[current,alumni,dropped]',
        'photo' => 'permit_empty|max_length[255]'
    ];

    protected $validationMessages = [
        'full_name' => [
            'required' => 'Full name is required',
            'max_length' => 'Full name cannot exceed 100 characters'
        ],
        'student_id' => [
            'required' => 'Student ID is required',
            'is_unique' => 'Student ID already exists'
        ],
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'Email already exists'
        ],
        'contact_number' => [
            'max_length' => 'Contact number cannot exceed 10 digits',
            'numeric' => 'Contact number must contain only numbers'
        ],
        'department_id' => [
            'required' => 'Department selection is required',
            'integer' => 'Department ID must be a valid integer'
        ],
        'program_type' => [
            'required' => 'Program type is required',
            'in_list' => 'Please select a valid program type'
        ],
        'admission_year' => [
            'required' => 'Admission year is required',
            'numeric' => 'Admission year must be a valid number'
        ],
        'expected_completion_year' => [
            'numeric' => 'Expected completion year must be a valid number'
        ],
        'actual_completion_year' => [
            'numeric' => 'Actual completion year must be a valid number'
        ],
        'current_semester' => [
            'integer' => 'Current semester must be a valid integer'
        ],
        'cgpa' => [
            'decimal' => 'CGPA must be a valid decimal number'
        ]
    ];

    protected $skipValidation       = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert   = ['setDefaultStatus'];
    protected $afterInsert    = [];
    protected $beforeUpdate   = ['updateStatusBasedOnCompletion'];
    protected $afterUpdate    = [];
    protected $beforeFind     = [];
    protected $afterFind      = [];
    protected $beforeDelete   = [];
    protected $afterDelete    = [];

    /**
     * Set default status before insert
     */
    protected function setDefaultStatus(array $data)
    {
        if (!isset($data['data']['status'])) {
            $data['data']['status'] = 'current';
        }
        return $data;
    }

    /**
     * Update status based on completion year before update
     */
    protected function updateStatusBasedOnCompletion(array $data)
    {
        if (isset($data['data']['actual_completion_year']) && !empty($data['data']['actual_completion_year'])) {
            $data['data']['status'] = 'alumni';
        } elseif (isset($data['data']['actual_completion_year']) && empty($data['data']['actual_completion_year'])) {
            $data['data']['status'] = 'current';
        }
        return $data;
    }

    /**
     * Get all students with department and faculty information
     */
    public function getAllStudents()
    {
        return $this->select('students.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'students.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->orderBy('students.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get student with department and faculty information
     */
    public function getStudentWithDetails($id)
    {
        return $this->select('students.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'students.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->find($id);
    }

    /**
     * Get current students (not graduated)
     */
    public function getCurrentStudents($departmentId = null)
    {
        $query = $this->select('students.*, departments.department_name, faculties.faculty_name')
                      ->join('departments', 'students.department_id = departments.id', 'left')
                      ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                      ->where('students.actual_completion_year IS NULL')
                      ->where('students.status', 'current');

        if ($departmentId) {
            $query->where('students.department_id', $departmentId);
        }

        return $query->orderBy('students.admission_year', 'DESC')
                     ->orderBy('students.full_name', 'ASC')
                     ->findAll();
    }

    /**
     * Get alumni (graduated students)
     */
    public function getAlumni($departmentId = null)
    {
        $query = $this->select('students.*, departments.department_name, faculties.faculty_name')
                      ->join('departments', 'students.department_id = departments.id', 'left')
                      ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                      ->where('students.actual_completion_year IS NOT NULL')
                      ->where('students.status', 'alumni');

        if ($departmentId) {
            $query->where('students.department_id', $departmentId);
        }

        return $query->orderBy('students.actual_completion_year', 'DESC')
                     ->orderBy('students.full_name', 'ASC')
                     ->findAll();
    }

    /**
     * Get students by department
     */
    public function getByDepartment($departmentId)
    {
        return $this->where('department_id', $departmentId)
                    ->orderBy('admission_year', 'DESC')
                    ->orderBy('full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get students by faculty (via departments)
     */
    public function getByFaculty($facultyId)
    {
        $db = \Config\Database::connect();
        return $db->table('students s')
                  ->select('s.*, d.department_name')
                  ->join('departments d', 's.department_id = d.id')
                  ->where('d.faculty_id', $facultyId)
                  ->orderBy('s.admission_year', 'DESC')
                  ->orderBy('s.full_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Get students by program type
     */
    public function getByProgramType($programType)
    {
        return $this->select('students.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'students.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->where('students.program_type', $programType)
                    ->orderBy('students.admission_year', 'DESC')
                    ->orderBy('students.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get students by admission year
     */
    public function getByAdmissionYear($year)
    {
        return $this->select('students.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'students.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->where('students.admission_year', $year)
                    ->orderBy('students.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get students by completion year (alumni)
     */
    public function getByCompletionYear($year)
    {
        return $this->select('students.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'students.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->where('students.actual_completion_year', $year)
                    ->where('students.status', 'alumni')
                    ->orderBy('students.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Search students
     */
    public function searchStudents($searchTerm)
    {
        return $this->select('students.*, departments.department_name, faculties.faculty_name')
                    ->join('departments', 'students.department_id = departments.id', 'left')
                    ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                    ->groupStart()
                        ->like('students.full_name', $searchTerm)
                        ->orLike('students.student_id', $searchTerm)
                        ->orLike('students.email', $searchTerm)
                        ->orLike('departments.department_name', $searchTerm)
                        ->orLike('faculties.faculty_name', $searchTerm)
                    ->groupEnd()
                    ->orderBy('students.full_name', 'ASC')
                    ->findAll();
    }

    /**
     * Enhanced search for global search functionality
     */
    public function searchForGlobalSearch(string $query, int $limit = null): array
    {
        $builder = $this->select('students.*, departments.department_name, faculties.faculty_name')
                        ->join('departments', 'students.department_id = departments.id', 'left')
                        ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                        ->groupStart()
                            ->like('students.full_name', $query)
                            ->orLike('students.student_id', $query)
                            ->orLike('students.email', $query)
                            ->orLike('students.program_type', $query)
                            ->orLike('departments.department_name', $query)
                            ->orLike('faculties.faculty_name', $query)
                        ->groupEnd()
                        ->orderBy('students.full_name', 'ASC');

        if ($limit !== null) {
            $builder->limit($limit);
        }

        return $builder->findAll();
    }

    /**
     * Get departments list for dropdown
     */
    public function getDepartmentsList()
    {
        $db = \Config\Database::connect();
        return $db->table('departments d')
                  ->select('d.id, d.department_name, f.faculty_name')
                  ->join('faculties f', 'd.faculty_id = f.id', 'left')
                  ->orderBy('f.faculty_name', 'ASC')
                  ->orderBy('d.department_name', 'ASC')
                  ->get()
                  ->getResultArray();
    }

    /**
     * Mark student as alumni
     */
    public function markAsAlumni($id, $completionYear = null)
    {
        if (!$completionYear) {
            $completionYear = date('Y');
        }

        return $this->update($id, [
            'actual_completion_year' => $completionYear,
            'status' => 'alumni'
        ]);
    }

    /**
     * Mark student as current (remove alumni status)
     */
    public function markAsCurrent($id)
    {
        return $this->update($id, [
            'actual_completion_year' => null,
            'status' => 'current'
        ]);
    }

    /**
     * Mark student as dropped
     */
    public function markAsDropped($id)
    {
        return $this->update($id, ['status' => 'dropped']);
    }

    /**
     * Create student with validation
     */
    public function createStudent($data)
    {
        // Validate department exists
        $departmentsModel = new \App\Models\DepartmentsModel();
        if (!$departmentsModel->find($data['department_id'])) {
            return false;
        }

        // Validate completion year is after admission year if provided
        if (isset($data['expected_completion_year']) && !empty($data['expected_completion_year'])) {
            if ($data['expected_completion_year'] <= $data['admission_year']) {
                return false;
            }
        }

        if (isset($data['actual_completion_year']) && !empty($data['actual_completion_year'])) {
            if ($data['actual_completion_year'] < $data['admission_year']) {
                return false;
            }
        }

        return $this->insert($data);
    }

    /**
     * Update student with validation
     */
    public function updateStudent($id, $data)
    {
        // Validate department exists if department_id is being updated
        if (isset($data['department_id'])) {
            $departmentsModel = new \App\Models\DepartmentsModel();
            if (!$departmentsModel->find($data['department_id'])) {
                return false;
            }
        }

        // Get current student data for validation
        $currentStudent = $this->find($id);
        if (!$currentStudent) {
            return false;
        }

        $admissionYear = isset($data['admission_year']) ? $data['admission_year'] : $currentStudent['admission_year'];

        // Validate completion years
        if (isset($data['expected_completion_year']) && !empty($data['expected_completion_year'])) {
            if ($data['expected_completion_year'] <= $admissionYear) {
                return false;
            }
        }

        if (isset($data['actual_completion_year']) && !empty($data['actual_completion_year'])) {
            if ($data['actual_completion_year'] < $admissionYear) {
                return false;
            }
        }

        // Set validation rules for update (exclude current ID from unique checks)
        $this->setValidationRules([
            'full_name' => 'required|max_length[100]',
            'student_id' => "required|max_length[20]|is_unique[students.student_id,record_id,{$id}]",
            'email' => "required|max_length[100]|valid_email|is_unique[students.email,record_id,{$id}]",
            'contact_number' => 'permit_empty|max_length[10]|numeric',
            'department_id' => 'required|integer',
            'program_type' => 'required|in_list[Bachelor\'s,Master\'s,MPhil,PhD]',
            'admission_year' => 'required|max_length[4]|numeric',
            'expected_completion_year' => 'permit_empty|max_length[4]|numeric',
            'actual_completion_year' => 'permit_empty|max_length[4]|numeric',
            'current_semester' => 'permit_empty|integer',
            'cgpa' => 'permit_empty|decimal',
            'status' => 'permit_empty|in_list[current,alumni,dropped]',
            'photo' => 'permit_empty|max_length[255]'
        ]);

        return $this->update($id, $data);
    }

    /**
     * Get student statistics
     */
    public function getStatistics()
    {
        $db = \Config\Database::connect();
        
        $stats = [];

        // Total students
        $stats['total_students'] = $this->countAllResults();

        // By status
        $statuses = ['current', 'alumni', 'dropped'];
        foreach ($statuses as $status) {
            $stats['by_status'][$status] = $this->where('status', $status)->countAllResults();
        }

        // By program type
        $programTypes = ['Bachelor\'s', 'Master\'s', 'MPhil', 'PhD'];
        foreach ($programTypes as $programType) {
            $stats['by_program'][$programType] = $this->where('program_type', $programType)->countAllResults();
        }

        // By faculty
        $facultyStats = $db->table('students s')
                           ->select('f.faculty_name, COUNT(*) as count')
                           ->join('departments d', 's.department_id = d.id')
                           ->join('faculties f', 'd.faculty_id = f.id')
                           ->groupBy('f.id, f.faculty_name')
                           ->orderBy('count', 'DESC')
                           ->get()
                           ->getResultArray();

        $stats['by_faculty'] = $facultyStats;

        // Recent admissions (last 5 years)
        $currentYear = date('Y');
        for ($i = 0; $i < 5; $i++) {
            $year = $currentYear - $i;
            $stats['recent_admissions'][$year] = $this->where('admission_year', $year)->countAllResults();
        }

        // Recent graduations (last 5 years)
        for ($i = 0; $i < 5; $i++) {
            $year = $currentYear - $i;
            $stats['recent_graduations'][$year] = $this->where('actual_completion_year', $year)
                                                       ->where('status', 'alumni')
                                                       ->countAllResults();
        }

        return $stats;
    }

    /**
     * Get academic performance statistics
     */
    public function getAcademicStats()
    {
        $db = \Config\Database::connect();
        
        $stats = [];

        // Average CGPA by program type
        $programTypes = ['Bachelor\'s', 'Master\'s', 'MPhil', 'PhD'];
        foreach ($programTypes as $programType) {
            $result = $db->table('students')
                         ->selectAvg('cgpa')
                         ->where('program_type', $programType)
                         ->where('cgpa IS NOT NULL')
                         ->get()
                         ->getRowArray();
            
            $stats['avg_cgpa_by_program'][$programType] = round($result['cgpa'] ?? 0, 2);
        }

        // CGPA distribution
        $cgpaRanges = [
            '3.5-4.0' => ['min' => 3.5, 'max' => 4.0],
            '3.0-3.49' => ['min' => 3.0, 'max' => 3.49],
            '2.5-2.99' => ['min' => 2.5, 'max' => 2.99],
            '2.0-2.49' => ['min' => 2.0, 'max' => 2.49],
            'Below 2.0' => ['min' => 0, 'max' => 1.99]
        ];

        foreach ($cgpaRanges as $range => $values) {
            $stats['cgpa_distribution'][$range] = $db->table('students')
                                                     ->where('cgpa >=', $values['min'])
                                                     ->where('cgpa <=', $values['max'])
                                                     ->countAllResults();
        }

        return $stats;
    }
}