<?php namespace Config;

class Sidebar
{
    public static function get()
    {
        return [

            [
                'header' => 'MAIN NAVIGATION',
                'items' => [
                    [
                        'label' => 'Dashboard',
                        'icon' => 'zmdi zmdi-view-dashboard',
                        'url' => '/dashboard',
                        'permission' => 'view_dashboard',
                    ]
                ]
            ],

            [
                'label' => 'Departments',
                'icon' => 'zmdi zmdi-city',
                'items' => [
                    ['label' => 'List Departments', 'url' => '/dashboard/listDepartments', 'permission' => 'view_departments'],
                    ['label' => 'Add Department', 'url' => '/dashboard/addDepartments', 'permission' => 'add_department']
                ]
            ],

            [
                'label' => 'Users',
                'icon' => 'zmdi zmdi-accounts',
                'items' => [
                    ['label' => 'List Users', 'url' => '/dashboard/listUsers', 'permission' => 'view_users'],
                    ['label' => 'Add User', 'url' => '/dashboard/addUsers', 'permission' => 'add_user'],
                    ['label' => 'Roles & Permissions', 'url' => '/dashboard/rolePermissionUsers', 'permission' => 'manage_roles']
                ]
            ],

            [
                'label' => 'Content Management',
                'icon' => 'zmdi zmdi-collection-folder-image',
                'items' => [
                    ['label' => 'Manage Sections', 'url' => '/dashboard/manageSections', 'permission' => 'manage_sections'],
                    ['label' => 'Add New Section', 'url' => '/dashboard/addSections', 'permission' => 'add_section']
                ]
            ],

            [
                'label' => 'Academics',
                'icon' => 'zmdi zmdi-graduation-cap',
                'items' => [
                    ['label' => 'Academic Programmes', 'url' => '/dashboard/academicProgrammes', 'permission' => 'view_programmes'],
                    ['label' => 'Syllabus Upload', 'url' => '/dashboard/syllabusUpload', 'permission' => 'upload_syllabus'],
                    ['label' => 'Fee Structure', 'url' => '/dashboard/feeStructure', 'permission' => 'view_fee_structure'],
                    ['label' => 'Academic Calendar', 'url' => '/dashboard/academicCalendar', 'permission' => 'view_academic_calendar'],
                    ['label' => 'Regulations & Guidelines', 'url' => '/dashboard/regulationsGuidelines', 'permission' => 'view_regulations']
                ]
            ],

            [
                'label' => 'Faculty',
                'icon' => 'zmdi zmdi-accounts-list',
                'items' => [
                    ['label' => 'Add Faculty', 'url' => '/dashboard/addFaculty', 'permission' => 'add_faculty'],
                    ['label' => 'List Faculty', 'url' => '/dashboard/listFaculty', 'permission' => 'view_faculty']
                ]
            ],

            [
                'label' => 'Admissions',
                'icon' => 'zmdi zmdi-assignment-check',
                'items' => [
                    ['label' => 'Prospectus Upload', 'url' => '/dashboard/prospectusUpload', 'permission' => 'upload_prospectus'],
                    ['label' => 'Admission Guidelines', 'url' => '/dashboard/admissionGuidelines', 'permission' => 'view_admission_guidelines'],
                    ['label' => 'Fee Refund Policy', 'url' => '/dashboard/feeRefundPolicy', 'permission' => 'view_refund_policy']
                ]
            ],

            [
                'label' => 'Research',
                'icon' => 'zmdi zmdi-lamp',
                'items' => [
                    ['label' => 'R&D Cell', 'url' => '/dashboard/research', 'permission' => 'view_research'],
                    ['label' => 'Ongoing Projects', 'url' => '/dashboard/ongoingProjects', 'permission' => 'view_ongoing_projects']
                ]
            ],

            [
                'label' => 'Facilities',
                'icon' => 'zmdi zmdi-home',
                'items' => [
                    ['label' => 'Hostel Info', 'url' => '/dashboard/hostelInfo', 'permission' => 'view_hostel'],
                    ['label' => 'Scholarship Info', 'url' => '/dashboard/scholarshipInfo', 'permission' => 'view_scholarship'],
                    ['label' => 'Central Library', 'url' => '/dashboard/centralLibrary', 'permission' => 'view_library'],
                    ['label' => 'Sports, Labs, AV Centre', 'url' => '/dashboard/sportsLabsAVCentre', 'permission' => 'view_sports_labs']
                ]
            ],

            [
                'label' => 'Notices & Updates',
                'icon' => 'zmdi zmdi-notifications',
                'items' => [
                    ['label' => 'Add Notice', 'url' => '/dashboard/addNotice', 'permission' => 'add_notice'],
                    ['label' => 'Manage Notices', 'url' => '/dashboard/manageNotices', 'permission' => 'manage_notices'],
                    ['label' => 'Results Upload', 'url' => '/dashboard/resultsUpload', 'permission' => 'upload_results'],
                    ['label' => 'Event Archive', 'url' => '/dashboard/eventArchive', 'permission' => 'view_event_archive']
                ]
            ],

            [
                'label' => 'Media & Gallery',
                'icon' => 'zmdi zmdi-collection-image',
                'items' => [
                    ['label' => 'Upload Images', 'url' => '/dashboard/uploadImages', 'permission' => 'upload_images'],
                    ['label' => 'Manage Gallery', 'url' => '/dashboard/manageGallery', 'permission' => 'manage_gallery']
                ]
            ],

            [
                'label' => 'Feedback & Forms',
                'icon' => 'zmdi zmdi-comment-text',
                'items' => [
                    ['label' => 'View Feedback', 'url' => '/dashboard/viewFeedback', 'permission' => 'view_feedback'],
                    ['label' => 'Download Forms', 'url' => '/dashboard/downloadForms', 'permission' => 'download_forms']
                ]
            ],

            [
                'label' => 'Security & Logs',
                'icon' => 'zmdi zmdi-lock',
                'items' => [
                    ['label' => 'Login History', 'url' => '/dashboard/loginHistory', 'permission' => 'view_login_history'],
                    ['label' => 'User Activity Logs', 'url' => '/dashboard/userActivityLogs', 'permission' => 'view_user_logs'],
                    ['label' => 'Enable 2FA/OTP', 'url' => '/dashboard/enable2FA', 'permission' => 'enable_2fa'],
                    ['label' => 'Audit Logs', 'url' => '/dashboard/auditLogs', 'permission' => 'view_audit_logs']
                ]
            ],

            [
                'label' => 'Settings',
                'icon' => 'zmdi zmdi-settings',
                'items' => [
                    ['label' => 'University Info', 'url' => '/dashboard/universityInfo', 'permission' => 'view_university_info'],
                    ['label' => 'Logo & Favicon', 'url' => '/dashboard/logoFavicon', 'permission' => 'update_branding'],
                    ['label' => 'SMTP Settings', 'url' => '/dashboard/smtpSettings', 'permission' => 'manage_smtp'],
                    ['label' => 'Social Media Links', 'url' => '/dashboard/socialMediaLinks', 'permission' => 'manage_social_links'],
                    ['label' => 'Backup & Exports', 'url' => '/dashboard/backupExports', 'permission' => 'manage_backup']
                ]
            ],

            [
                'header' => 'Help & Manual',
                'items' => [
                    ['label' => 'Admin Manual (PDF)', 'url' => 'javascript:void();', 'icon' => 'zmdi zmdi-coffee text-danger', 'permission' => 'view_help'],
                    ['label' => 'How to Add Pages', 'url' => 'javascript:void();', 'icon' => 'zmdi zmdi-chart-donut text-success', 'permission' => 'view_help'],
                    ['label' => 'System Usage Guide', 'url' => 'javascript:void();', 'icon' => 'zmdi zmdi-share text-info', 'permission' => 'view_help']
                ]
            ]
        ];
    }
}
