<?php

namespace App\Services;

use App\Models\FacultyModel;
use App\Models\DepartmentsModel;
use App\Models\FacultyMembersModel;
use App\Models\StudentsModel;

class NavigationService
{
    protected $facultyModel;
    protected $departmentsModel;
    protected $facultyMembersModel;
    protected $studentsModel;
    protected $cache;

    public function __construct()
    {
        $this->facultyModel = new FacultyModel();
        $this->departmentsModel = new DepartmentsModel();
        $this->facultyMembersModel = new FacultyMembersModel();
        $this->studentsModel = new StudentsModel();
        $this->cache = \Config\Services::cache();
    }

    /**
     * Build complete faculty hierarchy with departments
     */
    public function buildFacultyHierarchy()
    {
        try {
            $cacheKey = 'navigation_faculty_hierarchy';
            $cached = $this->cache->get($cacheKey);
            
            if ($cached !== null) {
                return $cached;
            }

            // Get all faculties first (simple query)
            $faculties = $this->facultyModel->select('id, faculty_name, faculty_description, dean_id')
                                           ->orderBy('faculty_name', 'ASC')
                                           ->findAll();

            if (empty($faculties)) {
                // Return empty structure if no faculties
                return [
                    'faculties' => [],
                    'meta' => [
                        'total_faculties' => 0,
                        'total_departments' => 0,
                        'cached' => false,
                        'cache_expires' => date('c', time() + 3600)
                    ]
                ];
            }

            $hierarchyData = [];

            foreach ($faculties as $faculty) {
                // Get dean information if exists
                $deanInfo = null;
                if (!empty($faculty['dean_id'])) {
                    $dean = $this->facultyMembersModel->select('full_name, designation')
                                                     ->where('record_id', $faculty['dean_id'])
                                                     ->first();
                    if ($dean) {
                        $deanInfo = [
                            'name' => $dean['full_name'],
                            'designation' => $dean['designation']
                        ];
                    }
                }

                // Get departments for this faculty
                $departments = $this->departmentsModel->select('id, department_name, department_description, head_of_department_id')
                                                     ->where('faculty_id', $faculty['id'])
                                                     ->orderBy('department_name', 'ASC')
                                                     ->findAll();

                $departmentData = [];
                $facultyStats = [
                    'total_departments' => 0,
                    'total_faculty_members' => 0,
                    'total_current_students' => 0,
                    'total_alumni' => 0
                ];

                foreach ($departments as $department) {
                    // Get head information if exists
                    $headInfo = null;
                    if (!empty($department['head_of_department_id'])) {
                        $head = $this->facultyMembersModel->select('full_name, designation')
                                                         ->where('record_id', $department['head_of_department_id'])
                                                         ->first();
                        if ($head) {
                            $headInfo = [
                                'name' => $head['full_name'],
                                'designation' => $head['designation']
                            ];
                        }
                    }

                    // Get basic stats for each department
                    $deptStats = $this->getDepartmentBasicStats($department['id']);
                    
                    $departmentData[] = [
                        'id' => $department['id'],
                        'name' => $department['department_name'],
                        'slug' => $this->generateSlug($department['department_name']),
                        'description' => $department['department_description'],
                        'head' => $headInfo,
                        'stats' => $deptStats
                    ];

                    // Aggregate faculty stats
                    $facultyStats['total_departments']++;
                    $facultyStats['total_faculty_members'] += $deptStats['faculty_members'];
                    $facultyStats['total_current_students'] += $deptStats['current_students'];
                    $facultyStats['total_alumni'] += $deptStats['alumni'];
                }

                $hierarchyData[] = [
                    'id' => $faculty['id'],
                    'name' => $faculty['faculty_name'],
                    'slug' => $this->generateSlug($faculty['faculty_name']),
                    'description' => $faculty['faculty_description'],
                    'dean' => $deanInfo,
                    'departments' => $departmentData,
                    'stats' => $facultyStats
                ];
            }

            $totalDepartments = array_sum(array_column(array_column($hierarchyData, 'stats'), 'total_departments'));

            $result = [
                'faculties' => $hierarchyData,
                'meta' => [
                    'total_faculties' => count($hierarchyData),
                    'total_departments' => $totalDepartments,
                    'cached' => false,
                    'cache_expires' => date('c', time() + 3600)
                ]
            ];

            // Cache for 1 hour
            $this->cache->save($cacheKey, $result, 3600);
            
            return $result;

        } catch (\Exception $e) {
            // Log the specific error for debugging
            log_message('error', 'NavigationService::buildFacultyHierarchy error: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine());
            throw $e;
        }
    }

    /**
     * Get basic statistics for a department
     */
    protected function getDepartmentBasicStats($departmentId)
    {
        try {
            $facultyMembersCount = $this->facultyMembersModel->where('department_id', $departmentId)
                                                             ->where('status', 'active')
                                                             ->countAllResults();

            $currentStudentsCount = $this->studentsModel->where('department_id', $departmentId)
                                                        ->where('status', 'current')
                                                        ->countAllResults();

            $alumniCount = $this->studentsModel->where('department_id', $departmentId)
                                               ->where('status', 'alumni')
                                               ->countAllResults();

            return [
                'faculty_members' => $facultyMembersCount ?? 0,
                'current_students' => $currentStudentsCount ?? 0,
                'alumni' => $alumniCount ?? 0
            ];
        } catch (\Exception $e) {
            log_message('error', 'getDepartmentBasicStats error for department ' . $departmentId . ': ' . $e->getMessage());
            
            // Return zero stats if there's an error
            return [
                'faculty_members' => 0,
                'current_students' => 0,
                'alumni' => 0
            ];
        }
    }

    /**
     * Generate URL-safe slug from name
     */
    public function generateSlug($name)
    {
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name), '-'));
    }

    /**
     * Cache data with specified duration
     */
    public function cacheData($key, $data, $duration = 3600)
    {
        return $this->cache->save($key, $data, $duration);
    }

    /**
     * Get cached data
     */
    public function getCachedData($key)
    {
        return $this->cache->get($key);
    }

    /**
     * Clear cache by key
     */
    public function clearCache($key)
    {
        return $this->cache->delete($key);
    }

    /**
     * Clear all navigation caches
     */
    public function clearAllNavigationCaches()
    {
        $keys = [
            'navigation_faculty_hierarchy',
            'navigation_university_stats'
        ];

        foreach ($keys as $key) {
            $this->cache->delete($key);
        }

        // Clear faculty and department specific caches
        $this->clearCachePattern('navigation_faculty_*');
        $this->clearCachePattern('navigation_department_*');
    }

    /**
     * Aggregate complete faculty data with all related entities
     */
    public function aggregateFacultyData($facultyId)
    {
        // Get faculty with dean information
        $faculty = $this->facultyModel->getFacultyWithDean($facultyId);
        
        if (!$faculty) {
            return null;
        }

        // Add slug to faculty data
        $faculty['slug'] = $this->generateSlug($faculty['faculty_name']);

        // Get all departments in this faculty
        $departments = $this->facultyModel->getDepartments($facultyId);
        
        // Process departments with head information
        $departmentData = [];
        foreach ($departments as $department) {
            $deptWithHead = $this->departmentsModel->getDepartmentWithHead($department['id']);
            $deptStats = $this->getDepartmentBasicStats($department['id']);
            
            $departmentData[] = [
                'id' => $deptWithHead['id'],
                'name' => $deptWithHead['department_name'],
                'slug' => $this->generateSlug($deptWithHead['department_name']),
                'description' => $deptWithHead['department_description'],
                'head' => $deptWithHead['head_name'] ? [
                    'name' => $deptWithHead['head_name'],
                    'designation' => $deptWithHead['head_designation']
                ] : null,
                'stats' => $deptStats
            ];
        }

        // Get all faculty members from this faculty
        $facultyMembers = $this->facultyModel->getFacultyMembers($facultyId);
        
        // Process faculty members data
        $facultyMembersData = [];
        foreach ($facultyMembers as $member) {
            $facultyMembersData[] = [
                'id' => $member['record_id'],
                'name' => $member['full_name'],
                'designation' => $member['designation'],
                'department' => $member['department_name'],
                'email' => $member['email'],
                'qualification' => $member['highest_qualification'],
                'discipline' => $member['discipline'],
                'photo' => $member['photo'],
                'bio' => $member['bio']
            ];
        }

        // Get PhD awardees
        $phdAwardees = $this->facultyModel->getPhdAwardees($facultyId);
        $phdData = [];
        foreach ($phdAwardees as $phd) {
            $phdData[] = [
                'id' => $phd['record_id'],
                'name' => $phd['full_name'],
                'department' => $phd['department_name'],
                'qualification' => $phd['highest_qualification'],
                'discipline' => $phd['discipline']
            ];
        }

        // Get current students
        $currentStudents = $this->facultyModel->getCurrentStudents($facultyId);
        $currentStudentsData = [];
        foreach ($currentStudents as $student) {
            $currentStudentsData[] = [
                'id' => $student['record_id'],
                'name' => $student['full_name'],
                'student_id' => $student['student_id'],
                'department' => $student['department_name'],
                'program_type' => $student['program_type'],
                'admission_year' => $student['admission_year'],
                'current_semester' => $student['current_semester']
            ];
        }

        // Get alumni
        $alumni = $this->facultyModel->getAlumni($facultyId);
        $alumniData = [];
        foreach ($alumni as $alum) {
            $alumniData[] = [
                'id' => $alum['record_id'],
                'name' => $alum['full_name'],
                'student_id' => $alum['student_id'],
                'department' => $alum['department_name'],
                'program_type' => $alum['program_type'],
                'completion_year' => $alum['actual_completion_year']
            ];
        }

        // Get news and events
        $newsEvents = $this->facultyModel->getFacultyNews($facultyId);
        $newsEventsData = [];
        foreach ($newsEvents as $news) {
            $newsEventsData[] = [
                'id' => $news['id'],
                'title' => $news['event_title'],
                'description' => $news['event_description'],
                'department' => $news['department_name'],
                'date' => $news['event_date'],
                'type' => 'event'
            ];
        }

        // Calculate statistics
        $statistics = [
            'total_departments' => count($departmentData),
            'total_faculty_members' => count($facultyMembersData),
            'total_phd_awardees' => count($phdData),
            'total_current_students' => count($currentStudentsData),
            'total_alumni' => count($alumniData),
            'total_news_events' => count($newsEventsData)
        ];

        return [
            'faculty' => $faculty,
            'departments' => $departmentData,
            'faculty_members' => $facultyMembersData,
            'current_students' => $currentStudentsData,
            'alumni' => $alumniData,
            'phd_awardees' => $phdData,
            'news_events' => $newsEventsData,
            'statistics' => $statistics
        ];
    }

    /**
     * Aggregate complete department data with all related entities
     */
    public function aggregateDepartmentData($departmentId)
    {
        // Get department with head and faculty information
        $department = $this->departmentsModel->getDepartmentWithHead($departmentId);
        
        if (!$department) {
            return null;
        }

        // Add slug to department data
        $department['slug'] = $this->generateSlug($department['department_name']);

        // Get all faculty members from this department
        $facultyMembers = $this->departmentsModel->getFacultyMembers($departmentId);
        
        // Process faculty members data
        $facultyMembersData = [];
        foreach ($facultyMembers as $member) {
            $facultyMembersData[] = [
                'id' => $member['record_id'],
                'name' => $member['full_name'],
                'designation' => $member['designation'],
                'email' => $member['email'],
                'qualification' => $member['highest_qualification'],
                'discipline' => $member['discipline'],
                'photo' => $member['photo'],
                'bio' => $member['bio']
            ];
        }

        // Get PhD awardees from faculty members
        $phdAwardees = array_filter($facultyMembers, function($member) {
            return $member['highest_qualification'] === 'PhD';
        });
        
        $phdData = [];
        foreach ($phdAwardees as $phd) {
            $phdData[] = [
                'id' => $phd['record_id'],
                'name' => $phd['full_name'],
                'qualification' => $phd['highest_qualification'],
                'discipline' => $phd['discipline']
            ];
        }

        // Get current students
        $currentStudents = $this->departmentsModel->getCurrentStudents($departmentId);
        $currentStudentsData = [];
        foreach ($currentStudents as $student) {
            $currentStudentsData[] = [
                'id' => $student['record_id'],
                'name' => $student['full_name'],
                'student_id' => $student['student_id'],
                'program_type' => $student['program_type'],
                'admission_year' => $student['admission_year'],
                'current_semester' => $student['current_semester']
            ];
        }

        // Get alumni
        $alumni = $this->departmentsModel->getAlumni($departmentId);
        $alumniData = [];
        foreach ($alumni as $alum) {
            $alumniData[] = [
                'id' => $alum['record_id'],
                'name' => $alum['full_name'],
                'student_id' => $alum['student_id'],
                'program_type' => $alum['program_type'],
                'completion_year' => $alum['actual_completion_year']
            ];
        }

        // Get department news and events
        $newsEvents = $this->departmentsModel->getDepartmentNews($departmentId);
        $newsEventsData = [];
        foreach ($newsEvents as $news) {
            $newsEventsData[] = [
                'id' => $news['id'],
                'title' => $news['event_title'],
                'description' => $news['event_description'],
                'date' => $news['event_date'],
                'type' => 'event'
            ];
        }

        // Calculate statistics
        $statistics = [
            'total_faculty_members' => count($facultyMembersData),
            'total_phd_awardees' => count($phdData),
            'total_current_students' => count($currentStudentsData),
            'total_alumni' => count($alumniData),
            'total_news_events' => count($newsEventsData)
        ];

        return [
            'department' => $department,
            'faculty_members' => $facultyMembersData,
            'current_students' => $currentStudentsData,
            'alumni' => $alumniData,
            'phd_awardees' => $phdData,
            'news_events' => $newsEventsData,
            'statistics' => $statistics
        ];
    }

    /**
     * Calculate comprehensive university statistics
     */
    public function calculateUniversityStatistics()
    {
        // Overview statistics
        $totalFaculties = $this->facultyModel->countAllResults();
        $totalDepartments = $this->departmentsModel->countAllResults();
        $totalFacultyMembers = $this->facultyMembersModel->where('status', 'active')->countAllResults();
        $totalCurrentStudents = $this->studentsModel->where('status', 'current')->countAllResults();
        $totalAlumni = $this->studentsModel->where('status', 'alumni')->countAllResults();

        // Academic statistics
        $phdHolders = $this->facultyMembersModel->where('highest_qualification', 'PhD')
                                               ->where('status', 'active')
                                               ->countAllResults();

        // Faculty members by designation
        $professors = $this->facultyMembersModel->where('designation', 'Professor')
                                                ->where('status', 'active')
                                                ->countAllResults();
        
        $associateProfessors = $this->facultyMembersModel->where('designation', 'Associate Professor')
                                                         ->where('status', 'active')
                                                         ->countAllResults();
        
        $assistantProfessors = $this->facultyMembersModel->where('designation', 'Assistant Professor')
                                                         ->where('status', 'active')
                                                         ->countAllResults();

        // Students by program type
        $bachelorsStudents = $this->studentsModel->where('program_type', "Bachelor's")->countAllResults();
        $mastersStudents = $this->studentsModel->where('program_type', "Master's")->countAllResults();
        $mphilStudents = $this->studentsModel->where('program_type', 'MPhil')->countAllResults();
        $phdStudents = $this->studentsModel->where('program_type', 'PhD')->countAllResults();

        // Faculty-wise breakdown
        $facultyBreakdown = [];
        $faculties = $this->facultyModel->select('id, faculty_name')->findAll();
        
        foreach ($faculties as $faculty) {
            $deptCount = $this->departmentsModel->where('faculty_id', $faculty['id'])->countAllResults();
            
            // Get faculty members count via departments
            $db = \Config\Database::connect();
            $facultyMembersCount = $db->table('faculty_members fm')
                                     ->join('departments d', 'fm.department_id = d.id')
                                     ->where('d.faculty_id', $faculty['id'])
                                     ->where('fm.status', 'active')
                                     ->countAllResults();

            $currentStudentsCount = $db->table('students s')
                                      ->join('departments d', 's.department_id = d.id')
                                      ->where('d.faculty_id', $faculty['id'])
                                      ->where('s.status', 'current')
                                      ->countAllResults();

            $alumniCount = $db->table('students s')
                             ->join('departments d', 's.department_id = d.id')
                             ->where('d.faculty_id', $faculty['id'])
                             ->where('s.status', 'alumni')
                             ->countAllResults();

            $facultyBreakdown[] = [
                'faculty_name' => $faculty['faculty_name'],
                'departments' => $deptCount,
                'faculty_members' => $facultyMembersCount,
                'current_students' => $currentStudentsCount,
                'alumni' => $alumniCount
            ];
        }

        return [
            'overview' => [
                'total_faculties' => $totalFaculties,
                'total_departments' => $totalDepartments,
                'total_faculty_members' => $totalFacultyMembers,
                'total_current_students' => $totalCurrentStudents,
                'total_alumni' => $totalAlumni
            ],
            'academic' => [
                'phd_holders' => $phdHolders,
                'professors' => $professors,
                'associate_professors' => $associateProfessors,
                'assistant_professors' => $assistantProfessors,
                'program_distribution' => [
                    'bachelors' => $bachelorsStudents,
                    'masters' => $mastersStudents,
                    'mphil' => $mphilStudents,
                    'phd' => $phdStudents
                ]
            ],
            'by_faculty' => $facultyBreakdown
        ];
    }

    /**
     * Get filtered faculty members with pagination
     */
    public function getFilteredFacultyMembers($filters, $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $builder = $this->facultyMembersModel->select('faculty_members.*, departments.department_name, faculties.faculty_name')
                                            ->join('departments', 'faculty_members.department_id = departments.id', 'left')
                                            ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                                            ->where('faculty_members.status', 'active');

        // Apply filters
        if (!empty($filters['faculty_id'])) {
            $builder->where('faculties.id', $filters['faculty_id']);
        }

        if (!empty($filters['department_id'])) {
            $builder->where('departments.id', $filters['department_id']);
        }

        if (!empty($filters['designation'])) {
            $builder->where('faculty_members.designation', $filters['designation']);
        }

        if (!empty($filters['qualification'])) {
            $builder->where('faculty_members.highest_qualification', $filters['qualification']);
        }

        // Get total count for pagination
        $total = $builder->countAllResults(false);

        // Get paginated results
        $results = $builder->orderBy('faculty_members.full_name', 'ASC')
                          ->limit($limit, $offset)
                          ->findAll();

        // Format results
        $formattedResults = [];
        foreach ($results as $member) {
            $formattedResults[] = [
                'id' => $member['record_id'],
                'name' => $member['full_name'],
                'designation' => $member['designation'],
                'department' => $member['department_name'],
                'faculty' => $member['faculty_name'],
                'email' => $member['email'],
                'qualification' => $member['highest_qualification'],
                'discipline' => $member['discipline'],
                'photo' => $member['photo']
            ];
        }

        return [
            'data' => $formattedResults,
            'total' => $total
        ];
    }

    /**
     * Get filtered students with pagination
     */
    public function getFilteredStudents($filters, $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $builder = $this->studentsModel->select('students.*, departments.department_name, faculties.faculty_name')
                                      ->join('departments', 'students.department_id = departments.id', 'left')
                                      ->join('faculties', 'departments.faculty_id = faculties.id', 'left');

        // Apply filters
        if (!empty($filters['faculty_id'])) {
            $builder->where('faculties.id', $filters['faculty_id']);
        }

        if (!empty($filters['department_id'])) {
            $builder->where('departments.id', $filters['department_id']);
        }

        if (!empty($filters['program_type'])) {
            $builder->where('students.program_type', $filters['program_type']);
        }

        if (!empty($filters['status'])) {
            $builder->where('students.status', $filters['status']);
        }

        if (!empty($filters['admission_year'])) {
            $builder->where('students.admission_year', $filters['admission_year']);
        }

        // Get total count for pagination
        $total = $builder->countAllResults(false);

        // Get paginated results
        $results = $builder->orderBy('students.full_name', 'ASC')
                          ->limit($limit, $offset)
                          ->findAll();

        // Format results
        $formattedResults = [];
        foreach ($results as $student) {
            $formattedResults[] = [
                'id' => $student['record_id'],
                'name' => $student['full_name'],
                'student_id' => $student['student_id'],
                'department' => $student['department_name'],
                'faculty' => $student['faculty_name'],
                'program_type' => $student['program_type'],
                'admission_year' => $student['admission_year'],
                'status' => $student['status'],
                'completion_year' => $student['actual_completion_year']
            ];
        }

        return [
            'data' => $formattedResults,
            'total' => $total
        ];
    }

    /**
     * Get filtered departments with pagination
     */
    public function getFilteredDepartments($filters, $page = 1, $limit = 20)
    {
        $offset = ($page - 1) * $limit;
        
        $builder = $this->departmentsModel->select('departments.*, faculties.faculty_name, faculty_members.full_name as head_name')
                                         ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                                         ->join('faculty_members', 'departments.head_of_department_id = faculty_members.record_id', 'left');

        // Apply filters
        if (!empty($filters['faculty_id'])) {
            $builder->where('faculties.id', $filters['faculty_id']);
        }

        // Get total count for pagination
        $total = $builder->countAllResults(false);

        // Get paginated results
        $results = $builder->orderBy('faculties.faculty_name', 'ASC')
                          ->orderBy('departments.department_name', 'ASC')
                          ->limit($limit, $offset)
                          ->findAll();

        // Format results
        $formattedResults = [];
        foreach ($results as $department) {
            $stats = $this->getDepartmentBasicStats($department['id']);
            
            $formattedResults[] = [
                'id' => $department['id'],
                'name' => $department['department_name'],
                'slug' => $this->generateSlug($department['department_name']),
                'description' => $department['department_description'],
                'faculty' => $department['faculty_name'],
                'head' => $department['head_name'],
                'stats' => $stats
            ];
        }

        return [
            'data' => $formattedResults,
            'total' => $total
        ];
    }

    /**
     * Clear cache by pattern (basic implementation)
     */
    protected function clearCachePattern($pattern)
    {
        // This is a basic implementation - in production you might want to use Redis or Memcached pattern matching
        $keys = [];
        
        // Get all faculties and departments to clear their specific caches
        $faculties = $this->facultyModel->select('id')->findAll();
        foreach ($faculties as $faculty) {
            $keys[] = "navigation_faculty_complete_{$faculty['id']}";
            $keys[] = "navigation_faculty_showcase_{$faculty['id']}";
        }

        $departments = $this->departmentsModel->select('id')->findAll();
        foreach ($departments as $department) {
            $keys[] = "navigation_department_complete_{$department['id']}";
            $keys[] = "navigation_department_showcase_{$department['id']}";
        }

        foreach ($keys as $key) {
            $this->cache->delete($key);
        }
    }
}