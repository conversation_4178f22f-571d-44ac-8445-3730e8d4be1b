<?php

namespace App\Services;

class CacheInvalidationService
{
    protected $cache;
    protected $logger;

    public function __construct()
    {
        $this->cache = \Config\Services::cache();
        $this->logger = \Config\Services::logger();
    }

    /**
     * Invalidate faculty-specific caches
     */
    public function invalidateFacultyCache($facultyId = null)
    {
        $keys = ['navigation_faculty_hierarchy'];
        
        if ($facultyId) {
            $keys[] = "navigation_faculty_complete_{$facultyId}";
        }
        
        // Also clear university stats as faculty changes affect overall stats
        $keys[] = 'navigation_university_stats';
        
        return $this->clearCacheKeys($keys, 'faculty', $facultyId);
    }

    /**
     * Invalidate department-specific caches
     */
    public function invalidateDepartmentCache($departmentId = null, $facultyId = null)
    {
        $keys = ['navigation_faculty_hierarchy'];
        
        if ($departmentId) {
            $keys[] = "navigation_department_complete_{$departmentId}";
        }
        
        if ($facultyId) {
            $keys[] = "navigation_faculty_complete_{$facultyId}";
        }
        
        // Also clear university stats
        $keys[] = 'navigation_university_stats';
        
        return $this->clearCacheKeys($keys, 'department', $departmentId);
    }

    /**
     * Invalidate faculty member related caches
     */
    public function invalidateFacultyMemberCache($departmentId = null, $facultyId = null)
    {
        $keys = ['navigation_faculty_hierarchy'];
        
        if ($departmentId) {
            $keys[] = "navigation_department_complete_{$departmentId}";
        }
        
        if ($facultyId) {
            $keys[] = "navigation_faculty_complete_{$facultyId}";
        }
        
        // Also clear university stats
        $keys[] = 'navigation_university_stats';
        
        return $this->clearCacheKeys($keys, 'faculty_member', null);
    }

    /**
     * Invalidate student related caches
     */
    public function invalidateStudentCache($departmentId = null, $facultyId = null)
    {
        $keys = ['navigation_faculty_hierarchy'];
        
        if ($departmentId) {
            $keys[] = "navigation_department_complete_{$departmentId}";
        }
        
        if ($facultyId) {
            $keys[] = "navigation_faculty_complete_{$facultyId}";
        }
        
        // Also clear university stats
        $keys[] = 'navigation_university_stats';
        
        return $this->clearCacheKeys($keys, 'student', null);
    }

    /**
     * Clear all navigation-related caches
     */
    public function clearAllNavigationCaches()
    {
        $keys = [
            'navigation_faculty_hierarchy',
            'navigation_university_stats'
        ];
        
        // Clear all faculty and department specific caches
        $this->clearFacultySpecificCaches();
        $this->clearDepartmentSpecificCaches();
        
        return $this->clearCacheKeys($keys, 'all_navigation', null);
    }

    /**
     * Clear navigation hierarchy cache specifically
     */
    public function invalidateNavigationCache()
    {
        return $this->clearCacheKeys(['navigation_faculty_hierarchy'], 'navigation_hierarchy', null);
    }

    /**
     * Clear university statistics cache
     */
    public function invalidateUniversityStats()
    {
        return $this->clearCacheKeys(['navigation_university_stats'], 'university_stats', null);
    }

    /**
     * Clear cache keys and handle errors
     */
    protected function clearCacheKeys(array $keys, $entityType = null, $entityId = null)
    {
        $success = true;
        $clearedKeys = [];
        $failedKeys = [];

        foreach ($keys as $key) {
            try {
                $result = $this->cache->delete($key);
                if ($result) {
                    $clearedKeys[] = $key;
                } else {
                    $failedKeys[] = $key;
                    $success = false;
                }
            } catch (\Exception $e) {
                $failedKeys[] = $key;
                $success = false;
                $this->logger->error("Cache deletion failed for key '{$key}': " . $e->getMessage());
            }
        }

        // Log the cache action
        $this->logCacheAction($entityType, $entityId, $clearedKeys, $failedKeys, $success);

        return [
            'success' => $success,
            'cleared_keys' => $clearedKeys,
            'failed_keys' => $failedKeys,
            'total_attempted' => count($keys)
        ];
    }

    /**
     * Clear faculty-specific caches by pattern
     */
    protected function clearFacultySpecificCaches()
    {
        try {
            // Get all faculties to clear their specific caches
            $facultyModel = new \App\Models\FacultyModel();
            $faculties = $facultyModel->select('id')->findAll();
            
            foreach ($faculties as $faculty) {
                $this->cache->delete("navigation_faculty_complete_{$faculty['id']}");
            }
        } catch (\Exception $e) {
            $this->logger->error("Failed to clear faculty-specific caches: " . $e->getMessage());
        }
    }

    /**
     * Clear department-specific caches by pattern
     */
    protected function clearDepartmentSpecificCaches()
    {
        try {
            // Get all departments to clear their specific caches
            $departmentsModel = new \App\Models\DepartmentsModel();
            $departments = $departmentsModel->select('id')->findAll();
            
            foreach ($departments as $department) {
                $this->cache->delete("navigation_department_complete_{$department['id']}");
            }
        } catch (\Exception $e) {
            $this->logger->error("Failed to clear department-specific caches: " . $e->getMessage());
        }
    }

    /**
     * Log cache invalidation actions
     */
    protected function logCacheAction($entityType, $entityId, $clearedKeys, $failedKeys, $success)
    {
        $context = [
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'cleared_keys' => $clearedKeys,
            'failed_keys' => $failedKeys,
            'success' => $success
        ];

        if ($success) {
            $message = "Cache invalidation successful for {$entityType}";
            if ($entityId) {
                $message .= " (ID: {$entityId})";
            }
            $this->logger->info($message, $context);
        } else {
            $message = "Cache invalidation partially failed for {$entityType}";
            if ($entityId) {
                $message .= " (ID: {$entityId})";
            }
            $this->logger->warning($message, $context);
        }
    }

    /**
     * Get cache status for monitoring
     */
    public function getCacheStatus()
    {
        $keys = [
            'navigation_faculty_hierarchy',
            'navigation_university_stats'
        ];

        $status = [];
        foreach ($keys as $key) {
            $cached = $this->cache->get($key);
            $status[$key] = [
                'exists' => $cached !== null,
                'size' => $cached ? strlen(serialize($cached)) : 0
            ];
        }

        return $status;
    }
}