<?php

namespace App\Services;

use App\Models\FacultyMembersModel;
use App\Models\StudentsModel;
use App\Models\DepartmentsModel;
use App\Models\FacultyModel;
use App\Models\PagesModel;
use App\Models\EventsModel;
use App\Models\NoticesModel;
use App\Models\AdministrationsModel;

class SearchService
{
    protected $facultyMembersModel;
    protected $studentsModel;
    protected $departmentsModel;
    protected $facultyModel;
    protected $pagesModel;
    protected $eventsModel;
    protected $noticesModel;
    protected $administrationsModel;

    public function __construct()
    {
        $this->facultyMembersModel = new FacultyMembersModel();
        $this->studentsModel = new StudentsModel();
        $this->departmentsModel = new DepartmentsModel();
        $this->facultyModel = new FacultyModel();
        $this->pagesModel = new PagesModel();
        $this->eventsModel = new EventsModel();
        $this->noticesModel = new NoticesModel();
        $this->administrationsModel = new AdministrationsModel();
    }

    /**
     * Perform quick search across all entities
     * 
     * @param string $query Search query
     * @param int $limit Results limit per category
     * @return array Formatted search results
     */
    public function performQuickSearch(string $query, int $limit = 5): array
    {
        if (empty(trim($query))) {
            return [];
        }

        $query = trim($query);
        
        // Check cache first
        $cache = \Config\Services::cache();
        $cacheKey = 'search_quick_' . md5($query . '_' . $limit);
        $cachedResults = $cache->get($cacheKey);
        
        if ($cachedResults !== null) {
            return $cachedResults;
        }
        
        $results = [];

        try {
            // Search Faculty Members
            $facultyResults = $this->searchFacultyMembers($query, $limit);
            if (!empty($facultyResults)) {
                $results[] = [
                    'category' => 'Faculty Members',
                    'icon' => 'zmdi-account',
                    'results' => $facultyResults,
                    'total_count' => count($facultyResults),
                    'view_all_url' => base_url('dashboard/manageFacultyMembers?search=' . urlencode($query))
                ];
            }

            // Search Students
            $studentResults = $this->searchStudents($query, $limit);
            if (!empty($studentResults)) {
                $results[] = [
                    'category' => 'Students',
                    'icon' => 'zmdi-graduation-cap',
                    'results' => $studentResults,
                    'total_count' => count($studentResults),
                    'view_all_url' => base_url('dashboard/manageStudents?search=' . urlencode($query))
                ];
            }

            // Search Departments
            $departmentResults = $this->searchDepartments($query, $limit);
            if (!empty($departmentResults)) {
                $results[] = [
                    'category' => 'Departments',
                    'icon' => 'zmdi-city-alt',
                    'results' => $departmentResults,
                    'total_count' => count($departmentResults),
                    'view_all_url' => base_url('dashboard/manageDepartments?search=' . urlencode($query))
                ];
            }

            // Search Faculties
            $facultyResults = $this->searchFaculties($query, $limit);
            if (!empty($facultyResults)) {
                $results[] = [
                    'category' => 'Faculties',
                    'icon' => 'zmdi-library',
                    'results' => $facultyResults,
                    'total_count' => count($facultyResults),
                    'view_all_url' => base_url('dashboard/manageFaculties?search=' . urlencode($query))
                ];
            }

            // Search Pages
            $pageResults = $this->searchPages($query, $limit);
            if (!empty($pageResults)) {
                $results[] = [
                    'category' => 'Pages',
                    'icon' => 'zmdi-file-text',
                    'results' => $pageResults,
                    'total_count' => count($pageResults),
                    'view_all_url' => base_url('dashboard/pages?search=' . urlencode($query))
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Search error: ' . $e->getMessage());
            return [];
        }

        // Cache results for 5 minutes
        if (!empty($results)) {
            $cache->save($cacheKey, $results, 300);
        }

        return $results;
    }

    /**
     * Perform comprehensive search with pagination
     * 
     * @param string $query Search query
     * @param array $filters Search filters
     * @return array Comprehensive search results
     */
    public function performFullSearch(string $query, array $filters = []): array
    {
        if (empty(trim($query))) {
            return [
                'results' => [],
                'total_count' => 0,
                'query' => $query,
                'filters' => $filters
            ];
        }

        $query = trim($query);
        $category = $filters['category'] ?? 'all';
        $page = (int)($filters['page'] ?? 1);
        $perPage = (int)($filters['per_page'] ?? 20);
        $sortBy = $filters['sort_by'] ?? 'relevance';

        // Check cache first
        $cache = \Config\Services::cache();
        $cacheKey = 'search_full_' . md5($query . '_' . serialize($filters));
        $cachedResults = $cache->get($cacheKey);
        
        if ($cachedResults !== null) {
            return $cachedResults;
        }

        $results = [];
        $totalCount = 0;

        try {
            if ($category === 'all' || $category === 'faculty_members') {
                $facultyResults = $this->searchFacultyMembers($query, null);
                if (!empty($facultyResults)) {
                    $results['Faculty Members'] = $facultyResults;
                    $totalCount += count($facultyResults);
                }
            }

            if ($category === 'all' || $category === 'students') {
                $studentResults = $this->searchStudents($query, null);
                if (!empty($studentResults)) {
                    $results['Students'] = $studentResults;
                    $totalCount += count($studentResults);
                }
            }

            if ($category === 'all' || $category === 'departments') {
                $departmentResults = $this->searchDepartments($query, null);
                if (!empty($departmentResults)) {
                    $results['Departments'] = $departmentResults;
                    $totalCount += count($departmentResults);
                }
            }

            if ($category === 'all' || $category === 'faculties') {
                $facultyResults = $this->searchFaculties($query, null);
                if (!empty($facultyResults)) {
                    $results['Faculties'] = $facultyResults;
                    $totalCount += count($facultyResults);
                }
            }

            if ($category === 'all' || $category === 'pages') {
                $pageResults = $this->searchPages($query, null);
                if (!empty($pageResults)) {
                    $results['Pages'] = $pageResults;
                    $totalCount += count($pageResults);
                }
            }

        } catch (\Exception $e) {
            log_message('error', 'Full search error: ' . $e->getMessage());
        }

        $searchResults = [
            'results' => $results,
            'total_count' => $totalCount,
            'query' => $query,
            'filters' => $filters,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($totalCount / $perPage)
            ]
        ];

        // Cache results for 5 minutes
        if ($totalCount > 0) {
            $cache->save($cacheKey, $searchResults, 300);
        }

        return $searchResults;
    }

    /**
     * Search faculty members
     */
    protected function searchFacultyMembers(string $query, ?int $limit = null): array
    {
        $results = $this->facultyMembersModel->searchFacultyMembers($query);
        
        if ($limit) {
            $results = array_slice($results, 0, $limit);
        }

        return array_map(function($item) use ($query) {
            return [
                'id' => $item['record_id'],
                'title' => $item['full_name'],
                'description' => $item['designation'] . ' - ' . ($item['department_name'] ?? 'No Department'),
                'url' => base_url('dashboard/editFacultyMember/' . $item['record_id']),
                'highlight' => $this->highlightText($item['full_name'], $query),
                'metadata' => [
                    'department' => $item['department_name'] ?? 'No Department',
                    'designation' => $item['designation'],
                    'email' => $item['email'],
                    'employee_id' => $item['employee_id']
                ]
            ];
        }, $results);
    }

    /**
     * Search students
     */
    protected function searchStudents(string $query, ?int $limit = null): array
    {
        $results = $this->studentsModel->searchStudents($query);
        
        if ($limit) {
            $results = array_slice($results, 0, $limit);
        }

        return array_map(function($item) use ($query) {
            return [
                'id' => $item['record_id'],
                'title' => $item['full_name'],
                'description' => $item['program_type'] . ' - ' . ($item['department_name'] ?? 'No Department'),
                'url' => base_url('dashboard/editStudent/' . $item['record_id']),
                'highlight' => $this->highlightText($item['full_name'], $query),
                'metadata' => [
                    'department' => $item['department_name'] ?? 'No Department',
                    'program_type' => $item['program_type'],
                    'student_id' => $item['student_id'],
                    'status' => ucfirst($item['status'])
                ]
            ];
        }, $results);
    }

    /**
     * Search departments
     */
    protected function searchDepartments(string $query, ?int $limit = null): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('departments d')
                     ->select('d.*, f.faculty_name, fm.full_name as head_name')
                     ->join('faculties f', 'd.faculty_id = f.id', 'left')
                     ->join('faculty_members fm', 'd.head_of_department_id = fm.record_id', 'left')
                     ->groupStart()
                         ->like('d.department_name', $query)
                         ->orLike('d.department_description', $query)
                         ->orLike('d.profile', $query)
                         ->orLike('f.faculty_name', $query)
                     ->groupEnd();

        if ($limit) {
            $builder->limit($limit);
        }

        $results = $builder->get()->getResultArray();

        return array_map(function($item) use ($query) {
            return [
                'id' => $item['id'],
                'title' => $item['department_name'],
                'description' => 'Department in ' . ($item['faculty_name'] ?? 'Unknown Faculty'),
                'url' => base_url('dashboard/editDepartment/' . $item['id']),
                'highlight' => $this->highlightText($item['department_name'], $query),
                'metadata' => [
                    'faculty' => $item['faculty_name'] ?? 'Unknown Faculty',
                    'head' => $item['head_name'] ?? 'No Head Assigned'
                ]
            ];
        }, $results);
    }

    /**
     * Search faculties
     */
    protected function searchFaculties(string $query, ?int $limit = null): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('faculties f')
                     ->select('f.*, fm.full_name as dean_name')
                     ->join('faculty_members fm', 'f.dean_id = fm.record_id', 'left')
                     ->groupStart()
                         ->like('f.faculty_name', $query)
                         ->orLike('f.faculty_description', $query)
                     ->groupEnd();

        if ($limit) {
            $builder->limit($limit);
        }

        $results = $builder->get()->getResultArray();

        return array_map(function($item) use ($query) {
            return [
                'id' => $item['id'],
                'title' => $item['faculty_name'],
                'description' => 'Faculty with ' . $this->getDepartmentCount($item['id']) . ' departments',
                'url' => base_url('dashboard/editFaculty/' . $item['id']),
                'highlight' => $this->highlightText($item['faculty_name'], $query),
                'metadata' => [
                    'dean' => $item['dean_name'] ?? 'No Dean Assigned',
                    'departments_count' => $this->getDepartmentCount($item['id'])
                ]
            ];
        }, $results);
    }

    /**
     * Search pages
     */
    protected function searchPages(string $query, ?int $limit = null): array
    {
        $db = \Config\Database::connect();
        $builder = $db->table('pages')
                     ->groupStart()
                         ->like('pageName', $query)
                         ->orLike('pageData', $query)
                     ->groupEnd();

        if ($limit) {
            $builder->limit($limit);
        }

        $results = $builder->get()->getResultArray();

        return array_map(function($item) use ($query) {
            return [
                'id' => $item['id'],
                'title' => $item['pageName'],
                'description' => 'CMS Page - Last updated ' . date('M j, Y', strtotime($item['updated_at'])),
                'url' => base_url('dashboard/pages/edit/' . $item['id']),
                'highlight' => $this->highlightText($item['pageName'], $query),
                'metadata' => [
                    'created' => date('M j, Y', strtotime($item['created_at'])),
                    'updated' => date('M j, Y', strtotime($item['updated_at']))
                ]
            ];
        }, $results);
    }

    /**
     * Get department count for a faculty
     */
    protected function getDepartmentCount(int $facultyId): int
    {
        $db = \Config\Database::connect();
        return $db->table('departments')->where('faculty_id', $facultyId)->countAllResults();
    }

    /**
     * Highlight matching text in search results
     */
    protected function highlightText(string $text, string $query): string
    {
        if (empty($query)) {
            return $text;
        }

        $highlighted = preg_replace(
            '/(' . preg_quote($query, '/') . ')/i',
            '<mark>$1</mark>',
            $text
        );

        return $highlighted ?: $text;
    }

    /**
     * Filter results based on user permissions
     */
    public function filterByPermissions(array $results, $user = null): array
    {
        // For now, return all results as the CMS doesn't have complex permissions
        // This method can be enhanced when role-based permissions are implemented
        return $results;
    }

    /**
     * Search by specific category
     */
    public function searchByCategory(string $query, string $category): array
    {
        if (empty(trim($query))) {
            return [];
        }

        $query = trim($query);
        $results = [];

        try {
            switch ($category) {
                case 'faculty_members':
                    $results = $this->searchFacultyMembers($query, null);
                    break;
                
                case 'students':
                    $results = $this->searchStudents($query, null);
                    break;
                
                case 'departments':
                    $results = $this->searchDepartments($query, null);
                    break;
                
                case 'faculties':
                    $results = $this->searchFaculties($query, null);
                    break;
                
                case 'pages':
                    $results = $this->searchPages($query, null);
                    break;
                
                default:
                    // Return empty if category not found
                    break;
            }

        } catch (\Exception $e) {
            log_message('error', 'Category search error: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Get search suggestions based on query
     */
    public function getSearchSuggestions(string $query, int $limit = 10): array
    {
        if (strlen($query) < 2) {
            return [];
        }

        $suggestions = [];

        try {
            // Get faculty member names
            $facultyNames = $this->facultyMembersModel
                ->select('full_name')
                ->like('full_name', $query)
                ->limit($limit)
                ->findAll();

            foreach ($facultyNames as $faculty) {
                $suggestions[] = [
                    'text' => $faculty['full_name'],
                    'category' => 'Faculty Members'
                ];
            }

            // Get department names
            $departmentNames = $this->departmentsModel
                ->select('department_name')
                ->like('department_name', $query)
                ->limit($limit)
                ->findAll();

            foreach ($departmentNames as $dept) {
                $suggestions[] = [
                    'text' => $dept['department_name'],
                    'category' => 'Departments'
                ];
            }

            // Remove duplicates and limit results
            $suggestions = array_unique($suggestions, SORT_REGULAR);
            $suggestions = array_slice($suggestions, 0, $limit);

        } catch (\Exception $e) {
            log_message('error', 'Search suggestions error: ' . $e->getMessage());
        }

        return $suggestions;
    }

    /**
     * Clear search cache
     * This should be called when data is updated
     */
    public function clearSearchCache(): bool
    {
        $cache = \Config\Services::cache();
        
        // Clear all search-related cache keys
        // Note: This is a simple implementation. In production, you might want
        // to use cache tags or a more sophisticated cache invalidation strategy
        return $cache->deleteMatching('search_*');
    }

    /**
     * Clear cache for specific search queries
     */
    public function clearCacheForQuery(string $query): bool
    {
        $cache = \Config\Services::cache();
        
        // Clear quick search cache
        $quickCacheKey = 'search_quick_' . md5($query . '_5');
        $cache->delete($quickCacheKey);
        
        // Clear full search cache (this is simplified - in reality you'd need to clear all variations)
        $fullCachePattern = 'search_full_' . md5($query . '_*');
        return $cache->deleteMatching($fullCachePattern);
    }
}