<?php

namespace App\Services;

use App\Models\FacultyModel;
use App\Models\DepartmentsModel;
use App\Models\FacultyMembersModel;
use App\Models\StudentsModel;

class DataAggregationService
{
    protected $facultyModel;
    protected $departmentsModel;
    protected $facultyMembersModel;
    protected $studentsModel;
    protected $cache;

    public function __construct()
    {
        $this->facultyModel = new FacultyModel();
        $this->departmentsModel = new DepartmentsModel();
        $this->facultyMembersModel = new FacultyMembersModel();
        $this->studentsModel = new StudentsModel();
        $this->cache = \Config\Services::cache();
    }

    /**
     * Get comprehensive faculty showcase with optimized queries
     */
    public function getFacultyShowcaseOptimized($facultyId, $useCache = true)
    {
        $cacheKey = "faculty_showcase_optimized_{$facultyId}";
        
        if ($useCache) {
            $cachedData = $this->cache->get($cacheKey);
            if ($cachedData !== null) {
                return $cachedData;
            }
        }

        $db = \Config\Database::connect();

        // Single query to get faculty with dean info
        $faculty = $this->facultyModel->getFacultyWithDean($facultyId);
        if (!$faculty) {
            return null;
        }

        // Optimized query to get all related data in fewer queries
        $showcaseData = [
            'faculty' => $faculty,
            'departments' => [],
            'faculty_members' => [],
            'phd_awardees' => [],
            'current_students' => [],
            'alumni' => [],
            'news_events' => [],
            'statistics' => []
        ];

        // Get departments
        $departments = $db->table('departments d')
                          ->select('d.*, fm.full_name as head_name, fm.designation as head_designation')
                          ->join('faculty_members fm', 'd.head_of_department_id = fm.record_id', 'left')
                          ->where('d.faculty_id', $facultyId)
                          ->orderBy('d.department_name', 'ASC')
                          ->get()
                          ->getResultArray();

        $showcaseData['departments'] = $departments;

        if (!empty($departments)) {
            $departmentIds = array_column($departments, 'id');
            $departmentIdsStr = implode(',', $departmentIds);

            // Get faculty members
            $facultyMembers = $db->table('faculty_members fm')
                                 ->select('fm.*, d.department_name')
                                 ->join('departments d', 'fm.department_id = d.id')
                                 ->where("fm.department_id IN ($departmentIdsStr)")
                                 ->where('fm.status', 'active')
                                 ->orderBy('fm.designation', 'ASC')
                                 ->orderBy('fm.full_name', 'ASC')
                                 ->get()
                                 ->getResultArray();

            $showcaseData['faculty_members'] = $facultyMembers;

            // Filter PhD awardees
            $showcaseData['phd_awardees'] = array_filter($facultyMembers, function($member) {
                return $member['highest_qualification'] === 'PhD';
            });

            // Get current students
            $currentStudents = $db->table('students s')
                                  ->select('s.*, d.department_name')
                                  ->join('departments d', 's.department_id = d.id')
                                  ->where("s.department_id IN ($departmentIdsStr)")
                                  ->where('s.actual_completion_year IS NULL')
                                  ->where('s.status', 'current')
                                  ->orderBy('s.admission_year', 'DESC')
                                  ->orderBy('s.full_name', 'ASC')
                                  ->get()
                                  ->getResultArray();

            $showcaseData['current_students'] = $currentStudents;

            // Get alumni
            $alumni = $db->table('students s')
                         ->select('s.*, d.department_name')
                         ->join('departments d', 's.department_id = d.id')
                         ->where("s.department_id IN ($departmentIdsStr)")
                         ->where('s.actual_completion_year IS NOT NULL')
                         ->where('s.status', 'alumni')
                         ->orderBy('s.actual_completion_year', 'DESC')
                         ->orderBy('s.full_name', 'ASC')
                         ->get()
                         ->getResultArray();

            $showcaseData['alumni'] = $alumni;

            // Get news and events
            $newsEvents = $db->table('events_news-events ne')
                             ->select('ne.*, d.department_name')
                             ->join('departments d', 'ne.department_id = d.id')
                             ->where("ne.department_id IN ($departmentIdsStr)")
                             ->where('ne.is_active', 'active')
                             ->orderBy('ne.created_at', 'DESC')
                             ->limit(20) // Limit for performance
                             ->get()
                             ->getResultArray();

            $showcaseData['news_events'] = $newsEvents;
        }

        // Calculate statistics
        $showcaseData['statistics'] = [
            'total_departments' => count($showcaseData['departments']),
            'total_faculty_members' => count($showcaseData['faculty_members']),
            'total_phd_awardees' => count($showcaseData['phd_awardees']),
            'total_current_students' => count($showcaseData['current_students']),
            'total_alumni' => count($showcaseData['alumni']),
            'total_news_events' => count($showcaseData['news_events'])
        ];

        // Cache the result
        if ($useCache) {
            $this->cache->save($cacheKey, $showcaseData, 3600); // Cache for 1 hour
        }

        return $showcaseData;
    }

    /**
     * Get comprehensive department showcase with optimized queries
     */
    public function getDepartmentShowcaseOptimized($departmentId, $useCache = true)
    {
        $cacheKey = "department_showcase_optimized_{$departmentId}";
        
        if ($useCache) {
            $cachedData = $this->cache->get($cacheKey);
            if ($cachedData !== null) {
                return $cachedData;
            }
        }

        $db = \Config\Database::connect();

        // Get department with head and faculty info
        $department = $this->departmentsModel->getDepartmentWithHead($departmentId);
        if (!$department) {
            return null;
        }

        $showcaseData = [
            'department' => $department,
            'faculty_members' => [],
            'phd_awardees' => [],
            'current_students' => [],
            'alumni' => [],
            'news_events' => [],
            'statistics' => []
        ];

        // Get faculty members
        $facultyMembers = $db->table('faculty_members')
                             ->where('department_id', $departmentId)
                             ->where('status', 'active')
                             ->orderBy('designation', 'ASC')
                             ->orderBy('full_name', 'ASC')
                             ->get()
                             ->getResultArray();

        $showcaseData['faculty_members'] = $facultyMembers;

        // Filter PhD awardees
        $showcaseData['phd_awardees'] = array_values(array_filter($facultyMembers, function($member) {
            return $member['highest_qualification'] === 'PhD';
        }));

        // Get current students
        $currentStudents = $db->table('students')
                              ->where('department_id', $departmentId)
                              ->where('actual_completion_year IS NULL')
                              ->where('status', 'current')
                              ->orderBy('admission_year', 'DESC')
                              ->orderBy('full_name', 'ASC')
                              ->get()
                              ->getResultArray();

        $showcaseData['current_students'] = $currentStudents;

        // Get alumni
        $alumni = $db->table('students')
                     ->where('department_id', $departmentId)
                     ->where('actual_completion_year IS NOT NULL')
                     ->where('status', 'alumni')
                     ->orderBy('actual_completion_year', 'DESC')
                     ->orderBy('full_name', 'ASC')
                     ->get()
                     ->getResultArray();

        $showcaseData['alumni'] = $alumni;

        // Get news and events
        $newsEvents = $db->table('events_news-events')
                         ->where('department_id', $departmentId)
                         ->where('is_active', 'active')
                         ->orderBy('created_at', 'DESC')
                         ->limit(20) // Limit for performance
                         ->get()
                         ->getResultArray();

        $showcaseData['news_events'] = $newsEvents;

        // Calculate statistics
        $showcaseData['statistics'] = [
            'total_faculty_members' => count($showcaseData['faculty_members']),
            'total_phd_awardees' => count($showcaseData['phd_awardees']),
            'total_current_students' => count($showcaseData['current_students']),
            'total_alumni' => count($showcaseData['alumni']),
            'total_news_events' => count($showcaseData['news_events'])
        ];

        // Cache the result
        if ($useCache) {
            $this->cache->save($cacheKey, $showcaseData, 3600); // Cache for 1 hour
        }

        return $showcaseData;
    }

    /**
     * Get system-wide statistics
     */
    public function getSystemStatistics($useCache = true)
    {
        $cacheKey = "system_statistics";
        
        if ($useCache) {
            $cachedData = $this->cache->get($cacheKey);
            if ($cachedData !== null) {
                return $cachedData;
            }
        }

        $db = \Config\Database::connect();

        $stats = [
            'totals' => [
                'faculties' => $this->facultyModel->countAllResults(),
                'departments' => $this->departmentsModel->countAllResults(),
                'faculty_members' => $this->facultyMembersModel->where('status', 'active')->countAllResults(),
                'current_students' => $this->studentsModel->where('status', 'current')->countAllResults(),
                'alumni' => $this->studentsModel->where('status', 'alumni')->countAllResults(),
                'phd_holders' => $this->facultyMembersModel->where('highest_qualification', 'PhD')->where('status', 'active')->countAllResults()
            ],
            'faculty_breakdown' => [],
            'recent_activities' => []
        ];

        // Get faculty breakdown
        $facultyBreakdown = $db->table('faculties f')
                               ->select('f.faculty_name, 
                                        COUNT(DISTINCT d.id) as departments_count,
                                        COUNT(DISTINCT fm.record_id) as faculty_members_count,
                                        COUNT(DISTINCT s.record_id) as students_count')
                               ->join('departments d', 'f.id = d.faculty_id', 'left')
                               ->join('faculty_members fm', 'd.id = fm.department_id AND fm.status = "active"', 'left')
                               ->join('students s', 'd.id = s.department_id AND s.status = "current"', 'left')
                               ->groupBy('f.id, f.faculty_name')
                               ->orderBy('f.faculty_name', 'ASC')
                               ->get()
                               ->getResultArray();

        $stats['faculty_breakdown'] = $facultyBreakdown;

        // Cache the result
        if ($useCache) {
            $this->cache->save($cacheKey, $stats, 1800); // Cache for 30 minutes
        }

        return $stats;
    }

    /**
     * Clear all showcase caches
     */
    public function clearAllShowcaseCaches()
    {
        // This would require implementing a cache tag system or pattern matching
        // For now, we'll clear specific known caches
        $this->cache->clean();
        return true;
    }

    /**
     * Clear cache for specific faculty
     */
    public function clearFacultyCache($facultyId)
    {
        $keys = [
            "faculty_showcase_{$facultyId}",
            "faculty_showcase_optimized_{$facultyId}",
            "system_statistics"
        ];

        foreach ($keys as $key) {
            $this->cache->delete($key);
        }

        return true;
    }

    /**
     * Clear cache for specific department
     */
    public function clearDepartmentCache($departmentId)
    {
        $keys = [
            "department_showcase_{$departmentId}",
            "department_showcase_optimized_{$departmentId}",
            "system_statistics"
        ];

        foreach ($keys as $key) {
            $this->cache->delete($key);
        }

        return true;
    }
}