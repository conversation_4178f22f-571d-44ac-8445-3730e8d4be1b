<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->


            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Upcoming Events</h4>
                    <hr>
                    <div class="row gy-3">
                        <div class="col-md-2 text-end d-grid">
                            <!-- Collapse Trigger Button -->
                            <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#bannerFormCollapse" aria-expanded="false" aria-controls="bannerFormCollapse">
                                Add banner
                            </button>
                        </div>
                    </div>

                    <!-- Collapsible Form -->
                    <div class="collapse mt-4" id="bannerFormCollapse">

                        <form class="row g-3" id="upcomingevent" enctype="multipart/form-data">
                            <div class="col-md-6">
                                <label for="inputFirstName" class="form-label">Title</label>
                                <input type="text" name="title" class="form-control" id="inputFirstName">
                            </div>
                            <div class="col-md-6">
                                <label for="description" class="form-label">Description</label>
                                <textarea type="text" name="description" class="form-control" id="description"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="department" class="form-label">Department</label>
                                <select class="form-control" id="department" name="department">
                                    <option value="">None</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= esc($dept['id']) ?>"><?= esc($dept['department_name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="files" class="form-label">Files</label>
                                <input type="file" name="files[]" class="form-control" id="files" multiple>
                            </div>
                            <div class="col-md-6">
                                <label for="starting_date" class="form-label">Starting Date</label>
                                <input type="datetime-local" name="starting_date" class="form-control" id="starting_date">
                            </div>
                            <div class="col-md-6">
                                <label for="ending_date" class="form-label">Ending Date</label>
                                <input type="datetime-local" name="ending_date" class="form-control" id="ending_date">
                            </div>

                            <div class="col-12 mt-2">
                                <button type="submit" class="btn btn-light px-5">Submit</button>
                            </div>
                        </form>

                    </div>

                    <?php foreach ($upcomingevents as $events): ?>
                        <?php
                        // Decode banner image array
                        $bannerImages = json_decode($events->banner, true);
                        ?>

                        <div class="row align-items-start mb-4 mt-4 border p-3 rounded shadow">
                            <!-- Left: Banner Images Preview -->
                            <div class="col-md-8 d-flex flex-wrap gap-2">
                                <?php if (!empty($bannerImages) && is_array($bannerImages)): ?>
                                    <?php foreach ($bannerImages as $img): ?>
                                        <img
                                            src="<?= base_url('uploads/events/' . esc($img)); ?>"
                                            alt="Banner Image"
                                            class="img-thumbnail shadow"
                                            style="width: 140px; height: 100px; object-fit: cover;">
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <p>No banner images available.</p>
                                <?php endif; ?>
                            </div>

                            <!-- Right: Title, Subtitle, Upload -->
                            <div class="col-md-4">
                                <!-- <form id="editupcomingeventform_<?= $events->id ?>" enctype="multipart/form-data"> -->
                                    <form id="editupcomingeventform_<?= $events->id ?>" class="editupcomingeventform" enctype="multipart/form-data">


                                    <input type="hidden" name="id" value="<?= esc($events->id); ?>">
                                    <div class="mb-3">
                                        <label class="form-label">Banner Title</label>
                                        <input type="text"
                                            id="bannerTitle_<?= $events->id ?>"
                                            class="form-control"
                                            value="<?= esc($events->title); ?>"
                                            name="title">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Event Starting Date</label>
                                        <input type="datetime-local"
                                            id="eventStartingDate_<?= $events->id ?>"
                                            class="form-control"
                                            value="<?= esc($events->starting_date); ?>"
                                            name="starting_date">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Event Ending Date</label>
                                        <input type="datetime-local"
                                            id="eventEndingDate_<?= $events->id ?>"
                                            class="form-control"
                                            value="<?= esc($events->ending_date); ?>"
                                            name="ending_date">
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Banner Description</label>
                                        <input type="text"
                                            id="bannerDescription_<?= $events->id ?>"
                                            class="form-control"
                                            value="<?= esc($events->description); ?>"
                                            name="description">
                                    </div>

                                    <button type="submit"
                                        class="btn btn-primary"
                                        >Update Event</button>
                                    <button type="button"
                                        class="btn btn-danger"
                                        onclick="deleteupcomingevent_(<?= $events->id ?>)">Delete Event</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>


                </div>

            </div>
        </div>

        <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>

        <!--End Dashboard Content-->

        <!--start overlay-->
        <div class="overlay toggle-menu"></div>
        <!--end overlay-->

    </div>
    <!-- End container-fluid-->

</div><!--End content-wrapper-->
<!--Start Back To Top Button-->
<a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
<!--End Back To Top Button-->

<!--Start footer-->

<!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<!-- custom scripts for saving the form -->


<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
    $('#upcomingevent').on('submit', function(e) {

        e.preventDefault();

        const formData = new FormData(this); // Automatically includes file input

        ajaxPostData("<?= base_url(); ?>dashboard/ajaxaddEvents", formData, function(res) {
            if (res.status === 'success') {

                // $('#editNoticeForm')[0].reset(); // Optional reset
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }
        });
    });
    $('.editupcomingeventform').on('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);

    ajaxPostData("<?= base_url(); ?>dashboard/ajaxEditEvents", formData, function(res) {
        if (res.status === 'success') {
            setTimeout(function() {
                window.location.reload();
            }, 1000);
        }
    });
});


    function deleteupcomingevent(id) {
        if (confirm('Are you sure you want to delete this event?')) {
            const formData = new FormData();
            formData.append('id', id);

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxDeleteEvents", formData, function(res) {
                if (res.status === 'success') {
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    alert('Failed to delete event.');
                }
            });
        }
    }

</script>

<?= $this->endSection(); ?>