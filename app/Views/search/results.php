<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>

<!--Start sidebar-wrapper-->
<?= $this->include('partials/sidebar'); ?>
<!--End sidebar-wrapper-->

<!--Start topbar header-->
<?= $this->include('partials/topbar'); ?>
<!--End topbar header-->

<div class="clearfix"></div>

<div class="content-wrapper">
    <div class="container-fluid">
        
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="search-results-page">
                            
                            <!-- Search Header -->
                            <div class="search-results-header">
                                <?php if (!empty($query)): ?>
                                    <h2 class="search-query">Search results for: "<?= esc($query) ?>"</h2>
                                    <?php if (isset($search_results)): ?>
                                        <div class="search-meta">
                                            Found <?= $search_results['total_count'] ?> results
                                            <?php if ($search_results['total_count'] > 0): ?>
                                                in <?= count($search_results['results']) ?> categories
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <h2 class="search-query">Search</h2>
                                    <div class="search-meta">Enter a search term to find content across the CMS</div>
                                <?php endif; ?>
                            </div>

                            <!-- Search Form -->
                            <div class="search-form-container mb-4">
                                <form method="GET" action="<?= base_url('dashboard/search/results') ?>" class="row g-3">
                                    <div class="col-md-6">
                                        <input type="text" 
                                               name="q" 
                                               class="form-control form-control-lg" 
                                               placeholder="Search faculty, students, departments..." 
                                               value="<?= esc($query) ?>"
                                               required>
                                    </div>
                                    <div class="col-md-3">
                                        <select name="category" class="form-control form-control-lg">
                                            <?php foreach ($categories as $key => $label): ?>
                                                <option value="<?= $key ?>" <?= $category === $key ? 'selected' : '' ?>>
                                                    <?= $label ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="submit" class="btn btn-primary btn-lg w-100">
                                            <i class="zmdi zmdi-search"></i> Search
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- Search Filters -->
                            <?php if (!empty($query) && isset($search_results)): ?>
                                <div class="search-filters">
                                    <div class="search-filter">
                                        <label>Sort by:</label>
                                        <select name="sort_by" class="form-control" onchange="updateSearchResults()">
                                            <option value="relevance" <?= $sort_by === 'relevance' ? 'selected' : '' ?>>Relevance</option>
                                            <option value="date" <?= $sort_by === 'date' ? 'selected' : '' ?>>Date</option>
                                            <option value="name" <?= $sort_by === 'name' ? 'selected' : '' ?>>Name</option>
                                        </select>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Search Results -->
                            <?php if (isset($error)): ?>
                                <div class="alert alert-danger">
                                    <i class="zmdi zmdi-alert-triangle"></i>
                                    <?= esc($error) ?>
                                </div>
                            <?php elseif (!empty($query) && isset($search_results)): ?>
                                
                                <?php if ($search_results['total_count'] === 0): ?>
                                    <div class="search-no-results text-center py-5">
                                        <div class="no-results-message">
                                            <i class="zmdi zmdi-search" style="font-size: 48px; color: #6c757d; margin-bottom: 20px;"></i>
                                            <h4>No results found</h4>
                                            <p>We couldn't find anything matching "<?= esc($query) ?>"</p>
                                            <small>Try different keywords, check spelling, or search in all categories</small>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    
                                    <div class="search-results-list">
                                        <?php foreach ($search_results['results'] as $categoryName => $results): ?>
                                            <div class="search-category-section mb-4">
                                                <h4 class="search-category-title">
                                                    <i class="zmdi zmdi-folder"></i>
                                                    <?= esc($categoryName) ?>
                                                    <span class="badge badge-primary"><?= count($results) ?></span>
                                                </h4>
                                                
                                                <div class="row">
                                                    <?php foreach ($results as $result): ?>
                                                        <div class="col-md-6 col-lg-4 mb-3">
                                                            <div class="search-result-card">
                                                                <div class="result-title">
                                                                    <a href="<?= esc($result['url']) ?>">
                                                                        <?= $result['highlight'] ?? esc($result['title']) ?>
                                                                    </a>
                                                                </div>
                                                                <div class="result-description">
                                                                    <?= esc($result['description']) ?>
                                                                </div>
                                                                <?php if (!empty($result['metadata'])): ?>
                                                                    <div class="result-metadata mt-2">
                                                                        <?php foreach ($result['metadata'] as $key => $value): ?>
                                                                            <?php if ($value && $value !== 'No Department' && $value !== 'Unknown Faculty'): ?>
                                                                                <span class="badge badge-secondary mr-1">
                                                                                    <?= esc($value) ?>
                                                                                </span>
                                                                            <?php endif; ?>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- Pagination -->
                                    <?php if (isset($search_results['pagination']) && $search_results['pagination']['total_pages'] > 1): ?>
                                        <div class="search-pagination">
                                            <nav aria-label="Search results pagination">
                                                <ul class="pagination justify-content-center">
                                                    <?php 
                                                    $currentPage = $search_results['pagination']['current_page'];
                                                    $totalPages = $search_results['pagination']['total_pages'];
                                                    ?>
                                                    
                                                    <!-- Previous Page -->
                                                    <?php if ($currentPage > 1): ?>
                                                        <li class="page-item">
                                                            <a class="page-link" href="<?= base_url('dashboard/search/results') ?>?q=<?= urlencode($query) ?>&category=<?= urlencode($category) ?>&page=<?= $currentPage - 1 ?>&sort_by=<?= urlencode($sort_by) ?>">
                                                                Previous
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                    
                                                    <!-- Page Numbers -->
                                                    <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                                                        <li class="page-item <?= $i === $currentPage ? 'active' : '' ?>">
                                                            <a class="page-link" href="<?= base_url('dashboard/search/results') ?>?q=<?= urlencode($query) ?>&category=<?= urlencode($category) ?>&page=<?= $i ?>&sort_by=<?= urlencode($sort_by) ?>">
                                                                <?= $i ?>
                                                            </a>
                                                        </li>
                                                    <?php endfor; ?>
                                                    
                                                    <!-- Next Page -->
                                                    <?php if ($currentPage < $totalPages): ?>
                                                        <li class="page-item">
                                                            <a class="page-link" href="<?= base_url('dashboard/search/results') ?>?q=<?= urlencode($query) ?>&category=<?= urlencode($category) ?>&page=<?= $currentPage + 1 ?>&sort_by=<?= urlencode($sort_by) ?>">
                                                                Next
                                                            </a>
                                                        </li>
                                                    <?php endif; ?>
                                                </ul>
                                            </nav>
                                        </div>
                                    <?php endif; ?>

                                <?php endif; ?>
                                
                            <?php elseif (!empty($query)): ?>
                                <div class="alert alert-info">
                                    <i class="zmdi zmdi-info"></i>
                                    Please enter at least 2 characters to search.
                                </div>
                            <?php endif; ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script>
function updateSearchResults() {
    const sortBy = document.querySelector('select[name="sort_by"]').value;
    const url = new URL(window.location);
    url.searchParams.set('sort_by', sortBy);
    url.searchParams.set('page', '1'); // Reset to first page when sorting
    window.location.href = url.toString();
}

// Auto-focus search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="q"]');
    if (searchInput && !searchInput.value) {
        searchInput.focus();
    }
});
</script>
<?= $this->endSection(); ?>