<!DOCTYPE html>
<html lang="en">
<script>
  document.addEventListener("DOMContentLoaded", function() {
    const savedTheme = localStorage.getItem('selectedTheme');
    if (savedTheme) {
      document.body.className = savedTheme;
      console.log('Theme loaded from localStorage:', savedTheme);
    }
  });
</script>
<script>
  const BASE_URL = "<?= base_url() ?>";
</script>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
  <meta name="description" content="" />
  <meta name="author" content="" />
  <title><?= isset($title) ? $title : 'JB CMS PANEL'; ?></title>

  <!-- Loader -->
  <link href="<?= base_url('assets/css/pace.min.css'); ?>" rel="stylesheet" />
  <script src="<?= base_url('assets/js/pace.min.js'); ?>"></script>

  <!-- Favicon -->
  <link rel="icon" href="<?= base_url('assets/images/favicon.ico'); ?>" type="image/x-icon">

  <!-- Simplebar CSS -->
  <link href="<?= base_url('assets/plugins/simplebar/css/simplebar.css'); ?>" rel="stylesheet" />

  <!-- Bootstrap core CSS -->
  <link href="<?= base_url('assets/css/bootstrap.min.css'); ?>" rel="stylesheet" />

  <!-- Animate CSS -->
  <link href="<?= base_url('assets/css/animate.css'); ?>" rel="stylesheet" type="text/css" />

  <!-- Icons CSS -->
  <link href="<?= base_url('assets/css/icons.css'); ?>" rel="stylesheet" type="text/css" />

  <!-- Sidebar CSS -->
  <link href="<?= base_url('assets/css/sidebar-menu.css'); ?>" rel="stylesheet" />

  <!-- Custom Style -->
  <link href="<?= base_url('assets/css/app-style.css'); ?>" rel="stylesheet" />

  <script src="<?= base_url('assets/tinymce/tinymce.min.js') ?>"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
  <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
</head>

<body class="bg-theme bg-theme1">
  <div id="loader-overlay" style="display: none;">
    <img src="<?= base_url('assets/images/logojbu.gif'); ?>" alt="Loading..." />
  </div>

  <!-- Dynamic Content -->
  <?= $this->renderSection('content'); ?>

  <div class="modal fade" id="globalEditModal" tabindex="-1" role="dialog" aria-labelledby="globalEditLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content bg-dark text-white">
        <form id="globalEditForm">
          <div class="modal-header">
            <h5 class="modal-title" id="globalEditLabel">Edit Data</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body" id="globalEditBody">
            <!-- Dynamic form content will be injected here -->
          </div>
          <div class="modal-footer">
            <button type="submit" class="btn btn-primary">Save Changes</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Bootstrap core JavaScript - KEEP ORIGINAL ORDER -->
  <script src="<?= base_url('assets/js/jquery.min.js'); ?>"></script>
  <script src="<?= base_url('assets/js/popper.min.js'); ?>"></script>
  <script src="<?= base_url('assets/js/bootstrap.min.js'); ?>"></script>
  
  <!-- ADD Bootstrap 5 for sidebar functionality -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <!-- Simplebar JS -->
  <script src="<?= base_url('assets/plugins/simplebar/js/simplebar.js'); ?>"></script>

  <!-- Sidebar-menu JS -->
  <script src="<?= base_url('assets/js/sidebar-menu.js'); ?>"></script>

  <!-- Custom Scripts -->
  <script src="<?= base_url('assets/js/core.js'); ?>"></script>
  <script src="<?= base_url('assets/js/global.js'); ?>"></script>
  <script src="<?= base_url('assets/js/custom-functions.js'); ?>"></script>
  <script src="<?= base_url('assets/js/app-script.js'); ?>"></script>
  
  <!-- Global Search JavaScript -->
  <script src="<?= base_url('assets/js/global-search.js'); ?>"></script>

  <!-- Dynamic Scripts for Specific Pages -->
  <?= $this->renderSection('scripts'); ?>

  <!-- Modal Fix Script - Only for Bootstrap 4 modals -->
  <script>
    $(document).ready(function() {
      // Ensure Bootstrap 4 modals work properly
      console.log('jQuery version:', $.fn.jquery);
      console.log('Bootstrap 4 modal function:', typeof $.fn.modal);
      console.log('Bootstrap 5 available:', typeof bootstrap !== 'undefined');
      
      // Force Bootstrap 4 modal behavior for data-dismiss
      $(document).on('click', '[data-dismiss="modal"]', function(e) {
        e.preventDefault();
        const modal = $(this).closest('.modal');
        if (modal.length && typeof modal.modal === 'function') {
          modal.modal('hide');
        }
      });
      
      // Debug function to test modals
      window.testModal = function(modalId) {
        console.log('Testing modal:', modalId);
        $('#' + modalId).modal('show');
      };
    });
  </script>

  <script>
    document.addEventListener("DOMContentLoaded", function() {
      const currentPage = window.location.href;

      // Step 1: Loop through all dropdown menu items
      document.querySelectorAll('.sidebar-menu .dropdown-item').forEach(link => {
        if (link.href === currentPage) {
          // Step 2: Add activeliclass to <li> parent
          const liParent = link.closest('li');
          if (liParent) {
            liParent.classList.add('activeliclass');
          }

          // Step 3: Add activeaclass to the parent <a> that toggles the submenu
          const parentLink = link.closest('.collapse')?.previousElementSibling;
          if (parentLink && parentLink.matches('a')) {
            parentLink.classList.add('activeaclass');

            // KEEP ORIGINAL Bootstrap 5 syntax for sidebar
            const collapseElement = link.closest('.collapse');
            if (collapseElement && typeof bootstrap !== 'undefined') {
              new bootstrap.Collapse(collapseElement, {
                toggle: false
              }).show();
            }

            // Optional: Rotate arrow icon
            const arrow = parentLink.querySelector('.dropdown-arrow');
            if (arrow) arrow.classList.add('rotated');
          }
        }
      });
    });
  </script>

</body>

</html>
