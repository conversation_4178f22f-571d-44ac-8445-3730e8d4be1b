<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="modal fade" id="editCellModal" tabindex="-1" role="dialog" aria-labelledby="editCellModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-slideout modal-dialog-bottom" role="document" style="max-width: 40rem;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCellModalLabel">Edit Department</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editShortcutForm" enctype="multipart/form-data">
                        <input type="hidden" name="id">
                        
                        <div class="form-group mb-3">
                            <label>Title</label>
                            <input type="text" name="title" class="form-control" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>Link</label>
                            <input type="url" name="src" class="form-control" required>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>Current Icon:</label>
                            <div id="currentIconDisplay" class="mb-2"></div>
                        </div>
                        
                        <div class="form-group mb-3">
                            <label>Update Icon (optional)</label>
                            <input type="file" 
                                   name="icon" 
                                   class="form-control" 
                                   accept="image/*,.ico,.svg"
                                   data-preview="iconPreview">
                            <div id="iconPreview" class="mt-2"></div>
                            <small class="text-muted">Leave empty to keep current icon. Max size: 2MB</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">Update Shortcut</button>
                    </form>
                </div>
                <!-- <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>


                </div> -->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">


            <!-- Edit Cell Modal -->





            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Departments List</h5>
                        <div class="table-responsive">
                            <table class="table" id="listRow">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Title</th>
                                        <th scope="col">Link</th>
                                        <th scope="col">Icon</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($shortcuts)): ?>
                                        <?php $i = 1 + ($perPage * ($currentPage - 1)); ?>
                                        <?php foreach ($shortcuts as $shortcut): ?>
                                            <tr>
                                                <th scope="row"><?= $i++; ?></th>
                                                <td><?= esc($shortcut->title); ?></td>
                                                <td><?= esc($shortcut->src); ?></td>
                                                <td><img src="<?= base_url('uploads/shortcuts/') . esc($shortcut->icon); ?>" width="50px"></td>
                                                <td>
                                                    <a href="#" class="btn btn-sm btn-danger" onclick="deleteshortcut(<?= $shortcut->id; ?>)">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4">No departments found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                            <!-- Pagination (if needed) -->
                            <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
                        </div>
                    </div>
                </div>

                <!-- Edit Cell Form -->




                <!--End Dashboard Content-->

                <!--start overlay-->
                <div class="overlay toggle-menu"></div>
                <!--end overlay-->

            </div>
            <!-- End container-fluid-->

        </div><!--End content-wrapper-->
        <!--Start Back To Top Button-->
        <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
        <!--End Back To Top Button-->




    </div><!--End wrapper-->

    <!--start color switcher-->
    <?= $this->include('partials/colorswitcher'); ?>
    <!--end color switcher-->

    <?= $this->endSection(); ?>

    <?= $this->section('scripts'); ?>
    <!-- Chart.js -->
    <script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
    <script>
        $('#editCellForm').on('submit', function(e) {

            e.preventDefault();

            const formData = $(this).serialize();

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxEditDepartments", formData, function(res) {
                if (res.status === 'success') {

                    // $('#editCellForm')[0].reset(); // Optional reset
                    // setTimeout(function() {
                    //     window.location.reload();
                    // }, 1000);
                }
            });
        });

        function deleteshortcut(id) {
            if (confirm('Are you sure you want to delete this shortcut?')) {
                $.ajax({
                    url: "<?= base_url('dashboard/ajaxDeleteShortcuts'); ?>",
                    type: "POST",
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            alert('Shortcut deleted successfully');
                            window.location.reload();
                        } else {
                            alert('Failed to delete shortcut');
                        }
                    },
                    error: function() {
                        alert('Error occurred while deleting shortcut');
                    }
                });
            }
        }
    </script>
    <!-- Dashboard Index Script -->
    <script src="<?= base_url('assets/js/index.js'); ?>"></script>
    <?= $this->endSection(); ?>