<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->


            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Important Information</h4>
                    <hr>
                    <div class="row gy-3">
                        <div class="col-md-2 text-end d-grid">
                            <!-- Collapse Trigger Button -->
                            <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#bannerFormCollapse" aria-expanded="false" aria-controls="bannerFormCollapse">
                                Add Link
                            </button>
                        </div>
                    </div>

                    <!-- Collapsible Form -->
                    <div class="collapse mt-4" id="bannerFormCollapse">

                        <form class="row g-3" id="importantInformationForm" enctype="multipart/form-data">
                            <div class="col-md-6">
                                <label for="title" class="form-label">Title</label>
                                <input type="text" name="title" class="form-control" id="title" placeholder="Enter title" required>
                            </div>
                            <div class="col-md-6">
                                <label for="link" class="form-label">Link</label>
                                <input type="url" name="link" class="form-control" id="link" placeholder="Enter link" required>
                            </div>
                            <div class="col-12 mt-2">
                                <button type="submit" class="btn btn-light px-5">Submit</button>
                            </div>
                        </form>

                    </div>




                </div>

            </div>
            <div class="form-row mt-3">
                <div class="col-12">
                    <div id="todo-container">
                        <?php foreach ($importantInformation as $link): ?>
                        <div class="pb-3 todo-item" >
                            <div class="input-group">

                        
                                <input type="text" readonly="" style="color: #d7d7d7 !important" class="form-control false " aria-label="Text input with checkbox" value="<?= $link->title; ?>">
                                <input type="text" readonly="" style="color: #d7d7d7 !important" class="form-control false " aria-label="Text input with checkbox" value="<?= $link->link; ?>">
                               

                                <button  class="btn btn-outline-secondary bg-danger text-white" type="button" onclick="DeleteLink(<?= $link->id; ?>);" id="button-addon2 ">X</button>

                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>

            <div class="row">
                <div class="col-md-12">
                   <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
                </div>
            </div>


        <!--End Dashboard Content-->

        <!--start overlay-->
        <div class="overlay toggle-menu"></div>
        <!--end overlay-->

    </div>
    <!-- End container-fluid-->

</div><!--End content-wrapper-->
<!--Start Back To Top Button-->
<a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
<!--End Back To Top Button-->

<!--Start footer-->

<!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<!-- custom scripts for saving the form -->


<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
    $('#importantInformationForm').on('submit', function(e) {

        e.preventDefault();

        const formData = new FormData(this); // Automatically includes file input

        ajaxPostData("<?= base_url(); ?>dashboard/ajaxAddImportantInformation", formData, function(res) {
            if (res.status === 'success') {

                // $('#editNoticeForm')[0].reset(); // Optional reset
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }
        });
    });


    function DeleteLink(id) {
        if (confirm('Are you sure you want to delete this link?')) {
            const formData = new FormData();
            formData.append('id', id);

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxDeleteImportantInformation", formData, function(res) {
                if (res.status === 'success') {
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    alert('Failed to delete event.');
                }
            });
        }
    }
</script>

<?= $this->endSection(); ?>