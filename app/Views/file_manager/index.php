<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

<div class="clearfix"></div>

<div class="content-wrapper">
    <div class="container-fluid">
        
        <!-- File Manager Header -->
        <div class="row mb-3">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex flex-wrap justify-content-between align-items-center">
                            <div class="mb-2 mb-md-0">
                                <h4 class="mb-1"><i class="zmdi zmdi-folder-outline"></i> File Manager</h4>
                                <!-- Breadcrumbs -->
                                <nav aria-label="breadcrumb">
                                    <ol class="breadcrumb mb-0" style="background: transparent; padding-left: 0;">
                                        <?php foreach ($breadcrumbs as $index => $crumb): ?>
                                            <?php if ($index === count($breadcrumbs) - 1): ?>
                                                <li class="breadcrumb-item active" aria-current="page"><?= esc($crumb['name']) ?></li>
                                            <?php else: ?>
                                                <li class="breadcrumb-item">
                                                    <a href="?path=<?= urlencode($crumb['path']) ?>"><?= esc($crumb['name']) ?></a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endforeach; ?>
                                    </ol>
                                </nav>
                            </div>
                            
                            <!-- Storage Info -->
                            <div class="text-right">
                                <div class="storage-info">
                                    <small class="text-muted">
                                        <?= number_format($storageInfo['used'] / 1024 / 1024 / 1024, 2) ?> GB of 
                                        <?= number_format($storageInfo['total'] / 1024 / 1024 / 1024, 2) ?> GB used
                                    </small>
                                    <div class="progress mt-1" style="height: 5px; width: 200px;">
                                        <div class="progress-bar" role="progressbar" style="width: <?= $storageInfo['usage_percent'] ?>%" aria-valuenow="<?= $storageInfo['usage_percent'] ?>" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- File Manager Toolbar -->
        <div class="row mb-3">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body py-2">
                        <div class="d-flex flex-wrap justify-content-between align-items-center">
                            <div class="btn-group mb-2 mb-md-0" role="group">
                                <button type="button" class="btn btn-primary" style="display: flex;flex-direction: row;align-items: center;gap: 0.5rem;justify-content: center;align-content: center;" onclick="showUploadModal()">
                                    <i class="zmdi zmdi-upload" style="font-size: 1.2rem;"></i> <span class="d-none d-sm-inline">Upload</span>
                                </button>
                                <!-- <button type="button" class="btn btn-success" style="display: flex;flex-direction: row;align-items: center;gap: 0.5rem;justify-content: center;align-content: center;" onclick="showCreateFolderModal()">
                                    <i class="zmdi zmdi-folder-plus" style="font-size: 1.2rem;"></i> <span class="d-none d-sm-inline">New Folder</span>
                                </button>
                                <button type="button" id="renameBtn" class="btn btn-outline-secondary" style="display: flex;flex-direction: row;align-items: center;gap: 0.5rem;justify-content: center;align-content: center;" disabled title="Select one item to rename">
                                    <i class="zmdi zmdi-edit" style="font-size: 1.2rem;"></i> <span class="d-none d-sm-inline">Rename</span>
                                </button>
                                <button type="button" id="deleteBtn" class="btn btn-outline-danger" style="display: flex;flex-direction: row;align-items: center;gap: 0.5rem;justify-content: center;align-content: center;" disabled title="Select item(s) to delete">
                                    <i class="zmdi zmdi-delete" style="font-size: 1.2rem;"></i> <span class="d-none d-sm-inline">Delete</span>
                                </button> -->
                            </div>
                            
                            <div class="d-flex align-items-center">
                                <!-- View Toggle -->
                                <div class="btn-group mr-3" role="group">
                                    <a href="?path=<?= urlencode($currentPath) ?>&view=grid&sort=<?= $sort ?>&order=<?= $order ?>" 
                                       class="btn btn-sm <?= $view === 'grid' ? 'btn-primary' : 'btn-outline-primary' ?>" title="Grid View">
                                        <i class="zmdi zmdi-view-module" style="    font-size: 1.2rem;"></i>
                                    </a>
                                    <a href="?path=<?= urlencode($currentPath) ?>&view=list&sort=<?= $sort ?>&order=<?= $order ?>" 
                                       class="btn btn-sm <?= $view === 'list' ? 'btn-primary' : 'btn-outline-primary' ?>" title="List View">
                                        <i class="zmdi zmdi-view-list" style="font-size: 1.2rem;"></i>
                                    </a>
                                </div>
                                
                                <!-- Sort Options -->
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown">
                                        <i class="zmdi zmdi-sort-amount-asc"></i> Sort
                                    </button>
                                    <div class="dropdown-menu dropdown-menu-right" style="border-radius: 0.7rem; padding: 0px; overflow: hidden;">
                                        <a class="dropdown-item" href="?path=<?= urlencode($currentPath) ?>&view=<?= $view ?>&sort=name&order=asc">Name (A-Z)</a>
                                        <a class="dropdown-item" href="?path=<?= urlencode($currentPath) ?>&view=<?= $view ?>&sort=name&order=desc">Name (Z-A)</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="?path=<?= urlencode($currentPath) ?>&view=<?= $view ?>&sort=modified&order=desc">Date (Newest)</a>
                                        <a class="dropdown-item" href="?path=<?= urlencode($currentPath) ?>&view=<?= $view ?>&sort=modified&order=asc">Date (Oldest)</a>
                                        <div class="dropdown-divider"></div>
                                        <a class="dropdown-item" href="?path=<?= urlencode($currentPath) ?>&view=<?= $view ?>&sort=size&order=desc">Size (Largest)</a>
                                        <a class="dropdown-item" href="?path=<?= urlencode($currentPath) ?>&view=<?= $view ?>&sort=size&order=asc">Size (Smallest)</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- File Manager Content -->
        <div class="row">
            <div class="col-lg-12">
                <?php if (empty($files)): ?>
                    <div class="card">
                        <div class="card-body text-center py-5">
                            <i class="zmdi zmdi-folder-outline" style="font-size: 80px; color: #ced4da;"></i>
                            <h4 class="text-muted mt-3">This folder is empty</h4>
                            <p class="text-muted">Upload files or create a new folder to get started.</p>
                        </div>
                    </div>
                <?php else: ?>
                    <?php if ($view === 'grid'): ?>
                        <!-- Grid View -->
                        <div class="file-manager-grid" id="fileManagerContainer">
                            <?php foreach ($files as $file): ?>
                                <div class="file-manager-item" data-name="<?= esc($file['name']) ?>" data-type="<?= $file['type'] ?>">
                                    <div class="item-icon" onclick="openFile('<?= esc($file['name']) ?>')">
                                        <?php if ($file['type'] === 'folder'): ?>
                                            <a href="?path=<?= urlencode($currentPath . '/' . $file['name']) ?>" class="text-decoration-none">
                                                <i class="zmdi <?= $file['icon'] ?>" style="color: #ffc107;"></i>
                                            </a>
                                        <?php elseif (in_array($file['extension'], ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])): ?>
                                            <img src="<?= base_url('uploads/' . ltrim($currentPath . '/' . $file['name'], '/')) ?>" class="item-thumbnail" alt="<?= esc($file['name']) ?>">
                                        <?php else: ?>
                                            <i class="zmdi <?= $file['icon'] ?> text-muted"></i>
                                        <?php endif; ?>
                                    </div>
                                    <div class="item-name" title="<?= esc($file['name']) ?>">
                                        <?= esc($file['name']) ?>
                                    </div>
                                    <div class="item-details">
                                        <?php if ($file['type'] === 'file'): ?>
                                            <?php
                                                // *** FIX: Calculate file size directly in the view ***
                                                $bytes = $file['size'];
                                                $sizeText = '0 Bytes';
                                                if ($bytes > 0) {
                                                    $k = 1024;
                                                    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                                                    $i = floor(log($bytes) / log($k));
                                                    $sizeText = round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
                                                }
                                                echo esc($sizeText);
                                            ?>
                                            &bull; <?= date('M j, Y', $file['modified']) ?>
                                        <?php else: ?>
                                            Folder &bull; <?= date('M j, Y', $file['modified']) ?>
                                        <?php endif; ?>
                                    </div>
                                    <div class="item-actions dropdown">
                                        <button class="btn btn-sm btn-light btn-icon" style="font-size: 1.4rem;" type="button" data-toggle="dropdown">
                                            <i class="zmdi zmdi-more-vert" style="color: #898989;"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-right" style="border-radius: 0.7rem; padding: 0px; overflow: hidden;">
                                            <button class="dropdown-item" type="button" onclick="showFileInfo('<?= esc($file['name']) ?>')"><i class="zmdi zmdi-info-outline mr-2"></i>Info</button>
                                            <?php if ($file['type'] === 'file'): ?>
                                                <button class="dropdown-item" type="button" onclick="openFile('<?= esc($file['name']) ?>')"><i class="zmdi zmdi-open-in-new mr-2"></i>Open</button>
                                                <button class="dropdown-item" type="button" onclick="copyFileLink('<?= base_url('uploads/' . ltrim($currentPath . '/' . $file['name'], '/')) ?>')"><i class="zmdi zmdi-copy mr-2"></i>Copy Link</button>
                                                <a class="dropdown-item" href="<?= base_url('uploads/' . ltrim($currentPath . '/' . $file['name'], '/')) ?>" download><i class="zmdi zmdi-download mr-2"></i>Download</a>
                                            <?php endif; ?>
                                            <div class="dropdown-divider"></div>
                                            <button class="dropdown-item" type="button" disabled><i class="zmdi zmdi-edit mr-2"></i>Rename</button>
                                            <button class="dropdown-item" type="button" disabled><i class="zmdi zmdi-delete mr-2"></i>Delete</button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <!-- List View -->
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0" id="fileManagerContainer">
                                        <thead>
                                            <tr>
                                                <!-- <th width="40"><input type="checkbox" id="selectAll"></th> -->
                                                <th>Name</th>
                                                <th>Size</th>
                                                <th>Date Modified</th>
                                                <th class="text-right">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($files as $file): ?>
                                                <tr class="file-row" data-name="<?= esc($file['name']) ?>" data-type="<?= $file['type'] ?>">
                                                    <!-- <td>
                                                        <input type="checkbox" class="file-checkbox" value="<?= esc($file['name']) ?>">
                                                    </td> -->
                                                    <td>
                                                        <i class="zmdi <?= $file['icon'] ?> mr-2" style="<?= $file['type'] === 'folder' ? 'color: #ffc107;' : '' ?>"></i>
                                                        <?php if ($file['type'] === 'folder'): ?>
                                                            <a href="?path=<?= urlencode($currentPath . '/' . $file['name']) ?>"><?= esc($file['name']) ?></a>
                                                        <?php else: ?>
                                                            <a href="javascript:void(0);"  onclick="showFileInfo('<?= esc($file['name']) ?>')"><?= esc($file['name']) ?></a>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                            // *** FIX: Calculate file size directly in the view ***
                                                            if ($file['type'] === 'folder') {
                                                                echo '—';
                                                            } else {
                                                                $bytes = $file['size'];
                                                                $sizeText = '0 Bytes';
                                                                if ($bytes > 0) {
                                                                    $k = 1024;
                                                                    $sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
                                                                    $i = floor(log($bytes) / log($k));
                                                                    $sizeText = round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
                                                                }
                                                                echo esc($sizeText);
                                                            }
                                                        ?>
                                                    </td>
                                                    <td><?= date('M j, Y, g:i A', $file['modified']) ?></td>
                                                    <td class="text-right">
                                                         <div class="btn-group">
                                                            <button class="btn btn-sm btn-light" type="button" onclick="showFileInfo('<?= esc($file['name']) ?>')" title="Info"><i class="zmdi zmdi-info-outline"></i></button>
                                                            <?php if ($file['type'] === 'file'): ?>
                                                            <button class="btn btn-sm btn-light" type="button" onclick="openFile('<?= esc($file['name']) ?>')" title="Open"><i class="zmdi zmdi-open-in-new"></i></button>
                                                            <button class="btn btn-sm btn-light" type="button" onclick="copyFileLink('<?= base_url('uploads/' . ltrim($currentPath . '/' . $file['name'], '/')) ?>')" title="Copy Link"><i class="zmdi zmdi-copy"></i></button>
                                                            <a class="btn btn-sm btn-light" href="<?= base_url('uploads/' . ltrim($currentPath . '/' . $file['name'], '/')) ?>" download title="Download"><i class="zmdi zmdi-download"></i></a>
                                                            <?php endif; ?>
                                                            <button class="btn btn-sm btn-light" disabled title="Rename is disabled"><i class="zmdi zmdi-edit"></i></button>
                                                            <button class="btn btn-sm btn-light" disabled title="Delete is disabled"><i class="zmdi zmdi-delete"></i></button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div><!--End container-fluid-->
</div><!--End content-wrapper-->

<!--start overlay-->
<div class="overlay toggle-menu"></div>
<!--end overlay-->

</div><!--End wrapper-->

<!-- Modals -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content" style="background: rgba(250, 250, 255, 0.1); backdrop-filter: blur(0.7rem) saturate(1.5); border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #dddddd1f; display: flex; justify-content: space-between; align-items: center;">
                <h5 class="modal-title">Upload Files</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    <div class="form-group">
                        <label>Select Files to Upload to <strong class="text-primary"><?= esc($currentPath ?: 'Root') ?></strong></label>
                        <input type="file" class="form-control-file" name="files[]" multiple required>
                        <small class="text-muted">Max file size: 50MB. Allowed types: Images, Documents, Media, Archives.</small>
                    </div>
                    <input type="hidden" name="path" value="<?= esc($currentPath) ?>">
                </form>
                <div id="uploadProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%"></div>
                    </div>
                    <div id="uploadStatusText" class="text-center mt-1 small"></div>
                </div>
            </div>
            <div class="modal-footer" style="padding: 20px; border-top: 1px solid #dddddd1f; display: flex; justify-content: flex-end; gap: 10px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="uploadFiles()">Start Upload</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="createFolderModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Folder</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="createFolderForm">
                    <?= csrf_field() ?>
                    <div class="form-group">
                        <label>Folder Name</label>
                        <input type="text" class="form-control" name="name" required pattern="[a-zA-Z0-9\s_\-]+">
                        <input type="hidden" name="path" value="<?= esc($currentPath) ?>">
                        <small class="form-text text-muted">Only letters, numbers, spaces, underscores, and hyphens are allowed.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="createFolder()">Create</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="fileInfoModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header"><h5 class="modal-title">File Information</h5><button type="button" class="close" data-dismiss="modal"><span>&times;</span></button></div>
            <div class="modal-body" id="fileInfoContent"></div>
            <div class="modal-footer"><button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button></div>
        </div>
    </div>
</div>

<div class="modal fade" id="fileViewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: rgba(250, 250, 255, 0.1); backdrop-filter: blur(4rem) saturate(1.5); border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #dddddd1f; display: flex; justify-content: space-between; align-items: center;">
                <h5 class="modal-title" id="fileViewTitle">File Preview</h5>
                <button type="button" style="color: white;" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body" id="fileViewContent" style="text-align: center;"></div>
            <div class="modal-footer" style="padding: 20px; border-top: 1px solid #dddddd1f; display: flex; justify-content: flex-end; gap: 10px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<style>
/* Grid View Styles */
.file-manager-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(180px, 1fr)); gap: 1rem; }
.file-manager-item { position: relative; border: 1px solid #dee2e60a; border-radius: .375rem; padding: 1rem; text-align: center; transition: all 0.2s ease-in-out; background-color: #1b1b1b; }
.file-manager-item:hover { transform: translateY(-3px); box-shadow: 0 4px 12px rgba(0,0,0,0.08); border-color: #007bff;z-index: 10; }
.file-manager-item.selected { border-color: #007bff; background-color: #e7f1ff; }
.item-select { position: absolute; top: 8px; left: 8px; z-index: 2; }
.item-actions { position: absolute; top: 5px; right: 5px;}
.item-actions .btn-icon { border: none; background: transparent; }
.item-icon { height: 80px; display: flex; align-items: center; justify-content: center; margin-bottom: 0.5rem; }
.item-icon .zmdi { font-size: 60px; line-height: 1; }
.item-thumbnail { max-width: 100%; max-height: 80px; object-fit: contain; }
.item-name { font-weight: 500; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-bottom: 0.25rem; }
.item-details { font-size: 0.75rem; color: #6c757d; }
/* List View Styles */
.file-row { vertical-align: middle; }
.file-row.selected { background-color: #e7f1ff; }
.file-row .zmdi { font-size: 1.25rem; vertical-align: middle; }
.file-row a { color: #a5b2bf; text-decoration: none; }
.file-row a:hover { color: #007bff; }
/* General Styles */
.storage-info .progress { background-color: #e9ecef; }
.storage-info .progress-bar { background-color: #007bff; }
.btn-outline-danger { color: #dc3545; border-color: #dc3545; }
.btn-outline-danger:hover { color: #fff; background-color: #dc3545; border-color: #dc3545; }
.btn:disabled, button:disabled { cursor: not-allowed; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Selection logic remains the same
    const container = document.getElementById('fileManagerContainer');
    if (!container) return;
    let selectedFiles = [];
    container.addEventListener('click', function(e) { if (e.target.classList.contains('file-checkbox')) { const item = e.target.closest('.file-manager-item, .file-row'); toggleSelection(item, e.target.checked); } });
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) { selectAllCheckbox.addEventListener('change', function() { document.querySelectorAll('.file-checkbox').forEach(cb => { cb.checked = this.checked; const item = cb.closest('.file-manager-item, .file-row'); toggleSelection(item, this.checked); }); }); }
    function toggleSelection(item, isSelected) { if (!item) return; const name = item.dataset.name; item.classList.toggle('selected', isSelected); if (isSelected) { if (!selectedFiles.includes(name)) selectedFiles.push(name); } else { selectedFiles = selectedFiles.filter(f => f !== name); } updateActionButtons(); }
    function updateActionButtons() { const renameBtn = document.getElementById('renameBtn'); const deleteBtn = document.getElementById('deleteBtn'); if (renameBtn) renameBtn.disabled = selectedFiles.length !== 1; if (deleteBtn) deleteBtn.disabled = selectedFiles.length === 0; }
});

// --- MODAL & AJAX FUNCTIONS ---
function showUploadModal() { $('#uploadModal').modal('show'); }
function showCreateFolderModal() { $('#createFolderModal').modal('show'); }

// Upload function remains the same
function uploadFiles() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData(form);
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const statusText = document.getElementById('uploadStatusText');
    const fileInput = form.querySelector('input[type="file"]');
    const files = fileInput.files;

    if (files.length === 0) { showToast('Please select files to upload', 'error'); return; }
    progressDiv.style.display = 'block';
    
    let uploadedCount = 0;
    let totalFiles = files.length;
    let errors = [];

    Array.from(files).forEach(file => {
        const singleFileFormData = new FormData();
        singleFileFormData.append('file', file);
        fetch('<?= base_url('api/v1/media/upload') ?>', { method: 'POST', body: singleFileFormData })
        .then(response => response.json())
        .then(data => { if (data.status === 'success') { return organizeFileToDirectory(data.data.url, formData.get('path')); } else { throw new Error(data.message || `Upload failed for ${file.name}`); } })
        .catch(error => { console.error('Error with file:', file.name, error); errors.push(`${file.name}: ${error.message}`); })
        .finally(() => {
            uploadedCount++;
            const progress = (uploadedCount / totalFiles) * 100;
            progressBar.style.width = progress + '%';
            statusText.textContent = `Processing ${uploadedCount} of ${totalFiles}...`;
            if (uploadedCount === totalFiles) {
                if (errors.length === 0) { showToast(`${totalFiles} file(s) uploaded successfully!`, 'success'); } else { showToast(`${totalFiles - errors.length} files uploaded, ${errors.length} failed.`, 'warning'); }
                setTimeout(() => location.reload(), 1500);
            }
        });
    });
}
function organizeFileToDirectory(mediaUrl, targetPath) {
    const fileName = mediaUrl.split('/').pop();
    const sourcePath = mediaUrl.replace('<?= base_url() ?>', '').replace(/^\//, '');
    return fetch('<?= base_url('dashboard/filemanager/organizeFile') ?>', { method: 'POST', headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' }, body: JSON.stringify({ '<?= csrf_token() ?>': '<?= csrf_hash() ?>', sourcePath: sourcePath, targetPath: targetPath, fileName: fileName }) }).then(res => res.json()).then(data => { if (!data.success) throw new Error(data.message || 'Failed to move file.'); return data; });
}
function createFolder() {
    const form = document.getElementById('createFolderForm');
    const formData = new FormData(form);
    fetch('<?= base_url('dashboard/filemanager/createFolder') ?>', { method: 'POST', body: formData })
    .then(response => response.json())
    .then(data => { if (data.success) { showToast(data.message, 'success'); setTimeout(() => location.reload(), 1500); } else { throw new Error(data.message || 'An unknown error occurred.'); } })
    .catch(error => { showToast(error.message, 'error'); }).finally(() => { $('#createFolderModal').modal('hide'); });
}

// *** FIX: Use the formatFileSize helper in the info modal ***
function showFileInfo(filename) {
    const url = `<?= base_url('dashboard/filemanager/getFileInfo') ?>?file=${encodeURIComponent(filename)}&path=<?= urlencode($currentPath) ?>`;
    fetch(url)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const info = data.info;
            const content = `
                <div class="row">
                    <div class="col-4"><strong>Name:</strong></div><div class="col-8 text-break">${info.name}</div>
                    <div class="col-4"><strong>Size:</strong></div><div class="col-8">${formatFileSize(info.size)}</div>
                    <div class="col-4"><strong>Type:</strong></div><div class="col-8">${info.type}</div>
                    <div class="col-4"><strong>Modified:</strong></div><div class="col-8">${new Date(info.modified * 1000).toLocaleString()}</div>
                    ${info.dimensions ? `<div class="col-4"><strong>Dimensions:</strong></div><div class="col-8">${info.dimensions}</div>` : ''}
                    <div class="col-4"><strong>URL:</strong></div><div class="col-8"><a href="${info.url}" target="_blank" class="text-break">View/Download File</a></div>
                </div>`;
            document.getElementById('fileInfoContent').innerHTML = content;
            $('#fileInfoModal').modal('show');
        } else { throw new Error(data.message); }
    })
    .catch(error => showToast('Failed to load file information. ' + error.message, 'error'));
}

function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
function copyFileLink(url) {
    navigator.clipboard.writeText(url).then(() => {
        showToast('Link copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showToast('Link copied to clipboard!', 'success');
    });
}

function openFile(filename) {
    const fileUrl = `<?= base_url('uploads/') ?>${('<?= $currentPath ?>' ? '<?= $currentPath ?>/' : '')}${filename}`;
    const extension = filename.split('.').pop().toLowerCase();
    
    document.getElementById('fileViewTitle').textContent = filename;
    
    let content = '';
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
        content = `<img src="${fileUrl}" class="img-fluid" style="max-height: 500px;" alt="${filename}">`;
    } else if (['pdf'].includes(extension)) {
        content = `<embed src="${fileUrl}" width="100%" height="500px" type="application/pdf">`;
    } else if (['mp4', 'webm', 'ogg'].includes(extension)) {
        content = `<video controls style="max-width: 100%; max-height: 500px;"><source src="${fileUrl}" type="video/${extension}">Your browser does not support the video tag.</video>`;
    } else if (['mp3', 'wav', 'ogg'].includes(extension)) {
        content = `<audio controls style="width: 100%;"><source src="${fileUrl}" type="audio/${extension}">Your browser does not support the audio tag.</audio>`;
    } else {
        content = `<div class="text-center"><i class="zmdi zmdi-file" style="font-size: 80px; color: #6c757d;"></i><p class="mt-3">Preview not available for this file type.</p><a href="${fileUrl}" class="btn btn-primary" download>Download File</a></div>`;
    }
    
    document.getElementById('fileViewContent').innerHTML = content;
    $('#fileViewModal').modal('show');
}

function showToast(message, type = 'info') {
    const colors = { success: '#4CAF50', error: '#f44336', warning: '#ff9800', info: '#2196F3' };
    Toastify({ text: message, duration: 3000, gravity: "top", position: "right", backgroundColor: colors[type] || colors.info }).showToast();
}
</script>
<?= $this->endSection(); ?>