<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-4">
                <?php foreach ($users as $user): ?>
                    <div class="col">
                        <div class="card radius-15">
                            <div class="card-body text-center">
                                <div class="p-4 radius-15 text-center">
                                    <img src="<?= base_url('uploads/users/') . esc($user->profile_picture) ?>"
                                        width="110" height="110"
                                        class="rounded-circle shadow mb-3"
                                        alt="<?= esc($user->username) ?>">

                                    <h5 class="fw-bold mb-1"><?= esc($user->username) ?></h5>
                                    <p class="mb-2"><?= esc($user->email) ?></p>

                                    <ul class="list-unstyled small mb-3">
                                        <li><strong>Status:</strong> <?= esc($user->status) ?></li>
                                        <li><strong>Department:</strong> <?= esc($user->department_name) ?></li>
                                    </ul>

                                    <div class="d-grid">
                                        <a href="mailto:<?= esc($user->email) ?>" class="btn btn-outline-primary radius-15">
                                            <i class="zmdi zmdi-edit"></i> Edit Details
                                        </a>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>


            </div>
            <!--End Dashboard Content-->
            <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>

<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>