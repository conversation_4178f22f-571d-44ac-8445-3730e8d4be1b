<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-6 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Add Users</h5>
                        <form method="post" id="addUserForm" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="password">Password</label>
                                <input type="password" class="form-control" id="password" placeholder="password will be generated via system" name="password" disabled>
                            </div>
                            <div class="form-group">
                                <label for="profile_image">Profile Image</label>
                                <input type="file" class="form-control-file" id="profile_image" name="profile_image" accept="image/*">
                            </div>
                            <div class="form-group">
                                <label for="role">Role</label>
                                <select class="form-control" id="role" name="role" required>
                                    <option value="">Select Role</option>
                                    <option value="superadmin">Super Admin</option>
                                    <option value="departmentadmin">Department Admin</option>
                                    <option value="editor">Editor</option>
                                    <option value="contributor">Contributor</option>
                                </select>
                            </div>
                            <div class="form-group" id="department-group" style="display:none;">
                                <label for="department">Department</label>
                                <select class="form-control" id="department" name="department">
                                    <option value="">Select Department</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= esc($dept['id']) ?>"><?= esc($dept['department_name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>


                            <button type="submit" class="btn btn-primary">Add User</button>
                        </form>
                    </div>
                </div>
            </div>



            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script>
    $(document).ready(function() {
        $('#department').select2({
            placeholder: "Select Department",
            allowClear: true,
            width: '100%'
        });
    });
</script>
<script>
    $('#role').on('change', function() {
        if ($(this).val() === 'departmentadmin' || $(this).val() === 'editor') {
            $('#department-group').stop(true, true).fadeIn(300);
            $('#department').attr('required', true);
        } else {
            $('#department-group').stop(true, true).fadeOut(300);
            $('#department').attr('required', false);
        }
    });
</script>
<script>
    $('#addUserForm').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this); // Automatically includes file input

        ajaxPostData("<?= base_url(); ?>dashboard/ajaxAddUsers", formData, function(res) {
            if (res.status === 'success') {

                // $('#addUserForm')[0].reset(); // Optional reset
                setTimeout(function() {
                    // window.location.reload();
                }, 1000);
            }
        });
    });
</script>
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>