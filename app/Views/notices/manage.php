<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="modal fade" id="editCellModal" tabindex="-1" role="dialog" aria-labelledby="editCellModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-slideout modal-dialog-bottom" role="document" style="max-width: 40rem;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCellModalLabel" style="color: #000;">Edit Notices</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="modalLoader" style="text-align:center; padding: 30px;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <p>Loading notice data...</p>
                    </div>
                    <form id="editNoticeForm" enctype="multipart/form-data" method="post">
                        <input type="hidden" name="id">
                        <div class="form-group">
                            <label>Title</label>
                            <input type="text" name="title" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Description</label>
                            <textarea name="description" class="form-control"></textarea>
                        </div>
                        <div class="form-group">
                            <label>Notice File</label>
                            <input type="file" name="file" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>Existing File :</label>
                            <a href="#" class="btn btn-success" name="fileview" target="_blank" style="display: none;">View File</a>
                        </div>

                        <div class="form-group">
                            <label for="approval_status">Approval Status</label>
                            <select class="form-control" id="approval_status" name="approval_status" required>
                                <option value="">Select Approval Status</option>
                                <option value="1">Approved</option>
                                <option value="0">Pending</option>
                                <option value="2">Rejected</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary text-right">Update Notice</button>
                    </form>

                </div>
                <!-- <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>


                </div> -->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">


            <!-- Edit Cell Modal -->





            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">

                        <!-- <a href="#" id="addnotice" class="btn btn-primary mb-3">Add Notice</a> -->

                        <h5 class="card-title">Notices List</h5>
                        <div class="table-responsive">
                            <table class="table" id="listRow">
                                <thead>
                                    <tr>
                                        <th scope="col">Notice Title</th>
                                        <th scope="col">Description</th>
                                        <th scope="col">File</th>
                                        <th scope="col">Department</th>
                                        <th scope="col">Notice Type</th>
                                        <th scope="col">Created at</th>
                                        <th scope="col">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($notices)): ?>
                                        <?php foreach ($notices as $notice): ?>
                                            <tr>
                                                <td><?= esc($notice->title); ?></td>
                                                <td><?= esc($notice->description); ?></td>
                                                <td>
                                                    <?php if ($notice->files): ?>
                                                        <a href="<?= base_url('uploads/notices/' . $notice->files); ?>" target="_blank">View File</a>
                                                    <?php else: ?>
                                                        No File
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= esc($notice->department_name) == null ? 'General' : esc($notice->department_name); ?></td>
                                                <td><?= esc($notice->notice_type); ?></td>
                                                <td><?= esc($notice->created_at); ?></td>
                                                <td>
                                                    <a href="#" class="btn btn-sm btn-primary"
                                                        data-id="<?= $notice->id; ?>"
                                                        data-toggle="modal"
                                                        onclick="fillnoticemodal(this)">
                                                        Edit
                                                    </a>

                                                    <a href="#" data-id="<?= $notice->id?>" class="btn btn-sm btn-danger" onclick="deleteNotice(this)">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4">No notices found.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Pagination (if needed) -->
            <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
        </div>
    </div>
</div>

<!-- Edit Cell Form -->




<!--End Dashboard Content-->

<!--start overlay-->
<div class="overlay toggle-menu"></div>
<!--end overlay-->

</div>
<!-- End container-fluid-->

</div><!--End content-wrapper-->
<!--Start Back To Top Button-->
<a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
<!--End Back To Top Button-->



</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script>
    function fillnoticemodal(element) {
        const noticeId = element.getAttribute('data-id');

        // Show modal + loader, hide form
        $('#editCellModal').modal('show');
        document.getElementById('modalLoader').style.display = 'block';
        document.getElementById('editNoticeForm').style.display = 'none';

        fetch('<?= site_url('dashboard/getNoticeData'); ?>/' + noticeId)
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    const notice = result.data;

                    // Fill form fields
                    document.querySelector('#editNoticeForm input[name="id"]').value = notice.id;
                    document.querySelector('#editNoticeForm input[name="title"]').value = notice.title;
                    document.querySelector('#editNoticeForm textarea[name="description"]').value = notice.description;
                    document.querySelector('#editNoticeForm select[name="approval_status"]').value = String(notice.isApproved);

                    const fileLink = document.querySelector('#editNoticeForm a[name="fileview"]');
                    if (notice.files) {
                        fileLink.href = '<?= base_url('uploads/notices/'); ?>' + notice.files;
                        fileLink.textContent = 'View File';
                        fileLink.style.display = 'inline-block';
                    } else {
                        fileLink.href = '#';
                        fileLink.textContent = 'No File';
                        fileLink.style.display = 'none';
                    }

                    // Hide loader and show form
                    document.getElementById('modalLoader').style.display = 'none';
                    document.getElementById('editNoticeForm').style.display = 'block';
                } else {
                    alert(result.message);
                }
            })
            .catch(error => {
                console.error('AJAX error:', error);
                alert('Something went wrong while loading notice data.');
            });
    }
</script>


<script>
    $('#editNoticeForm').on('submit', function(e) {

        e.preventDefault();

        const formData = new FormData(this); // Automatically includes file input

        ajaxPostData("<?= base_url(); ?>dashboard/ajaxEditNotices", formData, function(res) {
            if (res.status === 'success') {

                // $('#editNoticeForm')[0].reset(); // Optional reset
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }
        });
    });

    function deleteNotice(element) {
        const noticeId = element.getAttribute('data-id');
        if (confirm('Are you sure you want to delete this notice?')) {
            fetch('<?= site_url('dashboard/deleteNotice'); ?>/' + noticeId, {
                method: 'GET'
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    alert(result.message);
                    window.location.reload();
                } else {
                    alert(result.message);
                }
            })
            .catch(error => {
                console.error('Error deleting notice:', error);
                alert('Something went wrong while deleting the notice.');
            });
        }
    }

</script>
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>