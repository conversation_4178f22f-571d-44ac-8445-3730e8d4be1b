<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-6 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Add notice</h5>
                        <form id="addnoticeForm" enctype="multipart/form-data">
                            <div class="form-group">
                                <label for="notice_title">notice Title</label>
                                <input type="text" class="form-control" id="notice_title" name="notice_title" required>
                            </div>


                            <div class="form-group">
                                <label for="notice_description">notice Description</label>
                                <textarea class="form-control" id="notice_description" name="notice_description" rows="3" required></textarea>


                            </div>

                            <div class="form-group">
                                <label for="notice_type">Notice Type</label>
                                <select class="form-control" id="notice_type" name="notice_type" required>
                                    <option value="">Select Notice Type</option>
                                    <option value="department">Department Notice</option>
                                    <option value="general">General Notice</option>
                                    <option value="result">Results Notice</option>
                                    <option value="important">Important Notice</option>
                                </select>
                            </div>

                           

                            <div class="form-group" id="department-group" style="display:none;">
                                <label for="department">Department</label>
                                <select class="form-control" id="department" name="department">
                                    <option value="">Select Department</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= esc($dept['id']) ?>"><?= esc($dept['department_name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                             <div class="form-group">
                                <label for="notice_image">notice files (Optional)</label>
                                <input type="file" class="form-control-file" id="notice_files" name="notice_image" accept="image/*">
                                <div class="mt-2">
                                    <img id="notice_image_preview" src="#" alt="Image Preview" style="display:none; max-width: 200px; max-height: 200px;" />
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="notice_link">Notice Link (Optional)</label>
                                <div class="custom-dropdown" data-link-id="notice">
                                    <div class="custom-dropdown-selected" onclick="toggleLinkDropdown('notice')">
                                        <span id="selectedLinkText_notice">Upload File</span>
                                        <i class="fa fa-chevron-down dropdown-arrow" id="dropdownLinkArrow_notice"></i>
                                    </div>
                                    <div class="custom-dropdown-options" id="dropdownLinkOptions_notice">
                                        <div class="custom-dropdown-option" data-value="upload" onclick="selectLinkDropdownOption('notice', 'upload', 'Upload File')">
                                            <i class="fa fa-upload"></i> Upload File
                                        </div>
                                        <div class="custom-dropdown-option" data-value="url" onclick="selectLinkDropdownOption('notice', 'url', 'Paste URL')">
                                            <i class="fa fa-link"></i> Paste URL
                                        </div>
                                        <div class="custom-dropdown-option" data-value="filemanager" onclick="selectLinkDropdownOption('notice', 'filemanager', 'Choose from File Manager')">
                                            <i class="fa fa-folder-open"></i> Choose from File Manager
                                        </div>
                                        <div class="custom-dropdown-option" data-value="page" onclick="selectLinkDropdownOption('notice', 'page', 'Choose Page')">
                                            <i class="fa fa-file-text"></i> Choose Page
                                        </div>
                                    </div>
                                    <input type="hidden" id="linkSourceType_notice" value="upload">
                                </div>
                            </div>

                            <div class="form-group" id="uploadLinkSection_notice">
                                <label for="notice_link_file">Choose Link File</label>
                                <input type="file" id="notice_link_file" class="form-control" accept=".pdf,.doc,.docx,.txt">
                            </div>

                            <div class="form-group" id="urlLinkSection_notice" style="display: none;">
                                <label for="notice_link_url">Link URL</label>
                                <input type="url" id="notice_link_url" class="form-control" placeholder="https://example.com" name="notice_link_url">
                            </div>

                            <div class="form-group" id="fileManagerLinkSection_notice" style="display: none;">
                                <label for="notice_link_filemanager">Choose from File Manager</label>
                                <div class="input-group">
                                    <input type="text" id="notice_link_filemanager" class="form-control" placeholder="Selected file will appear here..." readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="openLinkFileManagerPicker('notice')">
                                        <i class="fa fa-folder-open"></i> Browse
                                    </button>
                                </div>
                            </div>

                            <div class="form-group" id="pageLinkSection_notice" style="display: none;">
                                <label for="notice_link_page">Choose Page</label>
                                <select id="notice_link_page" class="form-control" name="notice_link_page">
                                    <option value="">Loading pages...</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary">Add notice</button>
                        </form>
                    </div>
                </div>
            </div>



            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('styles'); ?>
<style>
/* Custom Dropdown Styles for Link Field */
.custom-dropdown {
    position: relative;
    width: 100%;
}

.custom-dropdown-selected {
    background: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.575rem 0.75rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
    user-select: none;
}

.custom-dropdown-selected:hover {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.custom-dropdown-selected.active {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.dropdown-arrow {
    transition: transform 0.2s ease;
}

.dropdown-arrow.rotated {
    transform: rotate(180deg);
}

.custom-dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ced4da;
    border-top: none;
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.custom-dropdown-options.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.custom-dropdown-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.custom-dropdown-option:hover {
    background-color: #f8f9fa;
}

.custom-dropdown-option:active {
    background-color: #e9ecef;
}

.custom-dropdown-option i {
    width: 16px;
    color: #6c757d;
}
</style>
<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>

<script>
    // Link dropdown functionality
    let pagesData = [];

    // Initialize page when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure all dropdowns are closed initially
        document.querySelectorAll('.custom-dropdown-options').forEach((options, index) => {
            options.classList.remove('show');
            options.style.display = 'none';
        });

        // Ensure all arrows are not rotated
        document.querySelectorAll('.dropdown-arrow').forEach(arrow => {
            arrow.classList.remove('rotated');
        });

        // Ensure all selected elements are not active
        document.querySelectorAll('.custom-dropdown-selected').forEach(selected => {
            selected.classList.remove('active');
        });

        // Load pages for dropdown
        loadPagesForDropdown();
    });

    function loadPagesForDropdown() {
        fetch('<?= base_url('api/v1/pages') ?>')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    pagesData = data.data;
                    populatePageDropdown();
                }
            })
            .catch(error => {
                console.error('Error loading pages:', error);
            });
    }

    function populatePageDropdown() {
        const pageSelect = document.getElementById('notice_link_page');
        pageSelect.innerHTML = '<option value="">Select a page...</option>';

        pagesData.forEach(page => {
            const option = document.createElement('option');
            option.value = page.id;
            option.textContent = page.pageName;
            option.dataset.pageName = page.pageName;
            pageSelect.appendChild(option);
        });
    }

    function toggleLinkDropdown(id) {
        const options = document.getElementById('dropdownLinkOptions_' + id);
        const arrow = document.getElementById('dropdownLinkArrow_' + id);
        const selected = options.parentElement.querySelector('.custom-dropdown-selected');

        if (!options || !arrow || !selected) {
            console.error('Missing dropdown elements for:', id);
            return;
        }

        // Close all other dropdowns first
        document.querySelectorAll('.custom-dropdown-options').forEach(opt => {
            if (opt.id !== 'dropdownLinkOptions_' + id) {
                opt.classList.remove('show');
                opt.style.display = 'none';
                const otherArrow = opt.parentElement.querySelector('.dropdown-arrow');
                const otherSelected = opt.parentElement.querySelector('.custom-dropdown-selected');
                if (otherArrow) otherArrow.classList.remove('rotated');
                if (otherSelected) otherSelected.classList.remove('active');
            }
        });

        // Toggle current dropdown
        if (options.classList.contains('show')) {
            options.classList.remove('show');
            options.style.display = 'none';
            arrow.classList.remove('rotated');
            selected.classList.remove('active');
        } else {
            options.classList.add('show');
            options.style.display = 'block';
            arrow.classList.add('rotated');
            selected.classList.add('active');
        }
    }

    function selectLinkDropdownOption(id, value, text) {
        const selectedText = document.getElementById('selectedLinkText_' + id);
        const hiddenInput = document.getElementById('linkSourceType_' + id);
        const options = document.getElementById('dropdownLinkOptions_' + id);
        const arrow = document.getElementById('dropdownLinkArrow_' + id);
        const selected = options.parentElement.querySelector('.custom-dropdown-selected');

        // Update text and value
        selectedText.textContent = text;
        hiddenInput.value = value;

        // Close dropdown
        options.classList.remove('show');
        options.style.display = 'none';
        arrow.classList.remove('rotated');
        selected.classList.remove('active');

        // Toggle sections based on selection
        toggleLinkSource(id);
    }

    function toggleLinkSource(id) {
        const sourceType = document.getElementById('linkSourceType_' + id).value;
        const uploadSection = document.getElementById('uploadLinkSection_' + id);
        const urlSection = document.getElementById('urlLinkSection_' + id);
        const fileManagerSection = document.getElementById('fileManagerLinkSection_' + id);
        const pageSection = document.getElementById('pageLinkSection_' + id);

        // Hide all sections first
        if (uploadSection) uploadSection.style.display = 'none';
        if (urlSection) urlSection.style.display = 'none';
        if (fileManagerSection) fileManagerSection.style.display = 'none';
        if (pageSection) pageSection.style.display = 'none';

        // Show relevant section
        if (sourceType === 'upload' && uploadSection) {
            uploadSection.style.display = 'block';
        } else if (sourceType === 'url' && urlSection) {
            urlSection.style.display = 'block';
        } else if (sourceType === 'filemanager' && fileManagerSection) {
            fileManagerSection.style.display = 'block';
        } else if (sourceType === 'page' && pageSection) {
            pageSection.style.display = 'block';
        }
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.custom-dropdown')) {
            document.querySelectorAll('.custom-dropdown-options').forEach(options => {
                options.classList.remove('show');
                options.style.display = 'none';
                const arrow = options.parentElement.querySelector('.dropdown-arrow');
                const selected = options.parentElement.querySelector('.custom-dropdown-selected');
                if (arrow) arrow.classList.remove('rotated');
                if (selected) selected.classList.remove('active');
            });
        }
    });

    // Handle page selection
    document.addEventListener('change', function(event) {
        if (event.target.id === 'notice_link_page') {
            const selectedOption = event.target.options[event.target.selectedIndex];
            if (selectedOption.value) {
                const pageName = selectedOption.dataset.pageName;
                const pageId = selectedOption.value;
                // Generate the live page URL
                const livePageUrl = `https://jb-college.vercel.app/pages/${pageName.replace(/\s+/g, '-')}?id=${pageId}`;

                // Store the URL in a hidden field or use it as needed
                let hiddenUrlField = document.getElementById('notice_page_url');
                if (!hiddenUrlField) {
                    hiddenUrlField = document.createElement('input');
                    hiddenUrlField.type = 'hidden';
                    hiddenUrlField.id = 'notice_page_url';
                    hiddenUrlField.name = 'notice_page_url';
                    document.getElementById('addnoticeForm').appendChild(hiddenUrlField);
                }
                hiddenUrlField.value = livePageUrl;
            }
        }
    });

    function openLinkFileManagerPicker(id) {
        // Placeholder for file manager functionality
        alert('File manager integration would go here');
    }
</script>

<script>
    $(document).ready(function() {
        $('#department').select2({
            placeholder: "Select Department",
            allowClear: true,
            width: '100%'
        });
    });
</script>
<script>
    $('#notice_type').on('change', function() {
        if ($(this).val() === 'department') {
            $('#department-group').stop(true, true).fadeIn(300);
            $('#department').attr('required', true);
        } else {
            $('#department-group').stop(true, true).fadeOut(300);
            $('#department').attr('required', false);
        }
    });
</script>


<script>
    document.getElementById('notice_image').addEventListener('change', function(event) {
        const [file] = event.target.files;
        const preview = document.getElementById('notice_image_preview');
        if (file) {
            preview.src = URL.createObjectURL(file);
            preview.style.display = 'block';
        } else {
            preview.src = '#';
            preview.style.display = 'none';
        }
    });
</script>
<!-- custom scripts for saving the form -->
<script>
    $('#addnoticeForm').on('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this); // Automatically includes file input

        // Handle link data based on source type
        const linkSourceType = document.getElementById('linkSourceType_notice').value;

        if (linkSourceType === 'upload') {
            const linkFileInput = document.getElementById('notice_link_file');
            if (linkFileInput.files.length > 0) {
                formData.append('notice_link_file', linkFileInput.files[0]);
                formData.append('link_source_type', 'upload');
            }
        } else if (linkSourceType === 'url') {
            const linkUrlInput = document.getElementById('notice_link_url');
            if (linkUrlInput.value.trim()) {
                formData.append('notice_link_url', linkUrlInput.value.trim());
                formData.append('link_source_type', 'url');
            }
        } else if (linkSourceType === 'filemanager') {
            const linkFileManagerInput = document.getElementById('notice_link_filemanager');
            if (linkFileManagerInput.value.trim()) {
                formData.append('notice_link_url', linkFileManagerInput.value.trim());
                formData.append('link_source_type', 'filemanager');
            }
        } else if (linkSourceType === 'page') {
            const pageUrlInput = document.getElementById('notice_page_url');
            const pageSelectInput = document.getElementById('notice_link_page');
            if (pageUrlInput && pageUrlInput.value.trim()) {
                formData.append('notice_link_url', pageUrlInput.value.trim());
                formData.append('link_source_type', 'page');
                formData.append('selected_page_id', pageSelectInput.value);
            }
        }

        ajaxPostData("<?= base_url(); ?>dashboard/ajaxAddNotice", formData, function(res) {
            if (res.status === 'success') {
                $('#addnoticeForm')[0].reset(); // Optional reset

                // Reset custom dropdown
                document.getElementById('selectedLinkText_notice').textContent = 'Upload File';
                document.getElementById('linkSourceType_notice').value = 'upload';
                toggleLinkSource('notice');

                // Clear hidden URL field
                const hiddenUrlField = document.getElementById('notice_page_url');
                if (hiddenUrlField) {
                    hiddenUrlField.value = '';
                }

                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }
        });
    });
</script>
<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>