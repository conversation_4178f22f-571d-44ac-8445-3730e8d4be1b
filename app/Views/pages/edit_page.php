<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="card-title d-flex justify-content-between align-items-center">
                                <h4>📄 Edit Page: <?= esc($page['pageName']) ?></h4>
                                <div>
                                    <a href="<?= base_url('api/v1/pages/name/' . urlencode($page['pageName'])) ?>" 
                                       target="_blank" class="btn btn-info">
                                        <i class="fa fa-external-link"></i> View API
                                    </a>
                                    <a href="<?= base_url('dashboard/pages') ?>" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left"></i> Back to Pages
                                    </a>
                                </div>
                            </div>
                            <hr>

                            <form id="editPageForm">
                                <input type="hidden" id="pageId" value="<?= $page['id'] ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="pageName">Page Name *</label>
                                            <input type="text" class="form-control" id="pageName" name="pageName" 
                                                   value="<?= esc($page['pageName']) ?>" required>
                                            <small class="form-text text-muted">This will be used in the URL: /api/v1/pages/name/your-page-name</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Page Info</label>
                                            <div class="form-control-plaintext">
                                                <small class="text-muted">
                                                    Created: <?= date('M j, Y g:i A', strtotime($page['created_at'])) ?><br>
                                                    Updated: <?= date('M j, Y g:i A', strtotime($page['updated_at'])) ?>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="pageData">Page Data (JSON) *</label>
                                    <textarea class="form-control" id="pageData" name="pageData" rows="20" required><?= esc(json_encode($page['pageData'], JSON_PRETTY_PRINT)) ?></textarea>
                                    <small class="form-text text-muted">Enter the page structure as JSON array</small>
                                </div>

                                <div class="form-group">
                                    <button type="button" class="btn btn-secondary" onclick="validateJSON()">
                                        <i class="fa fa-check"></i> Validate JSON
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="resetToOriginal()">
                                        <i class="fa fa-undo"></i> Reset Changes
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> Update Page
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i></a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <?= $this->include('partials/footer'); ?>
    <!--End footer-->

</div>
<!--End wrapper-->

<script>
const originalPageData = <?= json_encode($page['pageData']) ?>;

function validateJSON() {
    const jsonText = document.getElementById('pageData').value;
    try {
        JSON.parse(jsonText);
        alert('✅ Valid JSON!');
    } catch (e) {
        alert('❌ Invalid JSON: ' + e.message);
    }
}

function resetToOriginal() {
    if (confirm('Are you sure you want to reset all changes?')) {
        document.getElementById('pageName').value = '<?= esc($page['pageName']) ?>';
        document.getElementById('pageData').value = JSON.stringify(originalPageData, null, 2);
    }
}

document.getElementById('editPageForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const pageId = document.getElementById('pageId').value;
    const pageName = document.getElementById('pageName').value;
    const pageDataText = document.getElementById('pageData').value;
    
    let pageData;
    try {
        pageData = JSON.parse(pageDataText);
    } catch (e) {
        alert('Invalid JSON: ' + e.message);
        return;
    }
    
    const data = {
        pageName: pageName,
        pageData: pageData
    };
    
    console.log('Updating page:', pageId, data);
    
    fetch(`${BASE_URL}api/v1/pages/update/${pageId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Update response status:', response.status);
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('Update response data:', data);
        if (data.status === 'success') {
            alert('✅ Page updated successfully!');
            location.reload();
        } else {
            alert('❌ Update Error: ' + (data.message || 'Failed to update page'));
        }
    })
    .catch(error => {
        console.error('Update Error:', error);
        alert('❌ Update Failed:\n\n' + error.message);
    });
});
</script>

<?= $this->endSection(); ?>