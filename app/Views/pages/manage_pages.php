<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="card-title d-flex justify-content-between align-items-center">
                                <h4>📄 Manage Pages</h4>
                                <div>
                                    <a href="<?= base_url('dashboard/pages/visual-editor') ?>" class="btn btn-success">
                                        <i class="fa fa-paint-brush"></i> Visual Builder
                                    </a>
                                    <a href="<?= base_url('dashboard/pages/create') ?>" class="btn btn-primary">
                                        <i class="fa fa-plus"></i> Create New Page
                                    </a>
                                </div>
                            </div>
                            <hr>
                            
                            <!-- Search Bar -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <?php if (!empty($searchTerm)): ?>
                                        <div class="alert alert-info">
                                            <i class="fa fa-search"></i> 
                                            Showing search results for: <strong>"<?= esc($searchTerm) ?>"</strong>
                                            <a href="<?= base_url('dashboard/pages') ?>" class="btn btn-sm btn-outline-secondary ml-2">
                                                <i class="fa fa-times"></i> Clear Search
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <form method="GET" action="<?= base_url('dashboard/pages') ?>">
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="search" 
                                                   placeholder="Search pages..." value="<?= esc($searchTerm ?? '') ?>">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary" type="submit">
                                                    <i class="fa fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped" id="pagesTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Page Name</th>
                                            <th>Created</th>
                                            <th>Updated</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($pages)): ?>
                                            <?php foreach ($pages as $page): ?>
                                                <tr>
                                                    <td><?= $page['id'] ?></td>
                                                    <td>
                                                        <strong><?= esc($page['pageName']) ?></strong>
                                                        <br>
                                                        <small class="text-muted">
                                                            <a href="<?= base_url('api/v1/pages/name/' . urlencode($page['pageName'])) ?>" target="_blank">
                                                                View API
                                                            </a>
                                                        </small>
                                                    </td>
                                                    <td><?= date('M j, Y g:i A', strtotime($page['created_at'])) ?></td>
                                                    <td><?= date('M j, Y g:i A', strtotime($page['updated_at'])) ?></td>
                                                    <td>
    <div class="btn-group" role="group" style="display: flex; flex-wrap: wrap; gap: 4px;">
        <!-- Copy Page URL Button -->
        <button 
            class="btn btn-sm" 
            onclick='copyPageURL("<?='https://jb-college.vercel.app/'.str_replace(' ','-',$page['pageName']).'?id='.$page['id']?>")'
            title="Copy Page URL"
            type="button"
            style="padding: 6px 8px; justify-content: center; font-size: 12px; border-radius: 4px; border: none; background-color: transparent; color: #6c757d; cursor: pointer; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-copy" style="font-size: 13px;"></i>
        </button>
        
        <!-- Visit Live Page Button -->
        <a 
            href="<?='https://jb-college.vercel.app/pages/'.str_replace(' ','-',$page['pageName']).'?id='.$page['id']?>"
            title="Visit Live Page"
            target="_blank"
            class="btn btn-sm"
            style="padding: 6px 8px; font-size: 12px; justify-content: center; border-radius: 4px; border: none; background-color: transparent; color: #6c757d; text-decoration: none; display: inline-flex; align-items: center; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-external-link" style="font-size: 13px;"></i>
        </a>
        
        <!-- Preview Button -->
        <button 
            class="btn btn-sm" 
            onclick="previewPage(<?=$page['id']?>)"
            title="Preview"
            style="padding: 6px 8px; font-size: 12px; border-radius: 4px; justify-content: center; border: none; background-color: transparent; color: #28a745; cursor: pointer; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-eye" style="font-size: 13px;"></i>
        </button>
        
        <!-- Visual Editor Button -->
        <a 
            href="<?=base_url('dashboard/pages/visual-editor?edit='.$page['id'])?>"
            class="btn btn-sm"
            title="Visual Editor"
            style="padding: 6px 8px; font-size: 12px; justify-content: center; border-radius: 4px; border: none; background-color: transparent; color: #007bff; text-decoration: none; display: inline-flex; align-items: center; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-paint-brush" style="font-size: 13px;"></i>
        </a>
        
        <!-- Edit JSON Button -->
        <a 
            href="<?=base_url('dashboard/pages/edit/'.$page['id'])?>"
            class="btn btn-sm"
            title="Edit JSON"
            style="padding: 6px 8px; font-size: 12px; justify-content: center; border-radius: 4px; border: none; background-color: transparent; color: #ffc107; text-decoration: none; display: inline-flex; align-items: center; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-code" style="font-size: 13px;"></i>
        </a>
        
        <!-- Duplicate Button -->
        <button 
            class="btn btn-sm" 
            onclick="duplicatePage(<?=$page['id']?>)"
            title="Duplicate"
            style="padding: 6px 8px; font-size: 12px; justify-content: center; border-radius: 4px; border: none; background-color: transparent; color: #17a2b8; cursor: pointer; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-clone" style="font-size: 13px;"></i>
        </button>
        
        <!-- Delete Button -->
        <button 
            class="btn btn-sm" 
            onclick='deletePage(<?=$page['id']?>,"<?=esc($page['pageName'])?>")'
            title="Delete"
            style="padding: 6px 8px; font-size: 12px; border-radius: 4px; border: none; background-color: transparent; color: #dc3545; cursor: pointer; transition: background-color 0.2s ease;"
            onmouseover="this.style.backgroundColor='rgba(255,255,255,0.1)'"
            onmouseout="this.style.backgroundColor='transparent'">
            <i class="fa fa-trash" style="font-size: 13px;"></i>
        </button>
    </div>
</td>

                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center">
                                                    <div class="py-4">
                                                        <i class="fa fa-file-o fa-3x text-muted mb-3"></i>
                                                        <p class="text-muted">No pages found. <a href="<?= base_url('dashboard/pages/create') ?>">Create your first page</a></p>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <nav aria-label="Pages pagination">
                                            <ul class="pagination justify-content-center">
                                                <!-- Previous Page -->
                                                <?php if ($pagination['current_page'] > 1): ?>
                                                    <li class="page-item">
                                                        <a class="page-link" href="<?= base_url('dashboard/pages') ?>?page=<?= $pagination['current_page'] - 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                            Previous
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                                
                                                <!-- Page Numbers -->
                                                <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                                    <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                                        <a class="page-link" href="<?= base_url('dashboard/pages') ?>?page=<?= $i ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                            <?= $i ?>
                                                        </a>
                                                    </li>
                                                <?php endfor; ?>
                                                
                                                <!-- Next Page -->
                                                <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                                    <li class="page-item">
                                                        <a class="page-link" href="<?= base_url('dashboard/pages') ?>?page=<?= $pagination['current_page'] + 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                            Next
                                                        </a>
                                                    </li>
                                                <?php endif; ?>
                                            </ul>
                                        </nav>
                                        
                                        <div class="text-center text-muted">
                                            Showing <?= count($pages) ?> of <?= $pagination['total'] ?> pages
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i></a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <?= $this->include('partials/footer'); ?>
    <!--End footer-->

</div>
<!--End wrapper-->

<!-- Preview Modal -->
<div id="previewModal" class="modal" style="display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-xl" style="margin: 20px auto; max-width: 95%; height: 90vh;">
        <div class="modal-content" style="background: white; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); height: 100%;">
            <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center;">
                <h5 class="modal-title">Page Preview</h5>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="closePreviewModal()" style="border: none; background: none; font-size: 20px; cursor: pointer;">&times;</button>
            </div>
            <div class="modal-body" style="padding: 0; height: calc(100% - 80px); overflow-y: auto;">
                <div id="previewContainer"></div>
            </div>
        </div>
    </div>
</div>

<style>
    .page-content-renderer{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif;line-height:1.65;color:#3d4852;max-width:1536px;margin:0 auto;padding:0}.page-content-renderer .page-section{padding:3rem 1.5rem}.page-content-renderer .page-section.hero-banner-section{padding:2rem}.page-content-renderer .section-title{display:flex;align-items:center;gap:1rem;font-size:clamp(2rem,5vw,2.75rem);font-weight:700;color:#2c3e50;border-bottom:3px solid #ecf0f1;padding-bottom:1rem;margin-bottom:.5rem}.page-content-renderer .section-title img{height:40px}.page-content-renderer .section-subtitle{font-size:1.2rem;color:#7f8c8d;margin-top:.5rem;margin-bottom:2rem;max-width:800px}.page-content-renderer .content-row{display:grid;grid-template-columns:1fr;gap:2.5rem;margin-top:1.5rem}.page-content-renderer .content-column h1,.page-content-renderer .content-column h2,.page-content-renderer .content-column h3,.page-content-renderer .content-column h4,.page-content-renderer .content-column h5,.page-content-renderer .content-column h6{color:#34495e;font-weight:600;line-height:1.3;margin-top:1.5em;margin-bottom:.75em}.page-content-renderer .content-column h1{font-size:clamp(1.8rem,4vw,2.5rem)}.page-content-renderer .content-column h2{font-size:clamp(1.6rem,3.5vw,2.2rem)}.page-content-renderer .content-column h3{font-size:clamp(1.4rem,3vw,1.9rem)}.page-content-renderer .content-column h4{font-size:clamp(1.2rem,2.5vw,1.6rem)}.page-content-renderer .content-column h5{font-size:clamp(1.1rem,2.2vw,1.4rem)}.page-content-renderer .content-column h6{font-size:clamp(1.0rem,2vw,1.2rem);text-transform:uppercase}.page-content-renderer ul,.page-content-renderer ol{padding-left:25px}.page-content-renderer a{color:#3498db;font-weight:600;text-decoration:none;transition:color .2s}.page-content-renderer a:hover{color:#2980b9;text-decoration:underline}.page-content-renderer table{width:100%;border-collapse:collapse;margin-top:1.5em;box-shadow:0 2px 8px #0000000d}.page-content-renderer th,.page-content-renderer td{border:1px solid #e0e6ed;padding:12px 15px;text-align:left}.page-content-renderer th{background-color:#f9fafb;font-weight:600}.hero-banner-section{position:relative;display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;min-height:36rem;color:#fff;background-size:cover;background-position:center;margin-bottom:3rem}.hero-banner-section::before{content:'';position:absolute;top:0;left:0;right:0;bottom:0;background-color:#00000080}.hero-banner-section .hero-content{position:relative;z-index:1;max-width:900px}.hero-banner-section .hero-title{font-size:clamp(2.5rem,8vw,4.5rem);font-weight:800;margin-bottom:1rem;text-shadow:0 2px 15px #0003}.hero-banner-section .hero-subtitle{font-size:clamp(1.2rem,4vw,1.5rem);font-weight:400;text-shadow:0 2px 10px #0003}@media (min-width: 768px){.page-content-renderer .page-section{padding:4rem 2.5rem}.page-content-renderer .content-row.two-column{grid-template-columns:1fr 1fr;align-items:center}}@media (min-width: 1024px){.page-content-renderer .content-row.three-column{grid-template-columns:1fr 1fr 1fr;align-items:flex-start}}
</style>

<script>
function copyPageURL(urlToCopy) {
  // Use the modern Navigator Clipboard API
  navigator.clipboard.writeText(urlToCopy).then(function() {
    // Success feedback
    alert('Page URL copied to clipboard!');
  }, function(err) {
    // Error feedback
    console.error('Could not copy text: ', err);
    alert('Failed to copy URL.');
  });
}
function duplicatePage(pageId) {
    if (confirm('Are you sure you want to duplicate this page?')) {
        fetch(`${BASE_URL}api/v1/pages/duplicate/${pageId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Page duplicated successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to duplicate page'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while duplicating the page');
        });
    }
}

function deletePage(pageId, pageName) {
    if (confirm(`Are you sure you want to delete the page "${pageName}"? This action cannot be undone.`)) {
        fetch(`${BASE_URL}api/v1/pages/delete/${pageId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                alert('Page deleted successfully!');
                location.reload();
            } else {
                alert('Error: ' + (data.message || 'Failed to delete page'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the page');
        });
    }
}

function previewPage(pageId) {
    // Show loading
    document.getElementById('previewContainer').innerHTML = '<div class="text-center p-5"><i class="fa fa-spinner fa-spin fa-3x"></i><p class="mt-3">Loading preview...</p></div>';
    document.getElementById('previewModal').style.display = 'block';
    
    // Fetch page data
    fetch(`${BASE_URL}api/v1/pages/show/${pageId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                renderPagePreview(data.data.pageData);
            } else {
                document.getElementById('previewContainer').innerHTML = '<div class="alert alert-danger">Error loading page: ' + (data.message || 'Unknown error') + '</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('previewContainer').innerHTML = '<div class="alert alert-danger">Error loading page: ' + error.message + '</div>';
        });
}

function renderPagePreview(pageData) {
    if (!pageData || pageData.length === 0) {
        document.getElementById('previewContainer').innerHTML = '<div class="alert alert-info">No content to preview.</div>';
        return;
    }

    let html = '<div class="page-content-renderer">';
    
    pageData.forEach(section => {
        if (section.type === 'hero-banner-with-overlay') {
            const heroStyle = section.settings.backgroundImage ? 
                `style="background-image: url(${section.settings.backgroundImage})"` : '';
            const titleStyles = generateTextStyles(section.settings.title?.style);
            const subtitleStyles = generateTextStyles(section.settings.subtitle?.style);

            html += `
                <section class="page-section hero-banner-section" ${heroStyle}>
                    <div class="hero-content">
                        ${section.settings.title?.text ? 
                            `<h1 class="hero-title" style="${titleStyles}">${section.settings.title.text}</h1>` : ''}
                        ${section.settings.subtitle?.text ? 
                            `<p class="hero-subtitle" style="${subtitleStyles}">${section.settings.subtitle.text}</p>` : ''}
                    </div>
                </section>
            `;
        } else {
            const titleStyles = generateTextStyles(section.settings.title?.style);
            const subtitleStyles = generateTextStyles(section.settings.subtitle?.style);

            html += `<section class="page-section">`;
            
            if (section.settings.title?.text) {
                html += `<h2 class="section-title" style="${titleStyles}">`;
                if (section.settings.title.imageSrc) {
                    html += `<img src="${section.settings.title.imageSrc}" alt="" style="height: 40px; margin-right: 1rem;">`;
                }
                html += `${section.settings.title.text}</h2>`;
            }
            
            if (section.settings.subtitle?.text) {
                html += `<p class="section-subtitle" style="${subtitleStyles}">${section.settings.subtitle.text}</p>`;
            }

            section.settings.body.forEach(row => {
                const columnClass = row.columns.length === 1 ? '' : 
                                  row.columns.length === 2 ? ' two-column' : ' three-column';
                
                html += `<div class="content-row${columnClass}">`;
                
                row.columns.forEach(column => {
                    html += '<div class="content-column">';
                    html += renderColumnPreview(column);
                    html += '</div>';
                });
                
                html += '</div>';
            });

            html += `</section>`;
        }
    });

    html += '</div>';
    document.getElementById('previewContainer').innerHTML = html;
}

function renderColumnPreview(column) {
    const mediaStyle = 'width: 100%; height: auto; display: block; border-radius: 8px;';
    
    switch (column.type) {
        case 'paragraph':
            return `<div>${column.content}</div>`;
        case 'image':
            return `<img src="${column.content}" alt="${column.altText || ''}" style="${mediaStyle}">`;
        case 'video':
            return `<video src="${column.content}" style="${mediaStyle}" 
                    ${column.settings?.controls ? 'controls' : ''} 
                    ${column.settings?.autoplay ? 'autoplay' : ''} 
                    ${column.settings?.loop ? 'loop' : ''} 
                    ${column.settings?.muted ? 'muted' : ''} 
                    playsInline>Video not supported.</video>`;
        case 'audio':
            return `<audio src="${column.content}" style="width: 100%;" 
                    ${column.settings?.controls ? 'controls' : ''} 
                    ${column.settings?.autoplay ? 'autoplay' : ''} 
                    ${column.settings?.loop ? 'loop' : ''}>Audio not supported.</audio>`;
        default:
            return '';
    }
}

function generateTextStyles(style) {
    if (!style) return '';

    let styles = [];

    if (style.opacity !== undefined) {
        styles.push(`opacity: ${style.opacity}`);
    }

    if (style.gradient && style.gradient.colors && style.gradient.colors.length > 0) {
        const angle = style.gradient.angle || 90;
        const colors = style.gradient.colors.join(', ');
        styles.push(`background: linear-gradient(${angle}deg, ${colors})`);
        styles.push('background-clip: text');
        styles.push('-webkit-background-clip: text');
        styles.push('-webkit-text-fill-color: transparent');
        styles.push('color: transparent');
    } else if (style.color) {
        styles.push(`color: ${style.color}`);
    }

    return styles.join('; ');
}

function closePreviewModal() {
    document.getElementById('previewModal').style.display = 'none';
}
</script>

<?= $this->endSection(); ?>