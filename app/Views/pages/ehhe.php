<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="card-title d-flex justify-content-between align-items-center">
                                <h4>🎨 Visual Page Builder</h4>
                                <div>
                                    <button class="btn btn-info" onclick="pageBuilder.togglePreview()">
                                        <i class="fa fa-eye"></i> <span id="previewToggleText">Preview</span>
                                    </button>
                                    <button class="btn btn-success" onclick="pageBuilder.savePage()">
                                        <i class="fa fa-save"></i> Save Page
                                    </button>
                                    <a href="<?= base_url('dashboard/pages') ?>" class="btn btn-secondary">
                                        <i class="fa fa-arrow-left"></i> Back
                                    </a>
                                </div>
                            </div>
                            <hr>

                            <!-- Page Settings -->
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="pageName">Page Name *</label>
                                        <input type="text" class="form-control" id="pageName" placeholder="Enter page name">
                                    </div>
                                </div>
                            </div>

                            <!-- Builder/Preview Toggle -->
                            <div id="builderView">
                                <div class="row">
                                    <!-- Left Panel - Add Sections -->
                                    <div class="col-md-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>➕ Add Sections</h5>
                                            </div>
                                            <div class="card-body">
                                                <button class="btn btn-primary btn-block mb-2" onclick="pageBuilder.addSection('hero-banner-with-overlay')">
                                                    <i class="fa fa-image"></i> Add Hero Banner
                                                </button>
                                                <button class="btn btn-info btn-block mb-2" onclick="pageBuilder.addSection('standard-section')">
                                                    <i class="fa fa-file-text"></i> Add Content Section
                                                </button>
                                                <hr>
                                                <button class="btn btn-outline-success btn-block btn-sm" onclick="pageBuilder.loadSampleData()">
                                                    <i class="fa fa-magic"></i> Load Sample Page
                                                </button>
                                                <button class="btn btn-outline-info btn-block btn-sm" onclick="pageBuilder.openPasteJSON()">
                                                    <i class="fa fa-paste"></i> Paste JSON
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Section Overview Panel -->
                                        <div class="card mt-3">
                                            <div class="card-header">
                                                <h6>📋 Section Overview</h6>
                                                <small class="text-muted">Drag to reorder</small>
                                            </div>
                                            <div class="card-body p-2">
                                                <div id="sectionOverview">
                                                    <div class="text-muted text-center py-3">
                                                        <small>No sections yet</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Right Panel - Sections List -->
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header">
                                                <h5>📄 Page Sections</h5>
                                            </div>
                                            <div class="card-body">
                                                <div id="sectionsContainer">
                                                    <div class="empty-sections">
                                                        <i class="fa fa-plus-circle fa-3x text-muted"></i>
                                                        <p class="text-muted mt-2">Click "Add Hero Banner" or "Add Content Section" to start building your page</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Preview View (Hidden by default) -->
                            <div id="previewView" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>👁️ Live Preview</h5>
                                    </div>
                                    <div class="card-body" style="background: white !important;">
                                        <div id="previewContainer"></div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i></a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <?= $this->include('partials/footer'); ?>
    <!--End footer-->

</div>
<!--End wrapper-->

<!-- Toast Container -->
<div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>

<!-- Rich Text Editor Modal -->
<div id="richTextModal" class="modal" style="display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
    <div class="modal-dialog modal-lg" style="margin: 50px auto; max-width: 800px;">
        <div class="modal-content" style="background: rgba(250, 250, 255, 0.2); backdrop-filter: blur(2rem); border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #ddd; display: flex; justify-content: space-between; align-items: center;">
                <h5 class="modal-title">Rich Text Editor</h5>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.closeRichTextModal()" style="border: none; background: #ffffff2e; color: white; font-size: 20px; cursor: pointer;">×</button>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div id="richTextToolbar" class="mb-3" style="border-bottom: 1px solid #eee; padding-bottom: 10px;">
                    <!-- Text Formatting -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('bold')" title="Bold"><i class="fa fa-bold"></i></button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('italic')" title="Italic"><i class="fa fa-italic"></i></button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('underline')" title="Underline"><i class="fa fa-underline"></i></button>
                    </div>
                    
                    <!-- Structure -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('formatBlock', '<h3>')" title="Heading 3">H3</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('formatBlock', '<h4>')" title="Heading 4">H4</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('insertUnorderedList')" title="Bullet List"><i class="fa fa-list-ul"></i></button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.formatText('insertOrderedList')" title="Numbered List"><i class="fa fa-list-ol"></i></button>
                    </div>
                    
                    <!-- Insert -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.insertLink()" title="Link"><i class="fa fa-link"></i></button>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="pageBuilder.insertTable()" title="Table"><i class="fa fa-table"></i></button>
                    </div>
                </div>
                
                <div contenteditable="true" id="richTextEditor" class="form-control" style="min-height: 300px; max-height: 60vh; background-color: white !important; overflow-y: scroll;"></div>
                <small class="text-muted mt-2 d-block">Tip: Select text and use the toolbar buttons to format it.</small>
            </div>
            <div class="modal-footer" style="padding: 20px; border-top: 1px solid #ddd; display: flex; justify-content: flex-end; gap: 10px;">
                <button type="button" class="btn btn-secondary" onclick="pageBuilder.closeRichTextModal()"><i class="fa fa-times"></i> Cancel</button>
                <button type="button" class="btn btn-success" onclick="pageBuilder.saveRichText()" style="min-width: 120px;"><i class="fa fa-save"></i> Save Content</button>
            </div>
        </div>
    </div>
</div>

<!-- All other modals (Media Upload, Paste JSON) can remain the same -->

<style>
/* All previous styles are good, just add or modify these */
.section-form {
    border-radius: 1rem;
    margin-bottom: 20px;
    background: rgb(41 48 57);
    overflow: hidden;
}
.section-header {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.section-content {
    padding: 20px;
    background: rgba(0, 0, 0, 0.3);
}
.row-form {
    margin: 15px 0;
    padding: 15px;
    border-radius: 0.78rem;
    background: #2f3842;
}
.column-form {
    padding: 15px;
    margin: 10px 0;
    border-radius: 0.78rem;
    background: rgba(0, 0, 0, 0.3);
}
.empty-sections {
    display: flex; flex-direction: column; align-items: center; justify-content: center;
    height: 200px; border-radius: 1rem; border: 2px dashed rgba(255,255,255,0.2);
}
.style-controls {
    background: #2f3842;
    padding: 15px;
    border-radius: 0.78rem;
    margin-top: 15px;
}
.gradient-color-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}
</style>

<script>
class FormPageBuilder {
    constructor() {
        this.pageData = [];
        this.editMode = false;
        this.editPageId = null;
        this.sectionCounter = 1;
        this.rowCounter = 1;
        this.columnCounter = 1;
        this.currentEditingColumnId = null;
        this.currentUploadColumnId = null;
        this.currentUploadSectionId = null;
        this.currentUploadType = null;
        this.isPreviewMode = false;
        this.draggedSectionIndex = undefined;
        this.checkEditMode();
        this.renderBuilderView();
    }

    // ===== SECTION MANAGEMENT =====
    
    addSection(type) {
        const sectionId = `section-${this.sectionCounter++}`;
        
        // NEW: Standardized Section Creation
        let section = {
            id: sectionId,
            type: type,
            settings: {
                title: { text: 'New Section Title', style: {}},
                subtitle: { text: 'New section subtitle', style: {}},
                body: []
            }
        };

        if (type === 'hero-banner-with-overlay') {
            section.settings.backgroundImage = '';
            section.settings.title.text = 'New Hero Title';
        } else {
            section.type = 'standard-section'; // Use a generic name
        }
        
        this.pageData.push(section);
        this.renderBuilderView();
        setTimeout(() => this.scrollToSection(sectionId), 100);
        this.showSuccess('Section Added', `${type === 'hero-banner-with-overlay' ? 'Hero Banner' : 'Content Section'} added`);
    }

    deleteSection(sectionId) {
        if (confirm('Are you sure you want to delete this section?')) {
            this.pageData = this.pageData.filter(section => section.id !== sectionId);
            this.renderBuilderView();
        }
    }

    // ===== ROW & COLUMN MANAGEMENT (No changes needed here) =====
    // addRow, deleteRow, addColumn, deleteColumn are all good.
    addRow(sectionId) {
        const section = this.findSectionById(sectionId);
        if (!section) return;
        const rowId = `row-${this.rowCounter++}`;
        section.settings.body.push({ id: rowId, columns: [] });
        this.renderSectionHTML(sectionId); // MODIFIED: Re-render only the affected section
    }

    deleteRow(sectionId, rowId) {
        if (!confirm('Delete this row?')) return;
        const section = this.findSectionById(sectionId);
        if (!section) return;
        section.settings.body = section.settings.body.filter(row => row.id !== rowId);
        this.renderSectionHTML(sectionId); // MODIFIED: Re-render only the affected section
    }

    addColumn(sectionId, rowId, columnType) {
        const section = this.findSectionById(sectionId);
        if (!section) return;
        const row = section.settings.body.find(r => r.id === rowId);
        if (!row || row.columns.length >= 3) return;
        const columnId = `col-${this.columnCounter++}`;
        let column = { id: columnId, type: columnType };
        switch(columnType) {
            case 'paragraph': column.content = '<p>Click to edit...</p>'; break;
            case 'image': column.content = 'https://via.placeholder.com/400x300'; column.altText = 'Placeholder'; break;
            case 'video': column.content = ''; column.settings = { controls: true, autoplay: false, loop: false, muted: false }; break;
            case 'audio': column.content = ''; column.settings = { controls: true, autoplay: false, loop: false }; break;
        }
        row.columns.push(column);
        this.renderSectionHTML(sectionId); // MODIFIED: Re-render only the affected section
    }

    deleteColumn(sectionId, rowId, columnId) {
        if (!confirm('Delete this column?')) return;
        const section = this.findSectionById(sectionId);
        if (!section) return;
        const row = section.settings.body.find(r => r.id === rowId);
        if (!row) return;
        row.columns = row.columns.filter(col => col.id !== columnId);
        this.renderSectionHTML(sectionId); // MODIFIED: Re-render only the affected section
    }

    // ===== PROPERTY UPDA TING =====

    // MODIFIED: Centralized property updater for both sections and columns
    updateProperty(path, value) {
        const pathParts = path.split('.');
        const id = pathParts.shift(); // e.g., 'section-1' or 'col-1'

        let obj;
        if (id.startsWith('section-')) {
            obj = this.findSectionById(id);
        } else if (id.startsWith('col-')) {
            obj = this.findColumnById(id);
        }

        if (!obj) return;

        let current = obj;
        for (let i = 0; i < pathParts.length - 1; i++) {
            const part = pathParts[i];
            if (!current[part] || typeof current[part] !== 'object') {
                current[part] = {}; // Create nested objects if they don't exist
            }
            current = current[part];
        }

        current[pathParts[pathParts.length - 1]] = value;
        
        // Re-render the housing section to reflect changes
        const sectionToRender = id.startsWith('section-') ? obj : this.findSectionContainingColumn(id);
        if (sectionToRender) this.renderSectionHTML(sectionToRender.id);
    }
    
    // ===== RICH TEXT EDITOR =====
    
    openRichTextEditor(columnId) {
        const column = this.findColumnById(columnId);
        if (!column || column.type !== 'paragraph') return;
        this.currentEditingColumnId = columnId;
        document.getElementById('richTextEditor').innerHTML = column.content;
        document.getElementById('richTextModal').style.display = 'block';
        setTimeout(() => document.getElementById('richTextEditor').focus(), 100);
    }
    
    saveRichText() {
        if (!this.currentEditingColumnId) return;
        const content = document.getElementById('richTextEditor').innerHTML;
        this.updateProperty(`${this.currentEditingColumnId}.content`, content);
        this.closeRichTextModal();
    }
    
    closeRichTextModal() {
        document.getElementById('richTextModal').style.display = 'none';
        this.currentEditingColumnId = null;
    }

    // MODIFIED: Simplified formatText
    formatText(command, value = null) {
        document.getElementById('richTextEditor').focus();
        document.execCommand(command, false, value);
    }

    insertLink() {
        const url = prompt('Enter URL:', 'https://');
        if (url) this.formatText('createLink', url);
    }
    
    insertTable() {
        const rows = prompt('Rows:', '2');
        const cols = prompt('Columns:', '2');
        if (!rows || !cols) return;
        let table = '<table style="width:100%; border-collapse: collapse;" border="1"><tbody>';
        for(let r=0; r<rows; r++) {
            table += '<tr>';
            for(let c=0; c<cols; c++) table += '<td style="border: 1px solid #ddd; padding: 8px;">Cell</td>';
            table += '</tr>';
        }
        table += '</tbody></table>'
        this.formatText('insertHTML', table);
    }

    // ===== DYNAMIC STYLE CONTROLS (NEW) =====

    getStyleControlsHTML(sectionId, target) { // target is 'title' or 'subtitle'
        const section = this.findSectionById(sectionId);
        const style = section.settings[target]?.style || {};
        const useGradient = style.gradient && style.gradient.colors && style.gradient.colors.length > 0;
        
        let gradientColorsHTML = '';
        if (useGradient) {
            style.gradient.colors.forEach((color, index) => {
                gradientColorsHTML += `
                    <div class="gradient-color-row">
                        <input type="color" class="form-control form-control-sm" value="${color}" 
                               onchange="pageBuilder.updateGradientColor('${sectionId}', '${target}', ${index}, this.value)">
                        <button class="btn btn-sm btn-outline-danger" onclick="pageBuilder.removeGradientColor('${sectionId}', '${target}', ${index})">×</button>
                    </div>
                `;
            });
        }
        
        return `
            <div class="style-controls">
                <h6>${target.charAt(0).toUpperCase() + target.slice(1)} Styling</h6>
                <div class="form-group mb-2">
                    <label class="small">Opacity</label>
                    <input type="range" class="form-control-range" min="0" max="1" step="0.05" value="${style.opacity ?? 1}"
                           oninput="this.nextElementSibling.textContent = this.value"
                           onchange="pageBuilder.updateProperty('${sectionId}.settings.${target}.style.opacity', parseFloat(this.value))">
                    <span class="small text-muted">${style.opacity ?? 1}</span>
                </div>
                
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="${sectionId}-${target}-color" id="${sectionId}-${target}-solid" ${!useGradient ? 'checked' : ''}
                           onchange="pageBuilder.toggleGradient('${sectionId}', '${target}', false)">
                    <label class="form-check-label small" for="${sectionId}-${target}-solid">Solid Color</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="${sectionId}-${target}-color" id="${sectionId}-${target}-gradient" ${useGradient ? 'checked' : ''}
                           onchange="pageBuilder.toggleGradient('${sectionId}', '${target}', true)">
                    <label class="form-check-label small" for="${sectionId}-${target}-gradient">Gradient</label>
                </div>

                <div id="${sectionId}-${target}-solid-controls" style="display: ${!useGradient ? 'block' : 'none'};">
                     <div class="form-group mb-2 mt-2">
                        <label class="small">Color</label>
                        <input type="color" class="form-control form-control-sm" value="${style.color || '#ffffff'}"
                               onchange="pageBuilder.updateProperty('${sectionId}.settings.${target}.style.color', this.value)">
                    </div>
                </div>

                <div id="${sectionId}-${target}-gradient-controls" style="display: ${useGradient ? 'block' : 'none'};">
                    <div class="form-group mb-2 mt-2">
                        <label class="small">Angle</label>
                        <input type="number" class="form-control form-control-sm" value="${style.gradient?.angle || 90}"
                               onchange="pageBuilder.updateProperty('${sectionId}.settings.${target}.style.gradient.angle', parseInt(this.value))">
                    </div>
                     <div class="form-group mb-2">
                        <label class="small">Colors</label>
                        <div id="${sectionId}-${target}-gradient-colors">${gradientColorsHTML}</div>
                        <button class="btn btn-sm btn-outline-success mt-2" onclick="pageBuilder.addGradientColor('${sectionId}', '${target}')">Add Color</button>
                    </div>
                </div>
            </div>
        `;
    }

    toggleGradient(sectionId, target, enabled) {
        const section = this.findSectionById(sectionId);
        if (!section) return;

        if (enabled) {
            // If switching to gradient, initialize it if it doesn't exist
            if (!section.settings[target].style.gradient) {
                section.settings[target].style.gradient = { angle: 90, colors: ['#ffffff', '#000000'] };
            }
            // Clear solid color to give gradient precedence
            if (section.settings[target].style.color) delete section.settings[target].style.color;
        } else {
            // If switching to solid, clear the gradient
            if (section.settings[target].style.gradient) delete section.settings[target].style.gradient;
        }

        this.renderSectionHTML(sectionId);
    }
    
    addGradientColor(sectionId, target) {
        const section = this.findSectionById(sectionId);
        if (section && section.settings[target]?.style?.gradient) {
            section.settings[target].style.gradient.colors.push('#000000');
            this.renderSectionHTML(sectionId);
        }
    }

    removeGradientColor(sectionId, target, index) {
        const section = this.findSectionById(sectionId);
        if (section && section.settings[target]?.style?.gradient) {
            section.settings[target].style.gradient.colors.splice(index, 1);
            this.renderSectionHTML(sectionId);
        }
    }
    
    updateGradientColor(sectionId, target, index, color) {
        const section = this.findSectionById(sectionId);
         if (section && section.settings[target]?.style?.gradient) {
            section.settings[target].style.gradient.colors[index] = color;
        }
    }


    // ===== RENDERING =====
    
    renderBuilderView() {
        const container = document.getElementById('sectionsContainer');
        if (this.pageData.length === 0) {
            container.innerHTML = `<div class="empty-sections"><i class="fa fa-plus-circle fa-3x text-muted"></i><p class="text-muted mt-2">Click an "Add" button to start</p></div>`;
        } else {
            container.innerHTML = '';
            this.pageData.forEach(section => {
                const sectionDiv = document.createElement('div');
                sectionDiv.id = section.id;
                sectionDiv.className = 'section-form';
                container.appendChild(sectionDiv);
                this.renderSectionHTML(section.id);
            });
        }
        this.renderSectionOverview();
    }
    
    // NEW: Renders the HTML for just one section
    renderSectionHTML(sectionId) {
        const sectionDiv = document.getElementById(sectionId);
        const section = this.findSectionById(sectionId);
        if (!sectionDiv || !section) return;
        sectionDiv.innerHTML = this.getSectionHTML(section);
    }
    
    getSectionHTML(section) {
        const isHero = section.type === 'hero-banner-with-overlay';
        
        let html = `
            <div class="section-header">
                <div><strong>${isHero ? '🖼️ Hero Banner' : '📄 Content Section'}</strong></div>
                <div><button class="btn btn-sm btn-danger" onclick="pageBuilder.deleteSection('${section.id}')"><i class="fa fa-trash"></i></button></div>
            </div>
            <div class="section-content">
        `;
        
        if (isHero) {
            html += `<div class="form-group">
                        <label>Background Image URL</label>
                        <input type="text" class="form-control" value="${section.settings.backgroundImage || ''}" onchange="pageBuilder.updateProperty('${section.id}.settings.backgroundImage', this.value)">
                     </div>`;
        }

        html += `<div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Title Text</label>
                            <input type="text" class="form-control" value="${section.settings.title.text}" onchange="pageBuilder.updateProperty('${section.id}.settings.title.text', this.value)">
                        </div>
                        ${this.getStyleControlsHTML(section.id, 'title')}
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Subtitle Text</label>
                            <input type="text" class="form-control" value="${section.settings.subtitle.text}" onchange="pageBuilder.updateProperty('${section.id}.settings.subtitle.text', this.value)">
                        </div>
                        ${this.getStyleControlsHTML(section.id, 'subtitle')}
                    </div>
                 </div>`;
        // NEW: Input for title.imageSrc
        if (!isHero) {
            html += `<div class="form-group mt-3">
                        <label>Title Icon URL (Optional)</label>
                        <input type="text" class="form-control" value="${section.settings.title.imageSrc || ''}" onchange="pageBuilder.updateProperty('${section.id}.settings.title.imageSrc', this.value)">
                     </div>`;
        }

        html += `<hr><h6>Content Rows</h6>`;
        section.settings.body.forEach(row => {
            html += this.getRowHTML(section.id, row);
        });
        html += `<div class="text-center mt-3"><button class="btn btn-success" onclick="pageBuilder.addRow('${section.id}')"><i class="fa fa-plus"></i> Add Row</button></div>`;
        html += `</div>`;
        return html;
    }

    getRowHTML(sectionId, row) {
        let html = `<div class="row-form"><div class="d-flex justify-content-end mb-2"><button class="btn btn-sm btn-outline-danger" onclick="pageBuilder.deleteRow('${sectionId}', '${row.id}')"><i class="fa fa-trash"></i></button></div>
                    <div class="row">`;
        row.columns.forEach(column => {
            html += `<div class="col-md-${12 / (row.columns.length || 1)}">${this.getColumnHTML(sectionId, row.id, column)}</div>`;
        });
        html += `</div>`;
        if (row.columns.length < 3) {
            html += `<div class="text-center mt-2 pt-2 border-top"><div class="btn-group">
                        <button class="btn btn-sm btn-primary" onclick="pageBuilder.addColumn('${sectionId}', '${row.id}', 'paragraph')"><i class="fa fa-paragraph"></i></button>
                        <button class="btn btn-sm btn-success" onclick="pageBuilder.addColumn('${sectionId}', '${row.id}', 'image')"><i class="fa fa-image"></i></button>
                        <button class="btn btn-sm btn-info" onclick="pageBuilder.addColumn('${sectionId}', '${row.id}', 'video')"><i class="fa fa-video-camera"></i></button>
                        <button class="btn btn-sm btn-warning" onclick="pageBuilder.addColumn('${sectionId}', '${row.id}', 'audio')"><i class="fa fa-volume-up"></i></button>
                    </div></div>`;
        }
        html += `</div>`;
        return html;
    }

    getColumnHTML(sectionId, rowId, column) {
        let html = `<div class="column-form"><div class="d-flex justify-content-between align-items-center mb-2">
                    <h6>${column.type}</h6>
                    <button class="btn btn-sm btn-outline-danger" onclick="pageBuilder.deleteColumn('${sectionId}', '${row.id}', '${column.id}')"><i class="fa fa-trash"></i></button>
                </div>`;
        switch(column.type) {
            case 'paragraph': html += `<button class="btn btn-light btn-sm btn-block" onclick="pageBuilder.openRichTextEditor('${column.id}')"><i class="fa fa-edit"></i> Edit Content</button>`; break;
            case 'image': html += `<div class="form-group"><label class="small">Image URL</label><input type="text" class="form-control form-control-sm" value="${column.content}" onchange="pageBuilder.updateProperty('${column.id}.content', this.value)"></div><div class="form-group"><label class="small">Alt Text</label><input type="text" class="form-control form-control-sm" value="${column.altText || ''}" onchange="pageBuilder.updateProperty('${column.id}.altText', this.value)"></div>`; break;
            case 'video': case 'audio':
                html += `<div class="form-group"><label class="small">${column.type} URL</label><input type="text" class="form-control form-control-sm" value="${column.content}" onchange="pageBuilder.updateProperty('${column.id}.content', this.value)"></div>`;
                html += `<div class="form-check small"><input type="checkbox" class="form-check-input" ${column.settings?.controls ? 'checked' : ''} onchange="pageBuilder.updateProperty('${column.id}.settings.controls', this.checked)"><label>Controls</label></div>`;
                html += `<div class="form-check small"><input type="checkbox" class="form-check-input" ${column.settings?.autoplay ? 'checked' : ''} onchange="pageBuilder.updateProperty('${column.id}.settings.autoplay', this.checked)"><label>Autoplay</label></div>`;
                html += `<div class="form-check small"><input type="checkbox" class="form-check-input" ${column.settings?.loop ? 'checked' : ''} onchange="pageBuilder.updateProperty('${column.id}.settings.loop', this.checked)"><label>Loop</label></div>`;
                if(column.type === 'video') html += `<div class="form-check small"><input type="checkbox" class="form-check-input" ${column.settings?.muted ? 'checked' : ''} onchange="pageBuilder.updateProperty('${column.id}.settings.muted', this.checked)"><label>Muted</label></div>`;
                break;
        }
        html += `</div>`;
        return html;
    }

    // All other methods like renderSectionOverview, savePage, preview, helpers, etc. can remain largely the same.
    // ... (rest of the class methods)
    findSectionById(id) { return this.pageData.find(s => s.id === id); }
    findColumnById(id) { for (const s of this.pageData) for (const r of s.settings.body) for (const c of r.columns) if (c.id === id) return c; return null; }
    findSectionContainingColumn(id) { return this.pageData.find(s => s.settings.body.some(r => r.columns.some(c => c.id === id))); }
    // ... all other methods from your original file ...
}

// Ensure the rest of your original script (initialization, other methods) is here
let pageBuilder;
document.addEventListener('DOMContentLoaded', function() {
    pageBuilder = new FormPageBuilder(); // This line from your original file initializes everything
});
</script>

<?= $this->endSection(); ?>