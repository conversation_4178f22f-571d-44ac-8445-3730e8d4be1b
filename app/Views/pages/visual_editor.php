<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">
            <div class="row mt-3">
                <div class="col-lg-12">
                    <div id="pageBuilderWrapper">
                        <div class="card"><div class="card-body">
                            <div class="card-title d-flex justify-content-between align-items-center">
                                <h4>🎨 Visual Page Builder</h4>
                                <div>
                                    <button class="btn btn-info" onclick="pageBuilder.togglePreview()"><i class="fa fa-eye"></i> <span id="previewToggleText">Preview</span></button>
                                    <button class="btn btn-success" onclick="pageBuilder.savePage()"><i class="fa fa-save"></i> Save Page</button>
                                    <a href="<?= base_url('dashboard/pages') ?>" class="btn btn-secondary"><i class="fa fa-arrow-left"></i> Back</a>
                                </div>
                            </div>
                            <hr>
                            <div class="row mb-4"><div class="col-md-6"><div class="form-group"><label for="pageName">Page Name *</label><input type="text" class="form-control" id="pageName" placeholder="Enter page name"></div></div></div>
                            <div id="builderView"><div class="row"><div class="col-md-4"><div class="card"><div class="card-header"><h5>➕ Add Sections</h5></div><div class="card-body"><button class="btn btn-primary btn-block mb-2" onclick="pageBuilder.addSection('hero-banner-with-overlay')"><i class="fa fa-image"></i> Add Hero</button><button class="btn btn-info btn-block mb-2" onclick="pageBuilder.addSection('standard-section')"><i class="fa fa-file-text"></i> Add Section</button><hr><button class="btn btn-outline-success btn-block btn-sm" onclick="pageBuilder.loadSampleData()"><i class="fa fa-magic"></i> Load Sample</button><button class="btn btn-outline-warning btn-block btn-sm mt-2" onclick="pageBuilder.openJsonModal()"><i class="fa fa-code"></i> Load from JSON</button></div></div><div class="card mt-3"><div class="card-header"><h6>📋 Overview</h6><small class="text-muted">Drag to reorder</small></div><div class="card-body p-2" id="sectionOverview"></div></div></div><div class="col-md-8"><div class="card"><div class="card-header"><h5>📄 Page Sections</h5></div><div class="card-body" id="sectionsContainer"><div class="empty-sections"><i class="fa fa-plus-circle fa-3x text-muted"></i><p class="text-muted mt-2">Add a section to start</p></div></div></div></div></div></div>
                            <div id="previewView" style="display: none; background-color: white !important;"><div id="previewContainer" class="bg-light p-3"></div></div>
                        </div></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?= $this->include('partials/footer'); ?>
</div>

<div id="toastContainer"></div>
<div id="richTextModal" style="display:none;position:fixed;z-index:1050;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5);backdrop-filter:blur(5px);"><div style="margin:3vh auto;max-width:1100px;width:90vw;"><div style="background:rgba(40,42,54,0.95);color:#f8f8f2;border-radius:12px;border:1px solid rgba(255,255,255,0.1);box-shadow:0 8px 32px 0 rgba(0,0,0,0.37);"><div style="padding:1rem 1.5rem;border-bottom:1px solid rgba(255,255,255,0.1);display:flex;justify-content:space-between;align-items:center;"><h5 style="margin:0;font-size:1.25rem;">Rich Text Editor</h5><button type="button"onclick="pageBuilder.closeRichTextModal()"style="background:0;border:0;font-size:1.75rem;color:#999;cursor:pointer;padding:0 .5rem"onmouseover="this.style.color='#fff'"onmouseout="this.style.color='#999'">×</button></div><div style="padding:1.5rem;"><div style="margin-bottom:1rem;padding-bottom:1rem;border-bottom:1px solid rgba(255,255,255,0.1);display:flex;flex-wrap:wrap;gap:.5rem"><div style="display:inline-flex; border-radius:.25rem;overflow:hidden"><select class="rte-select"onchange="pageBuilder.formatText('formatBlock',this.value)"><option value="p">Paragraph</option><option value="h1">H1</option><option value="h2">H2</option><option value="h3">H3</option><option value="h4">H4</option><option value="h5">H5</option><option value="h6">H6</option></select></div><div style="display:inline-flex"><button class="rte-btn"onclick="pageBuilder.formatText('bold')"><i class="fa fa-bold"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('italic')"><i class="fa fa-italic"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('underline')"><i class="fa fa-underline"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('strikethrough')"><i class="fa fa-strikethrough"></i></button></div><div style="display:inline-flex"><button class="rte-btn"onclick="pageBuilder.formatText('superscript')"><i class="fa fa-superscript"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('subscript')"><i class="fa fa-subscript"></i></button></div><div style="display:inline-flex"><button class="rte-btn"onclick="pageBuilder.openColorPicker('foreColor')"><i class="fa fa-font"style="color:#ffb86c"></i></button><button class="rte-btn"onclick="pageBuilder.openColorPicker('backColor')"><i class="fa fa-paint-brush"style="color:#50fa7b"></i></button></div><div style="display:inline-flex"><button class="rte-btn"onclick="pageBuilder.formatText('justifyLeft')"><i class="fa fa-align-left"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('justifyCenter')"><i class="fa fa-align-center"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('justifyRight')"><i class="fa fa-align-right"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('justifyFull')"><i class="fa fa-align-justify"></i></button></div><div style="display:inline-flex"><button class="rte-btn"onclick="pageBuilder.formatText('insertUnorderedList')"><i class="fa fa-list-ul"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('insertOrderedList')"><i class="fa fa-list-ol"></i></button></div><div style="display:inline-flex"><button class="rte-btn"onclick="pageBuilder.insertLink()"><i class="fa fa-link"></i></button><button class="rte-btn"onclick="pageBuilder.insertTable()"><i class="fa fa-table"></i></button><button class="rte-btn"onclick="pageBuilder.formatText('removeFormat')"><i class="fa fa-eraser"></i></button></div></div><div contenteditable="true"class="richTextEdit"id="richTextEditor"style="min-height:300px;max-height:60vh;background-color:#f8f9fa;color:#212529;border-radius:.25rem;padding:1rem;line-height:1.6;overflow-y:auto;"></div></div><div style="padding:1rem 1.5rem;border-top:1px solid rgba(255,255,255,0.1);display:flex;justify-content:flex-end;gap:1rem"><button onclick="pageBuilder.closeRichTextModal()"class="rte-modal-btn-secondary">Cancel</button><button onclick="pageBuilder.saveRichText()"class="rte-modal-btn-primary">Save</button></div></div></div></div>
<div id="colorPickerModal" style="display:none;position:fixed;z-index:2000;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5);"><div style="margin:10vh auto;max-width:320px;width:90vw;"><div style="background:rgba(68,71,90,0.95);color:#f8f8f2;border-radius:12px;border:1px solid rgba(255,255,255,0.1);box-shadow:0 8px 32px rgba(0,0,0,0.37);"><div style="padding:1rem 1.5rem;border-bottom:1px solid rgba(255,255,255,0.1);display:flex;justify-content:space-between;align-items:center;"><h5 style="margin:0;font-size:1.1rem;"id="colorPickerTitle">Color Picker</h5><button type="button"onclick="pageBuilder.closeColorPicker()"style="background:0;border:0;font-size:1.5rem;color:#999;cursor:pointer;padding:0 .5rem"onmouseover="this.style.color='#fff'"onmouseout="this.style.color='#999'">×</button></div><div style="padding:1.5rem;"><div style="margin-bottom:1rem;text-align:center; display: none;"><div style="display:inline-flex;background-color:rgba(0,0,0,0.2);border-radius:6px;padding:4px;"><button id="colorTypeSolid"onclick="pageBuilder.setColorPickerType('solid')"class="color-type-btn active">Solid</button><button id="colorTypeGradient"onclick="pageBuilder.setColorPickerType('gradient')"class="color-type-btn">Gradient</button></div></div><div id="solidColorControls"style="display:none;"><div class="form-group"><label style="font-size:.8rem;color:#bd93f9">Color</label><input type="color"id="solidColorInput"value="#ff79c6"style="width:100%;height:40px;border:none;border-radius:4px;background-color:#282a36;"></div></div><div id="gradientColorControls"style="display:block;"><div class="form-group"><label style="font-size:.8rem;color:#bd93f9">Angle</label><input type="number"id="gradientAngle"value="90"style="width:100%;background-color:#282a36;border:1px solid #6272a4;color:#f8f8f2;border-radius:4px;padding: .375rem .75rem;"></div><div class="form-group"><label style="font-size:.8rem;color:#bd93f9">Colors</label><div id="gradientColorsContainer"></div><button onclick="pageBuilder.addGradientColorStop()"style="background-color:#44475a;color:#f8f8f2;border:1px solid #6272a4;padding:.25rem .5rem;border-radius:4px;cursor:pointer;font-size:.8rem;">+ Add Color</button></div></div><hr style="border-color:rgba(255,255,255,0.1);margin:1rem 0"><div class="form-group"><label style="font-size:.8rem;color:#bd93f9">Opacity</label><input type="range"min="0"max="1"step="0.05"value="1"id="colorOpacity"class="form-control-range"oninput="this.nextElementSibling.textContent=this.value"><span style="color:#999;font-size:.8rem;margin-left:.5rem">1</span></div></div><div style="padding:1rem 1.5rem;border-top:1px solid rgba(255,255,255,0.1);display:flex;justify-content:flex-end;gap:1rem"><button onclick="pageBuilder.closeColorPicker()"class="rte-modal-btn-secondary">Cancel</button><button onclick="pageBuilder.applyRteColor()"class="rte-modal-btn-primary">Apply</button></div></div></div></div>
<div id="mediaUploadModal" style="display:none;position:fixed;z-index:1060;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5);"><div style="margin:50px auto;max-width:600px;"><div style="background:#74747424;border-radius:12px;box-shadow:0 8px 32px 0 rgba(0,0,0,0.37);backdrop-filter: blur(3rem);"><div style="padding:1rem 1.5rem;border-bottom:1px solid #e9ecef24;display:flex;justify-content:space-between;align-items:center;"><h5 style="margin:0;font-size:1.25rem;">Upload Media</h5><button type="button"onclick="pageBuilder.closeMediaUploadModal()"style="background:0;border:0;font-size:1.75rem;color:#999;cursor:pointer;">×</button></div><div style="padding:1.5rem;"><div class="form-group"><label for="mediaFileInput">Select File</label><input type="file"class="form-control-file"id="mediaFileInput"onchange="pageBuilder.previewSelectedFile()"><small class="form-text text-muted"id="mediaFileHelpText"></small></div><div id="uploadProgress"style="display:none"><div class="progress"><div class="progress-bar"role="progressbar"style="width:0%"></div></div><small class="text-muted">Uploading...</small></div><div id="uploadPreview"style="display:none;margin-top:15px"><label>Preview:</label><div id="previewContent"style="max-height:200px;overflow:hidden"></div></div></div><div style="padding:1rem 1.5rem;border-top:1px solid #e9ecef24;display:flex;justify-content:flex-end;gap:1rem"><button type="button"onclick="pageBuilder.closeMediaUploadModal()"class="btn btn-secondary">Cancel</button><button type="button"onclick="pageBuilder.uploadFile()"id="uploadBtn"class="btn btn-primary"><i class="fa fa-upload"></i> Upload & Use</button></div></div></div></div>

<div id="jsonImportModal" style="display:none;position:fixed;z-index:1050;left:0;top:0;width:100%;height:100%;background-color:rgba(0,0,0,0.5);backdrop-filter:blur(5px);"><div style="margin:5vh auto;max-width:800px;width:90vw;"><div style="background:rgba(40,42,54,0.95);color:#f8f8f2;border-radius:12px;border:1px solid rgba(255,255,255,0.1);box-shadow:0 8px 32px 0 rgba(0,0,0,0.37);"><div style="padding:1rem 1.5rem;border-bottom:1px solid rgba(255,255,255,0.1);display:flex;justify-content:space-between;align-items:center;"><h5 style="margin:0;font-size:1.25rem;">Import from JSON</h5><button type="button"onclick="pageBuilder.closeJsonModal()"style="background:0;border:0;font-size:1.75rem;color:#999;cursor:pointer;padding:0 .5rem"onmouseover="this.style.color='#fff'"onmouseout="this.style.color='#999'">×</button></div><div style="padding:1.5rem;"><p class="text-muted small">Paste your page data JSON below. This will replace all current content on the builder.</p><textarea id="jsonInput"style="width:100%;height:50vh;background-color:#282a36;color:#f8f8f2;border:1px solid #6272a4;border-radius:6px;padding:1rem;font-family:monospace;font-size:0.9rem;resize:vertical;"></textarea></div><div style="padding:1rem 1.5rem;border-top:1px solid rgba(255,255,255,0.1);display:flex;justify-content:flex-end;gap:1rem"><button onclick="pageBuilder.closeJsonModal()"class="rte-modal-btn-secondary">Cancel</button><button onclick="pageBuilder.loadFromJson()"class="rte-modal-btn-primary">Load Data</button></div></div></div></div>


<style>
#pageBuilderWrapper .section-form{border-radius:1rem;margin-bottom:1.5rem;background:#0000002d;overflow:hidden}#pageBuilderWrapper .section-header{background:#f8f9fa21;padding:.75rem 1.25rem;display:flex;justify-content:space-between;align-items:center}#pageBuilderWrapper .section-content{padding:1.25rem}#pageBuilderWrapper .row-form{margin:1rem 0;padding:1rem;border-radius:.5rem;background:#f8f9fa21}#pageBuilderWrapper .column-form{padding:1rem;margin:.5rem 0;border-radius:.5rem;background:#f8f9fa21}#pageBuilderWrapper .empty-sections{display:flex;flex-direction:column;align-items:center;justify-content:center;height:200px;border-radius:.5rem;border:2px dashed #ced4da;background-color:#f8f9fa21}#pageBuilderWrapper .style-controls{background:#f8f9fa21;padding:1rem;border-radius:.5rem;margin-top:1rem}#pageBuilderWrapper .gradient-color-row{display:flex;align-items:center;gap:10px;margin-bottom:5px}#pageBuilderWrapper .section-overview-card{background:#f8f9fa21;border-radius:.5rem;padding:.5rem .75rem;margin-bottom:.5rem;cursor:grab;transition:all .2s ease;position:relative;box-shadow:0 1px 2px rgba(0,0,0,.05)}#pageBuilderWrapper .section-overview-card:hover{border-color:#007bff;box-shadow:0 2px 5px rgba(0,0,0,.1);transform:translateY(-1px)}#pageBuilderWrapper .section-overview-card.dragging{opacity:.6;transform:rotate(1deg) scale(1.02);cursor:grabbing;z-index:1000}#pageBuilderWrapper .section-overview-card.drag-over-top{border-top:3px solid #28a745}#pageBuilderWrapper .section-overview-card.drag-over-bottom{border-bottom:3px solid #28a745}#pageBuilderWrapper .section-overview-card .section-type{font-size:.75rem;font-weight:600;color:#ced4da}#pageBuilderWrapper .section-overview-card .section-title{font-size:.875rem;font-weight:500;color:#f8f9fa;margin:2px 0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}#pageBuilderWrapper .section-overview-card .drag-handle{position:absolute;right:8px;top:50%;transform:translateY(-50%);color:#adb5bd}#toastContainer{position:fixed;top:80px;right:20px;z-index:9999}.rte-btn,.rte-select{background-color:#44475a;border:1px solid #6272a4;color:#f8f8f2;padding:.375rem .75rem;border-radius:.25rem;cursor:pointer;vertical-align:middle}.rte-btn:hover,.rte-select:hover{background-color:#6272a4}.rte-select{-webkit-appearance:none;appearance:none;padding-right:1.5rem;background-image:url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23F8F8F2%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');background-repeat:no-repeat;background-position:right .75rem center;background-size:.65em auto}#toastContainer .toast{background:#343a40;color:#f8f9fa;padding:12px 20px;border-radius:6px;margin-bottom:10px;box-shadow:0 4px 12px rgba(0,0,0,.3);border-left:4px solid #007bff;display:flex;align-items:center;gap:10px;min-width:280px;max-width:400px;animation:slideIn .3s forwards}#toastContainer .toast.success{border-left-color:#28a745}#toastContainer .toast.error{border-left-color:#dc3545}#toastContainer .toast-title{font-weight:700;font-size:.95rem}.rte-modal-btn-secondary{background-color:#6272a4;color:#f8f8f2;border:0;padding:.5rem 1rem;border-radius:.25rem;cursor:pointer}.rte-modal-btn-secondary:hover{background-color:#7a8ab8}.rte-modal-btn-primary{background-color:#50fa7b;color:#282a36;border:0;padding:.5rem 1rem;border-radius:.25rem;font-weight:700;cursor:pointer}.rte-modal-btn-primary:hover{background-color:#69ff91}.color-type-btn{background:transparent;color:#f8f8f2;border:none;padding:.5rem 1rem;border-radius:4px;cursor:pointer;font-size:.9rem}.color-type-btn.active{background-color:#6272a4}@keyframes slideIn{from{transform:translateX(100%);opacity:0}to{transform:translateX(0);opacity:1}}@keyframes slideOut{from{transform:translateX(0);opacity:1}to{transform:translateX(calc(100% + 40px));opacity:0}}#richTextEditor h1,#richTextEditor h2,#richTextEditor h3,#richTextEditor h4,#richTextEditor h5,#richTextEditor h6{color:#212529!important}
</style>

<script>
class FormPageBuilder{constructor(){this.pageData=[],this.editMode=!1,this.editPageId=null,this.sectionCounter=1,this.rowCounter=1,this.columnCounter=1,this.currentEditingColumnId=null,this.isPreviewMode=!1,this.draggedSectionIndex=void 0,this.currentUploadContext=null,this.savedSelection=null,this.currentColorAction=null,this.checkEditMode(),this.renderBuilderView()}updateProperty(t,e){const i=t.split("."),o=i.shift();let s;o.startsWith("section-")?s=this.findSectionById(o):o.startsWith("col-")&&(s=this.findColumnById(o));if(!s)return;let n=s;for(let t=0;t<i.length-1;t++){const e=i[t];n[e]&&"object"==typeof n[e]||(n[e]={}),n=n[e]}n[i[i.length-1]]=e;const r=o.startsWith("section-")?s:this.findSectionContainingColumn(o);r&&this.renderSectionHTML(r.id),this.renderSectionOverview()}addSection(t){const e=`section-${this.sectionCounter++}`,i={id:e,type:"hero-banner-with-overlay"===t?t:"standard-section",settings:{title:{text:"",style:{}},subtitle:{text:"",style:{}},body:[]}};"hero-banner-with-overlay"===t?Object.assign(i.settings,{backgroundImage:"https://via.placeholder.com/1920x1080",title:{text:"Hero Title",style:{color:"#ffffff",opacity:1}},subtitle:{text:"Captivating slogan.",style:{color:"#ffffff",opacity:.9}}}):Object.assign(i.settings,{title:{text:"Content Section",style:{color:"#f8f9fa"}},subtitle:{text:"Your main content.",style:{color:"#ced4da"}},body:[{id:`row-${this.rowCounter++}`,columns:[{id:`col-${this.columnCounter++}`,type:"paragraph",content:"<p>Start here.</p>"}]}]}),this.pageData.push(i),this.renderBuilderView(),setTimeout(()=>this.scrollToSection(e),100),this.showToast("success","Section Added")}deleteSection(t){confirm("Delete section?")&&(this.pageData=this.pageData.filter(e=>e.id!==t),this.renderBuilderView(),this.showToast("info","Section Deleted"))}addRow(t){const e=this.findSectionById(t);e&&(e.settings.body.push({id:`row-${this.rowCounter++}`,columns:[]}),this.renderSectionHTML(e.id),this.showToast("info","Row Added"))}deleteRow(t,e){confirm("Delete row?")&&(()=>{const i=this.findSectionById(t);i&&(i.settings.body=i.settings.body.filter(t=>t.id!==e),this.renderSectionHTML(i.id),this.showToast("info","Row Deleted"))})()}addColumn(t,e,i){const o=this.findSectionById(t);if(!o)return;const s=o.settings.body.find(t=>t.id===e);if(!s||s.columns.length>=3)return this.showToast("warning","Max Columns Reached");const n=`col-${this.columnCounter++}`;let r={id:n,type:i};switch(i){case"paragraph":r.content="<p>New text.</p>";break;case"image":r.content="https://via.placeholder.com/400x300",r.altText="Placeholder";break;case"video":r.content="",r.settings={controls:!0,autoplay:!1,loop:!1,muted:!1};break;case"audio":r.content="",r.settings={controls:!0,autoplay:!1,loop:!1}}s.columns.push(r),this.renderSectionHTML(t),this.showToast("success",`${i.toUpperCase()} Added`)}deleteColumn(t,e,i){confirm("Delete column?")&&(()=>{const o=this.findSectionById(t);if(!o)return;const s=o.settings.body.find(t=>t.id===e);if(!s)return;s.columns=s.columns.filter(t=>t.id!==i),this.renderSectionHTML(t),this.showToast("info","Column Deleted")})()}renderBuilderView(){const t=document.getElementById("sectionsContainer");this.pageData.length===0?t.innerHTML='<div class="empty-sections"><i class="fa fa-plus-circle fa-3x text-muted"></i><p class="text-muted mt-2">Click an "Add" button to start</p></div>':(t.innerHTML="",this.pageData.forEach(e=>{const i=document.createElement("div");i.id=e.id,i.className="section-form",t.appendChild(i),this.renderSectionHTML(e.id)})),this.renderSectionOverview()}renderSectionHTML(t){const e=document.getElementById(t),i=this.findSectionById(t);e&&i&&(e.innerHTML=this.getSectionHTML(i))}getSectionHTML(t){const e="hero-banner-with-overlay"===t.type;let i=`<div class="section-header"><div><strong>${e?"🖼️ Hero Banner":"📄 Content Section"}</strong><small class="text-muted"> (${t.id})</small></div><div><button class="btn btn-sm btn-danger"onclick="pageBuilder.deleteSection('${t.id}')"><i class="fa fa-trash"></i></button></div></div><div class="section-content">`;return e&&(i+=`<div class="form-group mb-4"><label class="small">BG Image</label><div class="input-group"><input type="text"class="form-control form-control-sm"value="${t.settings.backgroundImage||""}"onchange="pageBuilder.updateProperty('${t.id}.settings.backgroundImage',this.value)"placeholder="https://..."><div class="input-group-append"><button class="btn btn-sm btn-outline-secondary"onclick="pageBuilder.openMediaUpload('${t.id}','settings.backgroundImage','image')">Upload</button></div></div><img src="${t.settings.backgroundImage||""}"alt="Preview"style="max-width:100px;margin-top:5px;display:${t.settings.backgroundImage?"block":"none"}"></div>`),i+=`<div class="row"><div class="col-md-6"><div class="form-group mb-3"><label class="small">Title Text</label><input type="text"class="form-control form-control-sm"value="${t.settings.title?.text||""}"onchange="pageBuilder.updateProperty('${t.id}.settings.title.text',this.value)"></div>${this.getStyleControlsHTML(t.id,"title")}${e?"":`<div class="form-group mt-3"><label class="small">Title Icon</label><div class="input-group"><input type="text"class="form-control form-control-sm"value="${t.settings.title?.imageSrc||""}"onchange="pageBuilder.updateProperty('${t.id}.settings.title.imageSrc',this.value)"placeholder="https://..."><div class="input-group-append"><button class="btn btn-sm btn-outline-secondary"onclick="pageBuilder.openMediaUpload('${t.id}','settings.title.imageSrc','image')">Upload</button></div></div><img src="${t.settings.title?.imageSrc||""}"style="height:24px;margin-top:5px;display:${t.settings.title?.imageSrc?"block":"none"}"></div>`}</div><div class="col-md-6"><div class="form-group mb-3"><label class="small">Subtitle Text</label><input type="text"class="form-control form-control-sm"value="${t.settings.subtitle?.text||""}"onchange="pageBuilder.updateProperty('${t.id}.settings.subtitle.text',this.value)"></div>${this.getStyleControlsHTML(t.id,"subtitle")}</div></div>`,i+=`<hr class="my-4"><h6 class="mb-3">Content Rows</h6>`,t.settings.body.forEach(e=>{i+=this.getRowHTML(t.id,e)}),i+=`<div class="text-center mt-3"><button class="btn btn-success"onclick="pageBuilder.addRow('${t.id}')"><i class="fa fa-plus"></i> Add Row</button></div>`,i+"</div>"}getRowHTML(t,e){const i=12/(e.columns.length||1);let o=`<div class="row-form"><div class="d-flex justify-content-end mb-2"><button class="btn btn-sm btn-outline-danger"onclick="pageBuilder.deleteRow('${t}','${e.id}')"><i class="fa fa-trash"></i></button></div><div class="row">`;return e.columns.forEach(s=>{o+=`<div class="col-md-${i}">${this.getColumnHTML(t,e.id,s)}</div>`}),o+="</div>",e.columns.length<3&&(o+=`<div class="text-center mt-3 pt-3 border-top"><div class="btn-group"><button class="btn btn-sm btn-outline-primary"onclick="pageBuilder.addColumn('${t}','${e.id}','paragraph')"><i class="fa fa-paragraph"></i></button><button class="btn btn-sm btn-outline-success"onclick="pageBuilder.addColumn('${t}','${e.id}','image')"><i class="fa fa-image"></i></button><button class="btn btn-sm btn-outline-info"onclick="pageBuilder.addColumn('${t}','${e.id}','video')"><i class="fa fa-video-camera"></i></button><button class="btn btn-sm btn-outline-warning"onclick="pageBuilder.addColumn('${t}','${e.id}','audio')"><i class="fa fa-volume-up"></i></button></div></div>`),o+="</div>",o}getColumnHTML(t,e,i){let o=`<div class="column-form"><div class="d-flex justify-content-between align-items-center mb-2"><h6 class="mb-0 small text-muted">${i.type.toUpperCase()}</h6><button class="btn btn-sm btn-outline-danger"onclick="pageBuilder.deleteColumn('${t}','${e}','${i.id}')"><i class="fa fa-trash"></i></button></div>`;switch(i.type){case"paragraph":o+='<button class="btn btn-light btn-block"onclick="pageBuilder.openRichTextEditor(\''+i.id+"')\"><i class=\"fa fa-edit\"></i> Edit Content</button>";break;case"image":o+='<div class="form-group mb-2"><label class="small">Image</label><div class="input-group"><input type="text"class="form-control form-control-sm"value="'+(i.content||"")+'"onchange="pageBuilder.updateProperty(\''+i.id+'.content\',this.value)"><div class="input-group-append"><button class="btn btn-sm btn-outline-secondary"onclick="pageBuilder.openMediaUpload(\''+i.id+"','content','image')\">Upload</button></div></div></div>"+'<div class="form-group mb-0"><label class="small">Alt Text</label><input type="text"class="form-control form-control-sm"value="'+(i.altText||"")+'"onchange="pageBuilder.updateProperty(\''+i.id+'.altText\',this.value)"></div>';break;case"video":case"audio":o+='<div class="form-group mb-2"><label class="small">'+i.type+'</label><div class="input-group"><input type="text"class="form-control form-control-sm"value="'+(i.content||"")+'"onchange="pageBuilder.updateProperty(\''+i.id+'.content\',this.value)"><div class="input-group-append"><button class="btn btn-sm btn-outline-secondary"onclick="pageBuilder.openMediaUpload(\''+i.id+"','content','"+i.type+"')\">Upload</button></div></div></div>",["controls","autoplay","loop","muted"].forEach(t=>{if("audio"===i.type&&"muted"===t)return;o+='<div class="form-check small"><input type="checkbox"class="form-check-input"id="'+i.id+"-"+t+'"'+(i.settings?.[t]?"checked":"")+' onchange="pageBuilder.updateProperty(\''+i.id+".settings."+t+"',this.checked)\"><label class=\"form-check-label\"for=\""+i.id+"-"+t+'">'+t+"</label></div>"})}return o+="</div>",o}getStyleControlsHTML(t,e){const i=`${t}.settings.${e}.style`,o=this.findSectionById(t)?.settings[e]?.style||{},s=o.gradient?.colors?.length>0;let n=(o.gradient?.colors||[]).map((i,s)=>`<div class="gradient-color-row"><input type="color"class="form-control form-control-sm"value="${i}"onchange="pageBuilder.updateGradientColor('${t}','${e}',${s},this.value)"><button class="btn btn-sm btn-outline-danger"onclick="pageBuilder.removeGradientColor('${t}','${e}',${s})">×</button></div>`).join("");return`<div class="style-controls"><h6 class="mb-2 small">Styling</h6><div class="form-group mb-2"><label class="small">Opacity</label><input type="range"class="form-control-range"min="0"max="1"step="0.05"value="${o.opacity??1}"oninput="this.nextElementSibling.textContent=parseFloat(this.value).toFixed(2)"onchange="pageBuilder.updateProperty('${i}.opacity',parseFloat(this.value))"><span class="small text-muted">${(o.opacity??1).toFixed(2)}</span></div><div class="form-check form-check-inline"><input class="form-check-input"type="radio"name="${t}-${e}-color"id="${t}-${e}-solid"${s?"":"checked"} onchange="pageBuilder.toggleGradient('${t}','${e}',!this.checked)"><label class="form-check-label small"for="${t}-${e}-solid">Solid</label></div><div class="form-check form-check-inline"><input class="form-check-input"type="radio"name="${t}-${e}-color"id="${t}-${e}-gradient"${s?"checked":""} onchange="pageBuilder.toggleGradient('${t}','${e}',this.checked)"><label class="form-check-label small"for="${t}-${e}-gradient">Gradient</label></div><div class="mt-2"style="display:${s?"none":"block"};"><div class="form-group mb-0"><label class="small">Color</label><input type="color"class="form-control form-control-sm"value="${o.color||"#000000"}"onchange="pageBuilder.updateProperty('${i}.color',this.value)"></div></div><div class="mt-2"style="display:${s?"block":"none"};"><div class="form-group mb-2"><label class="small">Angle</label><input type="number"class="form-control form-control-sm"value="${o.gradient?.angle||90}"onchange="pageBuilder.updateProperty('${i}.gradient.angle',parseInt(this.value))"></div><div class="form-group mb-0"><label class="small">Colors</label><div id="${t}-${e}-gradient-colors">${n}</div><button class="btn btn-sm btn-outline-success mt-2"onclick="pageBuilder.addGradientColor('${t}','${e}')">+</button></div></div></div>`}toggleGradient(t,e,i){const o=this.findSectionById(t);if(!o)return;o.settings[e].style||(o.settings[e].style={}),i?(o.settings[e].style.gradient||(o.settings[e].style.gradient={angle:90,colors:["#ffffff","#000000"]}),delete o.settings[e].style.color):(delete o.settings[e].style.gradient,o.settings[e].style.color||(o.settings[e].style.color="#000000")),this.renderSectionHTML(t)}addGradientColor(t,e){const i=this.findSectionById(t);i&&i.settings[e]?.style?.gradient&&(i.settings[e].style.gradient.colors.push("#cccccc"),this.renderSectionHTML(t))}removeGradientColor(t,e,i){const o=this.findSectionById(t);o&&o.settings[e]?.style?.gradient&&o.settings[e].style.gradient.colors.length>1?(o.settings[e].style.gradient.colors.splice(i,1),this.renderSectionHTML(t)):this.showToast("warning","Minimum Colors")}updateGradientColor(t,e,i,o){const s=this.findSectionById(t);s&&s.settings[e]?.style?.gradient&&(s.settings[e].style.gradient.colors[i]=o)}openRichTextEditor(t){const e=this.findColumnById(t);e&&"paragraph"===e.type&&(this.currentEditingColumnId=t,document.getElementById("richTextEditor").innerHTML=e.content,document.getElementById("richTextModal").style.display="block")}closeRichTextModal(){this.closeColorPicker(!0),document.getElementById("richTextModal").style.display="none",this.currentEditingColumnId=null}saveRichText(){if(!this.currentEditingColumnId)return;this.updateProperty(`${this.currentEditingColumnId}.content`,document.getElementById("richTextEditor").innerHTML),this.closeRichTextModal(),this.showToast("success","Content Saved")}formatText(t,e=null){document.getElementById("richTextEditor").focus(),document.execCommand(t,!1,e)}insertLink(){const t=prompt("Enter URL:","https://");t&&this.formatText("createLink",t)}insertTable(){const t=prompt("Rows:","2"),e=prompt("Cols:","2");if(!t||!e)return;let i='<table style="width:100%;border-collapse:collapse"border="1"><thead><tr>';for(let o=0;o<parseInt(e);o++)i+=`<th>Header ${o+1}</th>`;i+="</tr></thead><tbody>";for(let o=0;o<parseInt(t);o++){i+="<tr>";for(let t=0;t<parseInt(e);t++)i+='<td style="border:1px solid #ddd;padding:8px">Cell</td>';i+="</tr>"}i+="</tbody></table>",this.formatText("insertHTML",i)}checkEditMode(){const t=new URLSearchParams(window.location.search),e=t.get("edit");e&&(this.editMode=!0,this.editPageId=e,this.loadPageForEdit(e))}loadPageForEdit(t){this.showToast("info","Loading Page..."),fetch(`${BASE_URL}api/v1/pages/show/${t}`).then(t=>t.json()).then(e=>{"success"===e.status?(this.pageData=e.data.pageData||[],document.getElementById("pageName").value=e.data.pageName,this.updateCountersFromData(),this.renderBuilderView(),this.showToast("success","Page Loaded",`"${e.data.pageName}" ready.`)):this.showToast("error","Load Failed",e.message||"err")}).catch(t=>this.showToast("error","Load Error",t.message))}updateCountersFromData(){let t=0,e=0,i=0;Array.isArray(this.pageData)&&this.pageData.forEach(o=>{const s=parseInt((o.id||"").replace(/[^0-9]/g,""))||0;s>t&&(t=s),o.settings&&Array.isArray(o.settings.body)&&o.settings.body.forEach(t=>{const s=parseInt((t.id||"").replace(/[^0-9]/g,""))||0;s>e&&(e=s),t.columns&&Array.isArray(t.columns)&&t.columns.forEach(t=>{const e=parseInt((t.id||"").replace(/[^0-9]/g,""))||0;e>i&&(i=e)})})}),this.sectionCounter=t+1,this.rowCounter=e+1,this.columnCounter=i+1}savePage(){const t=document.getElementById("pageName").value;if(!t)return this.showToast("warning","Page Name required");if(0===this.pageData.length)return this.showToast("warning","Add section");const e={pageName:t,pageData:this.pageData},i=this.editMode?`${BASE_URL}api/v1/pages/update/${this.editPageId}`:`${BASE_URL}api/v1/pages/create`,o=this.editMode?"PUT":"POST";this.showToast("info","Saving..."),fetch(i,{method:o,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}).then(t=>{if(!t.ok)return t.text().then(e=>{throw new Error(`HTTP ${t.status}: ${e}`)});return t.json()}).then(t=>{"success"===t.status?(this.showToast("success","Page Saved!"),setTimeout(()=>window.location.href=`${BASE_URL}dashboard/pages`,1500)):this.showToast("error","Save Error",t.message)}).catch(t=>this.showToast("error","Save Failed",t.message))}togglePreview(){this.isPreviewMode=!this.isPreviewMode,document.getElementById("builderView").style.display=this.isPreviewMode?"none":"block",document.getElementById("previewView").style.display=this.isPreviewMode?"block":"none",document.getElementById("previewToggleText").textContent=this.isPreviewMode?"Edit":"Preview",this.isPreviewMode&&this.renderPreview()}renderPreview(){const t=document.getElementById("previewContainer");if(0===this.pageData.length)return void(t.innerHTML='<div style="padding:2em;text-align:center;color:#888;">No content</div>');const e='<style>.page-content-renderer{font-family:system-ui,sans-serif;line-height:1.65;color:#3d4852;max-width:1536px;margin:0 auto}.page-content-renderer .page-section{padding:3rem 1.5rem}.hero-banner-section{position:relative;display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;min-height:36rem;color:#fff;background-size:cover;background-position:center}.hero-banner-section::before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background-color:rgba(0,0,0,.5)}.hero-banner-section .hero-content{position:relative;z-index:1;max-width:900px}.hero-banner-section .hero-title{font-size:clamp(2.5rem,8vw,4.5rem);font-weight:800}.hero-banner-section .hero-subtitle{font-size:clamp(1.2rem,4vw,1.5rem);font-weight:400}.page-content-renderer .section-title{display:flex;align-items:center;gap:1rem;font-size:clamp(2rem,5vw,2.75rem);font-weight:700;color:#2c3e50;border-bottom:3px solid #ecf0f1;padding-bottom:1rem;margin-bottom:.5rem}.page-content-renderer .section-title img{height:40px}.page-content-renderer .section-subtitle{font-size:1.2rem;color:#7f8c8d;margin:0}.page-content-renderer .content-row{display:grid;grid-template-columns:1fr;gap:2.5rem;margin-top:1.5rem}@media (min-width:768px){.page-content-renderer .content-row.two-column{grid-template-columns:1fr 1fr;align-items:center}}@media (min-width:1024px){.page-content-renderer .content-row.three-column{grid-template-columns:1fr 1fr 1fr;align-items:flex-start}}table{width:100%;border-collapse:collapse;margin:1em 0}th,td{border:1px solid #e0e6ed;padding:12px 15px}th{background-color:#f9fafb}</style>';let i=`<div class="page-content-renderer">${e}`;this.pageData.forEach(t=>{"hero-banner-with-overlay"===t.type?(()=>{const e=t.settings.backgroundImage?`style="background-image:url(${t.settings.backgroundImage});"`:"",o=this._generateTextStyles(t.settings.title?.style),s=this._generateTextStyles(t.settings.subtitle?.style);i+=`<section class="page-section hero-banner-section"${e}><div class="hero-content">${t.settings.title?.text?`<h1 class="hero-title"style="${o}">${t.settings.title.text}</h1>`:""}${t.settings.subtitle?.text?`<p class="hero-subtitle"style="${s}">${t.settings.subtitle.text}</p>`:""}</div></section>`})():(()=>{const e=this._generateTextStyles(t.settings.title?.style),o=this._generateTextStyles(t.settings.subtitle?.style);i+=`<section class="page-section">${t.settings.title?.text?`<h2 class="section-title"style="${e}">${t.settings.title.imageSrc?`<img src="${t.settings.title.imageSrc}"alt="">`:""}${t.settings.title.text}</h2>`:""}${t.settings.subtitle?.text?`<p class="section-subtitle"style="${o}">${t.settings.subtitle.text}</p>`:""}`,t.settings.body.forEach(t=>{const e=1===t.columns.length?"":2===t.columns.length?" two-column":" three-column";i+=`<div class="content-row${e}">`,t.columns.forEach(t=>{i+=`<div class="content-column">${this._renderColumnPreview(t)}</div>`}),i+="</div>"}),i+="</section>"})()}),i+="</div>",t.innerHTML=i}_generateTextStyles(t){if(!t)return"";let e=[];return t.opacity!==undefined&&e.push(`opacity:${t.opacity}`),t.gradient&&t.gradient.colors?.length>0?(e.push(`background:linear-gradient(${t.gradient.angle||90}deg,${t.gradient.colors.join(",")})`),e.push("background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;color:transparent")):t.color&&e.push(`color:${t.color}`),e.join(";")}_renderColumnPreview(t){const e="width:100%;height:auto;display:block;border-radius:.5rem;";switch(t.type){case"paragraph":return`<div>${t.content}</div>`;case"image":return`<img src="${t.content}"alt="${t.altText||""}"style="${e}">`;case"video":return`<video src="${t.content}"style="${e}"${t.settings?.controls?"controls":""}${t.settings?.autoplay?"autoplay":""}${t.settings?.loop?"loop":""}${t.settings?.muted?"muted":""}playsInline></video>`;case"audio":return`<audio src="${t.content}"style="width:100%"${t.settings?.controls?"controls":""}${t.settings?.autoplay?"autoplay":""}${t.settings?.loop?'loop':''}></audio>`;default:return''}}loadSampleData(){if(this.pageData.length>0&&!confirm("Replace current content?"))return;document.getElementById("pageName").value="sample-page",this.pageData=[{id:"section-hero-1",type:"hero-banner-with-overlay",settings:{backgroundImage:"https://images.unsplash.com/photo-1522202176988-66273c2fd55f",title:{text:"Welcome",style:{gradient:{angle:45,colors:["#f6d365","#fda085"]}}},subtitle:{text:"Your future begins here.",style:{color:"#ffffff",opacity:.95}},body:[]}},{id:"section-features-2",type:"standard-section",settings:{title:{text:"Our Programs",imageSrc:"https://www.svgrepo.com/show/444211/layout-columns.svg"},subtitle:{text:"A wide range of disciplines."},body:[{id:"row-2a",columns:[{id:"col-2a-1",type:"paragraph",content:"<h3>Engineering</h3><p>Innovate and build the future.</p>"}]}]}}],this.updateCountersFromData(),this.renderBuilderView(),this.showToast("success","Sample Loaded!")}showToast(t,e,i="",o=5e3){const s=document.getElementById("toastContainer"),n="t-"+Date.now(),r={success:"✅",error:"❌",warning:"⚠️",info:"ℹ️"},a=document.createElement("div");a.className=`toast ${t}`,a.id=n,a.innerHTML=`<div class="toast-icon">${r[t]}</div><div class="toast-content"><div class="toast-title">${e}</div><div>${i}</div></div><button class="toast-close"onclick="pageBuilder.closeToast('${n}')"style="background:none;border:none;color:#adb5bd;cursor:pointer;font-size:1.2rem;">×</button>`,s.appendChild(a),o>0&&setTimeout(()=>this.closeToast(n),o)}closeToast(t){const e=document.getElementById(t);e&&(e.style.animation="slideOut .3s forwards",setTimeout(()=>e.remove(),300))}findSectionById(t){return this.pageData.find(e=>e.id===t)}findColumnById(t){for(const e of this.pageData)for(const i of e.settings.body)for(const o of i.columns)if(o.id===t)return o;return null}findSectionContainingColumn(t){return this.pageData.find(e=>e.settings.body.some(i=>i.columns.some(e=>e.id===t)))}scrollToSection(t){const e=document.getElementById(t);e&&(e.scrollIntoView({behavior:"smooth",block:"start"}),e.style.transition="box-shadow .3s",e.style.boxShadow="0 0 0 4px rgba(0,123,255,.5)",setTimeout(()=>e.style.boxShadow="",1e3))}renderSectionOverview(){const t=document.getElementById("sectionOverview");if(0===this.pageData.length)return void(t.innerHTML='<div class="text-muted text-center py-3"><small>No sections yet</small></div>');let e="";this.pageData.forEach((i,o)=>{const s="hero-banner-with-overlay"===i.type,n=s?"🖼️":"📄",r=s?"Hero":"Content",a=i.settings.title?.text||"Untitled "+r,l=i.settings.body?.length||0;e+=`<div class="section-overview-card"draggable="true"data-section-index="${o}"ondragstart="pageBuilder.handleDragStart(event)"ondragover="pageBuilder.handleDragOver(event)"ondrop="pageBuilder.handleDrop(event)"ondragend="pageBuilder.handleDragEnd(event)"onclick="pageBuilder.scrollToSection('${i.id}')"><div class="section-type">${n} ${r}</div><div class="section-title">${a}</div><div class="section-meta"><span>${l} row${1!==l?"s":""}</span><span class="drag-handle">⋮⋮</span></div></div>`}),t.innerHTML=e}handleDragStart(t){this.draggedSectionIndex=parseInt(t.target.dataset.sectionIndex),t.target.classList.add("dragging")}handleDragOver(t){t.preventDefault();const e=t.target.closest(".section-overview-card");if(!e||e.classList.contains("dragging"))return;document.querySelectorAll(".section-overview-card").forEach(t=>t.classList.remove("drag-over-top","drag-over-bottom"));const i=e.getBoundingClientRect();e.classList.add(t.clientY<i.top+i.height/2?"drag-over-top":"drag-over-bottom")}handleDrop(t){t.preventDefault();const e=t.target.closest(".section-overview-card");if(!e||this.draggedSectionIndex===undefined)return this.cleanupDragState();const i=parseInt(e.dataset.sectionIndex),o=e.getBoundingClientRect();let s=t.clientY<o.top+o.height/2?i:i+1;this.draggedSectionIndex<i&&s--;if(this.draggedSectionIndex!==s){const[t]=this.pageData.splice(this.draggedSectionIndex,1);this.pageData.splice(s,0,t),this.renderBuilderView(),this.showToast("success","Section Reordered")}this.cleanupDragState()}handleDragEnd(t){this.cleanupDragState()}cleanupDragState(){document.querySelectorAll(".section-overview-card").forEach(t=>t.classList.remove("dragging","drag-over-top","drag-over-bottom")),this.draggedSectionIndex=undefined}openMediaUpload(t,e){this.currentUploadContext={targetId:t,propertyPath:e},document.getElementById("mediaFileInput").value="",document.getElementById("uploadProgress").style.display="none",document.getElementById("uploadPreview").style.display="none",document.getElementById("uploadBtn").disabled=!1,document.getElementById("mediaFileInput").accept="*",document.getElementById("mediaFileHelpText").textContent="All file types accepted. Max size: 200MB.",document.getElementById("mediaUploadModal").style.display="block"}closeMediaUploadModal(){document.getElementById("mediaUploadModal").style.display="none",this.currentUploadContext=null}previewSelectedFile(){const t=document.getElementById("mediaFileInput").files[0],e=document.getElementById("uploadPreview"),i=document.getElementById("previewContent");if(!t)return void(e.style.display="none");const o=URL.createObjectURL(t);let s="";t.type.startsWith("image/")?s=`<img src="${o}"alt="Preview"style="max-width:100%;max-height:200px">`:t.type.startsWith("video/")?s=`<video src="${o}"controls style="max-width:100%;max-height:200px">`:t.type.startsWith("audio/")&&(s=`<audio src="${o}"controls style="width:100%">`),i.innerHTML=s,e.style.display="block"}uploadFile(){const t=document.getElementById("mediaFileInput").files[0];if(!t)return this.showToast("warning","No file selected");document.getElementById("uploadProgress").style.display="block",document.getElementById("uploadBtn").disabled=!0;const e=new FormData;e.append("file",t),fetch(`${BASE_URL}api/v1/media/upload`,{method:"POST",body:e}).then(t=>{if(!t.ok)return t.text().then(e=>{throw new Error(`Upload failed: ${e}`)});return t.json()}).then(t=>{if("success"===t.status){const{targetId:e,propertyPath:i}=this.currentUploadContext;this.updateProperty(`${e}.${i}`,t.data.url),this.showToast("success","Upload Complete"),this.closeMediaUploadModal()}else this.showToast("error","Upload Error",t.message)}).catch(t=>this.showToast("error","Upload Failed",t.message)).finally(()=>{document.getElementById("uploadProgress").style.display="none",document.getElementById("uploadBtn").disabled=!1})}openColorPicker(t){const e=window.getSelection();if(!e.rangeCount||e.isCollapsed)return this.showToast("warning","Select Text First");this.savedSelection=e.getRangeAt(0).cloneRange(),this.currentColorAction=t,document.getElementById("colorPickerTitle").textContent="foreColor"===t?"Text Color":"Highlight Color",document.getElementById("colorPickerModal").style.display="block"}closeColorPicker(){this.savedSelection=null,this.currentColorAction=null,document.getElementById("colorPickerModal").style.display="none"}setColorPickerType(t){t = "gradient";document.getElementById("solidColorControls").style.display="solid"===t?"block":"none",document.getElementById("gradientColorControls").style.display="gradient"===t?"block":"none";const e=document.getElementById("colorTypeSolid"),i=document.getElementById("colorTypeGradient");e.classList.toggle("active","solid"===t),i.classList.toggle("active","gradient"===t),e.style.backgroundColor="solid"===t?"#6272a4":"transparent",i.style.backgroundColor="gradient"===t?"#6272a4":"transparent","gradient"===t&&0===document.getElementById("gradientColorsContainer").children.length&&(this.addGradientColorStop(),this.addGradientColorStop())}addGradientColorStop(){const t=document.createElement("div");t.style.cssText="display:flex;align-items:center;gap:10px;margin-bottom:5px;";const e=document.createElement("input");e.type="color",e.value="#ffffff",e.style.cssText="width:100%;height:30px;border:none;border-radius:4px;background-color:#282a36;";const i=document.createElement("button");i.innerHTML="×",i.style.cssText="background-color:#ff5555;color:#f8f8f2;border:none;border-radius:4px;cursor:pointer;width:30px;height:30px;",i.onclick=()=>t.remove(),t.appendChild(e),t.appendChild(i),document.getElementById("gradientColorsContainer").appendChild(t)}hexToRgba(t,e=1){let i;const o=(i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t))?{r:parseInt(i[1],16),g:parseInt(i[2],16),b:parseInt(i[3],16)}:null;return o?`rgba(${o.r},${o.g},${o.b},${e})`:t}applyRteColor(){if(!this.savedSelection)return;const t=window.getSelection();t.removeAllRanges(),t.addRange(this.savedSelection);const e=document.getElementById("richTextEditor");e.focus();const i=document.getElementById("solidColorControls").style.display,o=document.getElementById("colorOpacity").value;if("block"===i){const t=document.getElementById("solidColorInput").value,e=this.hexToRgba(t,o);this.formatText(this.currentColorAction,e)}else{const t=this.savedSelection.toString(),e=document.createElement("span");e.textContent=t;const i=document.getElementById("gradientAngle").value,s=Array.from(document.getElementById("gradientColorsContainer").children).map(t=>t.querySelector("input").value);"foreColor"===this.currentColorAction?(e.style.background=`linear-gradient(${i}deg, ${s.join(",")})`,e.style.webkitBackgroundClip="text",e.style.webkitTextFillColor="transparent",e.style.opacity=o):(e.style.background=`linear-gradient(${i}deg, ${s.join(",")})`,e.style.opacity=o);const n=this.savedSelection.extractContents();this.savedSelection.insertNode(e)}this.closeColorPicker()}openJsonModal(){document.getElementById("jsonImportModal").style.display="block",document.getElementById("jsonInput").value=""}closeJsonModal(){document.getElementById("jsonImportModal").style.display="none"}loadFromJson(){const t=document.getElementById("jsonInput").value;if(!t.trim())return this.showToast("warning","Input is empty","Please paste your JSON data.");let e;try{e=JSON.parse(t)}catch(t){return this.showToast("error","Invalid JSON",`Parsing failed: ${t.message}`)}const i=this.validatePageData(e);if(!i.isValid)return this.showToast("error","Invalid Data Structure",i.error);if(this.pageData.length>0&&!confirm("This will replace all current content. Are you sure?"))return;document.getElementById("pageName").value="imported-page",this.pageData=e,this.updateCountersFromData(),this.renderBuilderView(),this.closeJsonModal(),this.showToast("success","JSON Loaded","Page data has been successfully imported.")}validatePageData(t){if(!Array.isArray(t))return{isValid:!1,error:"The root element must be an array []."};for(let e=0;e<t.length;e++){const i=t[e],o=`Section ${e+1} (${i.id||"no id"})`;if("object"!=typeof i||null===i)return{isValid:!1,error:`${o} must be an object.`};if("string"!=typeof i.id)return{isValid:!1,error:`${o} is missing required 'id' (string).`};if("string"!=typeof i.type)return{isValid:!1,error:`${o} is missing required 'type' (string).`};if("object"!=typeof i.settings||null===i.settings)return{isValid:!1,error:`${o} is missing required 'settings' (object).`};if(!Array.isArray(i.settings.body))return{isValid:!1,error:`${o} needs a 'body' array in its settings.`};for(let t=0;t<i.settings.body.length;t++){const e=i.settings.body[t],s=`${o} > Row ${t+1} (${e.id||"no id"})`;if("object"!=typeof e||null===e)return{isValid:!1,error:`${s} must be an object.`};if("string"!=typeof e.id)return{isValid:!1,error:`${s} is missing required 'id' (string).`};if(!Array.isArray(e.columns))return{isValid:!1,error:`${s} is missing required 'columns' (array).`};for(let t=0;t<e.columns.length;t++){const i=e.columns[t],o=`${s} > Column ${t+1} (${i.id||"no id"})`,n=["paragraph","image","video","audio"];if("object"!=typeof i||null===i)return{isValid:!1,error:`${o} must be an object.`};if("string"!=typeof i.id)return{isValid:!1,error:`${o} is missing required 'id' (string).`};if("string"!=typeof i.type||!n.includes(i.type))return{isValid:!1,error:`${o} 'type' must be one of: ${n.join(", ")}.`};if("string"!=typeof i.content&&(void 0===i.content||null===i.content))return{isValid:!1,error:`${o} is missing required 'content' (string).`}}}}return{isValid:!0,error:null}}}
let pageBuilder;document.addEventListener("DOMContentLoaded",()=>{pageBuilder=new FormPageBuilder});
</script>

<?= $this->endSection(); ?>