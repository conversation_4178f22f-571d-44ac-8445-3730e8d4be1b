<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <div class="row mt-3">
                <div class="col-lg-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="card-title d-flex justify-content-between align-items-center">
                                <h4>📄 Create New Page</h4>
                                <a href="<?= base_url('dashboard/pages') ?>" class="btn btn-secondary">
                                    <i class="fa fa-arrow-left"></i> Back to Pages
                                </a>
                            </div>
                            <hr>

                            <form id="createPageForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="pageName">Page Name *</label>
                                            <input type="text" class="form-control" id="pageName" name="pageName" required>
                                            <small class="form-text text-muted">This will be used in the URL: /api/v1/pages/name/your-page-name</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="pageData">Page Data (JSON) *</label>
                                    <textarea class="form-control" id="pageData" name="pageData" rows="20" required></textarea>
                                    <small class="form-text text-muted">Enter the page structure as JSON array</small>
                                </div>

                                <div class="form-group">
                                    <button type="button" class="btn btn-info" onclick="loadSampleData()">
                                        <i class="fa fa-file-code-o"></i> Load Sample Data
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="validateJSON()">
                                        <i class="fa fa-check"></i> Validate JSON
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fa fa-save"></i> Create Page
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i></a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <?= $this->include('partials/footer'); ?>
    <!--End footer-->

</div>
<!--End wrapper-->

<script>
const samplePageData = [
    {
        "id": "section-hero",
        "type": "hero-banner-with-overlay",
        "settings": {
            "backgroundImage": "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?auto=format&fit=crop&w=1771",
            "title": {
                "text": "Welcome to Our New Page",
                "style": {
                    "gradient": {
                        "angle": 45,
                        "colors": ["#f6d365", "#fda085"]
                    }
                }
            },
            "subtitle": {
                "text": "This is a sample page created with the CMS system.",
                "style": {
                    "color": "#ffffff",
                    "opacity": 0.95
                }
            },
            "body": []
        }
    },
    {
        "id": "section-content",
        "type": "documentation-section",
        "settings": {
            "title": { "text": "Sample Content Section" },
            "subtitle": { "text": "This is how you can structure your content." },
            "body": [{
                "id": "row-1",
                "columns": [
                    { 
                        "id": "col-1", 
                        "type": "paragraph", 
                        "content": "<h3>Welcome</h3><p>This is a sample paragraph with <strong>bold text</strong> and <em>italic text</em>.</p>" 
                    },
                    { 
                        "id": "col-2", 
                        "type": "image", 
                        "content": "https://images.unsplash.com/photo-1556740738-b6a63e27c4df?auto=format&fit=crop&w=1170", 
                        "altText": "Sample image" 
                    }
                ]
            }]
        }
    }
];

function loadSampleData() {
    document.getElementById('pageData').value = JSON.stringify(samplePageData, null, 2);
}

function validateJSON() {
    const jsonText = document.getElementById('pageData').value;
    try {
        JSON.parse(jsonText);
        alert('✅ Valid JSON!');
    } catch (e) {
        alert('❌ Invalid JSON: ' + e.message);
    }
}

document.getElementById('createPageForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const pageName = document.getElementById('pageName').value;
    const pageDataText = document.getElementById('pageData').value;
    
    let pageData;
    try {
        pageData = JSON.parse(pageDataText);
    } catch (e) {
        alert('Invalid JSON: ' + e.message);
        return;
    }
    
    const data = {
        pageName: pageName,
        pageData: pageData
    };
    
    fetch(`${BASE_URL}api/v1/pages/create`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            alert('Page created successfully!');
            window.location.href = `${BASE_URL}dashboard/pages`;
        } else {
            alert('Error: ' + (data.message || 'Failed to create page'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the page');
    });
});
</script>

<?= $this->endSection(); ?>