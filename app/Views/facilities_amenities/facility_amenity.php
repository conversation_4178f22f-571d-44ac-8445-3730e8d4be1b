<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->


            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Facilities & Amenities</h4>
                    <hr>
                    <div class="row gy-3">
                        <div class="col-md-2 text-end d-grid">
                            <!-- Collapse Trigger Button -->
                            <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#bannerFormCollapse" aria-expanded="false" aria-controls="bannerFormCollapse">
                                Create New
                            </button>
                        </div>
                    </div>

                    <!-- Collapsible Form -->
                    <div class="collapse mt-4" id="bannerFormCollapse">

                        <form class="row g-3" id="facility_amenityForm" enctype="multipart/form-data">
                            <div class="col-md-6">
                                <label for="categoryName" class="form-label">Category Name</label>
                                <input type="text" name="categoryName" class="form-control" id="categoryName" placeholder="Enter category name" required>
                            </div>

                            <div class="col-md-6">
                                <label for="categoryIcon" class="form-label">Category Icon</label>
                                <input type="file" name="categoryIcon" class="form-control" id="categoryIcon" placeholder="Enter category icon" required>
                            </div>

                            <div class="col-md-6">
                                <label for="categoryTitle" class="form-label">Category Title</label>
                                <input type="text" name="categoryTitle" class="form-control" id="categoryTitle" placeholder="Enter link" required>
                            </div>

                            <div class="col-md-6">
                                <label for="categoryDescription" class="form-label">Category Description</label>
                                <textarea type="text" name="categoryDescription" class="form-control" id="categoryDescription" placeholder="Enter link" required></textarea>
                            </div>

                            <div class="col-md-6">
                                <label for="categoryBanner" class="form-label">Category Banner</label>
                                <input type="file" name="categoryBanner" class="form-control" id="categoryBanner" placeholder="Enter link" required>
                            </div>

                            <div class="col-12 mt-2">
                                <button type="submit" class="btn btn-light px-5">Submit</button>
                            </div>
                        </form>

                    </div>




                </div>

            </div>
            <div class="form-row mt-3">
                <div class="col-12">
                    <div id="todo-container">
                        <?php foreach ($facilities as $facility): ?>
                            <div class="pb-3 todo-item">




                                <div class="accordion" id="accordionExample">
                                    <div class="card shadow-sm mb-3">
                                        <!-- Card Header with Editable Fields -->
                                        <div class="card-header d-flex justify-content-between align-items-center" data-toggle="collapse" data-target="#collapseOne_<?= $facility->id ?>" aria-expanded="true" aria-controls="collapseOne" id="headingOne">
                                            <h5 class="mb-0 d-flex align-items-center">
                                                <button class="btn btn-link" type="button">
                                                    <?= $facility->category_name ?>
                                                </button>
                                            </h5>

                                            <!-- Category Icon + Name (Editable) -->
                                            <div class="d-flex align-items-center">
                                                <!-- Category Icon -->
                                                <?php if (!empty($facility->category_icon)): ?>
                                                    <img src="<?= base_url() . 'uploads/facilities/' . esc($facility->category_icon) ?>" 
                                                         alt="Category Icon" 
                                                         class="rounded-circle me-2" 
                                                         style="width: 30px; height: 30px; object-fit: cover;" />
                                                <?php else: ?>
                                                    <div class="bg-light rounded-circle d-flex align-items-center justify-content-center me-2" 
                                                         style="width: 30px; height: 30px;">
                                                        <i class="fa fa-image text-muted"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Card Body (Collapsible Content) -->
                                        <div id="collapseOne_<?= $facility->id ?>" class="collapse" aria-labelledby="headingOne" data-parent="#accordionExample">
                                            <form id="facilityForm_<?= $facility->id ?>" class="editamenity_form" enctype="multipart/form-data">
                                                <input type="hidden" name="id" class="facility_amenities" value="<?= $facility->id ?>">
                                                <div class="card-body">
                                                    <?php $categoryData = json_decode($facility->category_data); ?>
                                                    <!-- Category Icon Update -->
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <p><strong>Category Icon:</strong></p>
                                                            <div class="current-icon-display mb-2">
                                                                <?php if (!empty($facility->category_icon)): ?>
                                                                    <img src="<?= base_url() . 'uploads/facilities/' . esc($facility->category_icon) ?>" 
                                                                         alt="Current Icon" 
                                                                         class="rounded border" 
                                                                         style="width: 60px; height: 60px; object-fit: cover;">
                                                                    <div class="small text-muted mt-1">Current Icon</div>
                                                                <?php else: ?>
                                                                    <div class="bg-light rounded border d-flex align-items-center justify-content-center" 
                                                                         style="width: 60px; height: 60px;">
                                                                        <i class="fa fa-image text-muted"></i>
                                                                    </div>
                                                                    <div class="small text-muted mt-1">No Icon</div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <input type="file" 
                                                                   name="categoryIcon" 
                                                                   class="form-control form-control-sm" 
                                                                   accept="image/*"
                                                                   data-preview="icon_preview_<?= $facility->id ?>">
                                                            <div id="icon_preview_<?= $facility->id ?>" class="mt-2"></div>
                                                            <small class="text-muted">Leave empty to keep current icon. Max size: 2MB</small>
                                                        </div>
                                                        
                                                        <!-- Category Title -->
                                                        <div class="col-md-6">
                                                            <p><strong>Category Title:</strong></p>
                                                            <div class="input-group">
                                                                <input name="title" style="color: #ffff !important;" type="text" class="form-control form-control-sm" value="<?= esc($categoryData->title ?? '') ?>" readonly />
                                                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="editField(this)">
                                                                    <i class="zmdi zmdi-edit text-white"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- New Row for Category Description and Banner -->
                                                    <div class="row mb-3">
                                                        <!-- Category Description -->
                                                        <div class="col-md-6">
                                                            <p><strong>Category Description:</strong></p>
                                                            <div class="input-group">
                                                                <input name="description" style="color: #ffff !important;" type="text" class="form-control form-control-sm" value="<?= esc($categoryData->description ?? '') ?>" readonly />
                                                                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="editField(this)">
                                                                    <i class="zmdi zmdi-edit text-white"></i>
                                                                </button>
                                                            </div>
                                                        </div>

                                                        <!-- Category Banner -->
                                                        <div class="col-md-6">
                                                            <p><strong>Category Banner:</strong></p>
                                                            <div class="current-banner-display mb-2">
                                                                <?php if (!empty($categoryData->banner)): ?>
                                                                    <img src="<?= base_url() . 'uploads/facilities/' . esc($categoryData->banner) ?>" 
                                                                         alt="Current Banner" 
                                                                         class="img-fluid rounded border" 
                                                                         style="max-width: 200px; max-height: 120px; object-fit: cover;">
                                                                    <div class="small text-muted mt-1">Current Banner</div>
                                                                <?php else: ?>
                                                                    <div class="bg-light rounded border d-flex align-items-center justify-content-center" 
                                                                         style="width: 200px; height: 120px;">
                                                                        <i class="fa fa-image fa-2x text-muted"></i>
                                                                    </div>
                                                                    <div class="small text-muted mt-1">No Banner</div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <input type="file" 
                                                                   name="banner" 
                                                                   class="form-control form-control-sm" 
                                                                   accept="image/*"
                                                                   data-preview="banner_preview_<?= $facility->id ?>">
                                                            <div id="banner_preview_<?= $facility->id ?>" class="mt-2"></div>
                                                            <small class="text-muted">Leave empty to keep current banner. Max size: 5MB</small>
                                                        </div>
                                                    </div>

                                                    <!-- Optional Delete Button -->
                                                    <div class="row mt-4">
                                                        <div class="col-md-12 text-end">
                                                            <button class="btn btn-outline-primary btn-sm" type="submit">
                                                                <i class="bi bi-edit"></i> Edit
                                                            </button>
                                                            <button class="btn btn-outline-danger btn-sm" type="button" onclick="Deletefacility(<?= $facility->id ?>);">
                                                                <i class="bi bi-edit"></i> Delete
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        <?php endforeach; ?>

                    </div>
                </div>




                <!--End Dashboard Content-->

                <!--start overlay-->
                <div class="overlay toggle-menu"></div>
                <!--end overlay-->

            </div>

            <!-- End container-fluid-->

        </div><!--End content-wrapper-->
        <div class="row">
            <div class="col-md-12">
                <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
            </div>
        </div>
        <!--Start Back To Top Button-->
        <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
        <!--End Back To Top Button-->

        <!--Start footer-->

        <!--End footer-->


    </div><!--End wrapper-->

    <!--start color switcher-->
    <?= $this->include('partials/colorswitcher'); ?>
    <!--end color switcher-->

    <?= $this->endSection(); ?>

    <?= $this->section('scripts'); ?>
    <!-- Chart.js -->
    <script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
    
    <!-- File Update Handler -->
    <script src="<?= base_url('assets/js/file-update-handler.js'); ?>"></script>
    
    <!-- Dashboard Index Script -->
    <script src="<?= base_url('assets/js/index.js'); ?>"></script>

    <script>
        $('#facility_amenityForm').on('submit', function(e) {

            e.preventDefault();

            const formData = new FormData(this); // Automatically includes file input

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxAddfacility_amenity", formData, function(res) {
                if (res.status === 'success') {

                    // $('#editNoticeForm')[0].reset(); // Optional reset
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            });
        });
        $('.editamenity_form').on('submit', function(e) {

            e.preventDefault();

            const formData = new FormData(this); // Automatically includes file input

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxEditfacility_amenity", formData, function(res) {
                if (res.status === 'success') {
                    showAlert('success', res.message);
                    if (res.icon_info) {
                        console.log('Icon updated:', res.icon_info);
                    }
                    if (res.banner_info) {
                        console.log('Banner updated:', res.banner_info);
                    }
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('error', res.message || 'Failed to update facility');
                }
            });
        });


       function Deletefacility(id) {
            if (confirm("Are you sure you want to delete this facility?")) {
                $.ajax({
                    url: "<?= base_url('dashboard/ajaxDeletefacility_amenity'); ?>",
                    type: "POST",
                    data: { id: id },
                    success: function(response) {
                        if (response.status === 'success') {
                            showAlert('success', response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1500);
                        } else {
                            showAlert('error', response.message || 'Failed to delete facility');
                        }
                    },
                    error: function() {
                        showAlert('error', 'An error occurred while deleting the facility');
                    }
                });
            }
        }
    </script>
    <script>
        function editField(btn) {
            const input = btn.previousElementSibling;
            input.readOnly = false;
            input.focus();
        }

        // Global variable to hold the selected file
        let selectedBannerFile = null;

        function changeImage(btn) {
            const fileInput = btn.nextElementSibling;
            fileInput.click();
        }

        function previewImage(event, input) {
            const img = input.closest('.position-relative').querySelector('img');

            if (event.target.files && event.target.files[0]) {
                const file = event.target.files[0];
                selectedBannerFile = file; // Save the selected file globally

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }
        }
    </script>

    <?= $this->endSection(); ?>