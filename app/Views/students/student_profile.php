<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>

<!--Start sidebar-wrapper-->
<?= $this->include('partials/sidebar'); ?>
<!--End sidebar-wrapper-->

<!--Start topbar header-->
<?= $this->include('partials/topbar'); ?>
<!--End topbar header-->

<div class="clearfix"></div>

<div class="content-wrapper">
    <div class="container-fluid">
        
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <!-- Profile Photo and Basic Info -->
                            <div class="col-md-4">
                                <div class="text-center">
                                    <?php if (!empty($student['photo'])): ?>
                                        <img src="<?= base_url('uploads/students/' . $student['photo']) ?>" 
                                             alt="<?= esc($student['full_name']) ?>" 
                                             class="img-fluid rounded-circle mb-3" 
                                             style="width: 200px; height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                             style="width: 200px; height: 200px;">
                                            <i class="zmdi zmdi-account" style="font-size: 80px; color: white;"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h3><?= esc($student['full_name']) ?></h3>
                                    <p class="text-muted"><?= esc($student['student_id']) ?></p>
                                    <p class="text-muted"><?= esc($student['program_type']) ?> Student</p>
                                    <p class="text-muted"><?= esc($student['department_name']) ?></p>
                                    
                                    <div class="mt-3">
                                        <span class="badge badge-<?= $student['status'] === 'current' ? 'success' : ($student['status'] === 'alumni' ? 'primary' : 'warning') ?> mr-1">
                                            <?= ucfirst($student['status']) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Detailed Information -->
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h4>Contact Information</h4>
                                        <hr>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Email:</strong> 
                                            <a href="mailto:<?= esc($student['email']) ?>">
                                                <?= esc($student['email']) ?>
                                            </a>
                                        </p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Contact Number:</strong> <?= esc($student['contact_number']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <p><strong>Address:</strong> <?= esc($student['address']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-12 mt-4">
                                        <h4>Academic Information</h4>
                                        <hr>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Program Type:</strong> <?= esc($student['program_type']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Faculty:</strong> <?= esc($student['faculty_name']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Admission Year:</strong> <?= esc($student['admission_year']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Expected Graduation:</strong> 
                                            <?= !empty($student['graduation_year']) ? esc($student['graduation_year']) : 'Not specified' ?>
                                        </p>
                                    </div>
                                    
                                    <?php if (!empty($student['guardian_name']) || !empty($student['guardian_contact'])): ?>
                                        <div class="col-md-12 mt-4">
                                            <h4>Guardian Information</h4>
                                            <hr>
                                        </div>
                                        
                                        <?php if (!empty($student['guardian_name'])): ?>
                                            <div class="col-md-6">
                                                <p><strong>Guardian Name:</strong> <?= esc($student['guardian_name']) ?></p>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($student['guardian_contact'])): ?>
                                            <div class="col-md-6">
                                                <p><strong>Guardian Contact:</strong> <?= esc($student['guardian_contact']) ?></p>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    
                                    <div class="col-md-12 mt-4">
                                        <h4>Academic Timeline</h4>
                                        <hr>
                                        <div class="timeline">
                                            <div class="timeline-item">
                                                <div class="timeline-marker bg-primary"></div>
                                                <div class="timeline-content">
                                                    <h6>Admission</h6>
                                                    <p>Joined in <?= esc($student['admission_year']) ?></p>
                                                </div>
                                            </div>
                                            
                                            <?php if ($student['status'] === 'alumni' && !empty($student['graduation_year'])): ?>
                                                <div class="timeline-item">
                                                    <div class="timeline-marker bg-success"></div>
                                                    <div class="timeline-content">
                                                        <h6>Graduation</h6>
                                                        <p>Graduated in <?= esc($student['graduation_year']) ?></p>
                                                    </div>
                                                </div>
                                            <?php elseif ($student['status'] === 'current' && !empty($student['graduation_year'])): ?>
                                                <div class="timeline-item">
                                                    <div class="timeline-marker bg-info"></div>
                                                    <div class="timeline-content">
                                                        <h6>Expected Graduation</h6>
                                                        <p>Expected in <?= esc($student['graduation_year']) ?></p>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <a href="<?= base_url('dashboard/manageStudents') ?>" class="btn btn-secondary">
                                    <i class="zmdi zmdi-arrow-left"></i> Back to Students
                                </a>
                                <a href="<?= base_url('dashboard/editStudent/' . $student['record_id']) ?>" class="btn btn-primary">
                                    <i class="zmdi zmdi-edit"></i> Edit Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -23px;
    top: 5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.timeline-content h6 {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.timeline-content p {
    margin: 0;
    color: #6c757d;
}
</style>
<?= $this->endSection(); ?>