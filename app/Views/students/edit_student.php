<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Content-->
            <div class="col-lg-10 offset-lg-1 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="zmdi zmdi-edit"></i> Edit Student: <?= esc($student['full_name']) ?>
                        </h5>
                        <hr>
                        <!-- The form structure is now identical to create_student.php -->
                        <form id="editStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="full_name">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?= esc($student['full_name'] ?? '') ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="student_id">Student ID <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="student_id" name="student_id" 
                                               value="<?= esc($student['student_id'] ?? '') ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?= esc($student['email'] ?? '') ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_number">Contact Number</label>
                                        <input type="tel" class="form-control" id="contact_number" name="contact_number" 
                                               value="<?= esc($student['contact_number'] ?? '') ?>" maxlength="10">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="department_id">Department <span class="text-danger">*</span></label>
                                        <select class="form-control" id="department_id" name="department_id" required>
                                            <option value="">Select Department</option>
                                            <?php if (isset($departments)): ?>
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= esc($dept['id']); ?>" <?= ($student['department_id'] ?? '') == $dept['id'] ? 'selected' : '' ?>>
                                                        <?= esc($dept['department_name']); ?> 
                                                        (<?= esc($dept['faculty_name'] ?? 'No Faculty'); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="program_type">Program Type <span class="text-danger">*</span></label>
                                        <select class="form-control" id="program_type" name="program_type" required>
                                            <option value="">Select Program</option>
                                            <option value="Bachelor's" <?= ($student['program_type'] ?? '') === "Bachelor's" ? 'selected' : '' ?>>Bachelor's</option>
                                            <option value="Master's" <?= ($student['program_type'] ?? '') === "Master's" ? 'selected' : '' ?>>Master's</option>
                                            <option value="MPhil" <?= ($student['program_type'] ?? '') === "MPhil" ? 'selected' : '' ?>>MPhil</option>
                                            <option value="PhD" <?= ($student['program_type'] ?? '') === "PhD" ? 'selected' : '' ?>>PhD</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-calendar"></i> Academic Timeline</h6>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="admission_year">Admission Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="admission_year" name="admission_year" 
                                               value="<?= esc($student['admission_year'] ?? '') ?>" min="2000" max="<?= date('Y') + 1; ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="expected_completion_year">Expected Completion Year</label>
                                        <input type="number" class="form-control" id="expected_completion_year" name="expected_completion_year" 
                                               value="<?= esc($student['expected_completion_year'] ?? '') ?>" min="2000" max="2040">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="current_semester">Current Semester</label>
                                        <input type="number" class="form-control" id="current_semester" name="current_semester" 
                                               value="<?= esc($student['current_semester'] ?? '') ?>" min="1" max="12">
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-chart"></i> Academic Performance</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cgpa">CGPA</label>
                                        <input type="number" class="form-control" id="cgpa" name="cgpa" 
                                               value="<?= esc($student['cgpa'] ?? '') ?>" min="0" max="4" step="0.01">
                                        <small class="form-text text-muted">On a scale of 0.00 to 4.00</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="current" <?= ($student['status'] ?? '') === 'current' ? 'selected' : '' ?>>Current Student</option>
                                            <option value="alumni" <?= ($student['status'] ?? '') === 'alumni' ? 'selected' : '' ?>>Alumni</option>
                                            <option value="dropped" <?= ($student['status'] ?? '') === 'dropped' ? 'selected' : '' ?>>Dropped</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- This row is shown or hidden by JavaScript based on the Status dropdown -->
                            <div class="row" id="completionYearRow" style="display: <?= ($student['status'] ?? 'current') === 'alumni' ? 'block' : 'none' ?>;">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="actual_completion_year">Actual Completion Year</label>
                                        <input type="number" class="form-control" id="actual_completion_year" name="actual_completion_year" 
                                               value="<?= esc($student['actual_completion_year'] ?? '') ?>" min="2000" max="<?= date('Y') + 5; ?>">
                                        <small class="form-text text-muted">Required if status is Alumni</small>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Additional Information</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="photo">Update Profile Photo</label>
                                        <input type="file" class="form-control-file" id="photo" name="photo" accept="image/*">
                                        <small class="form-text text-muted">Optional: Upload a new profile photo</small>
                                        <div class="mt-2">
                                            <!-- Show current photo if it exists -->
                                            <?php if (!empty($student['photo'])): ?>
                                                <p><small>Current Photo:</small></p>
                                                <img id="photo_preview" src="<?= base_url('uploads/students/' . $student['photo']); ?>" alt="Current Photo" 
                                                     style="max-width: 150px; max-height: 150px;" />
                                            <?php else: ?>
                                                <img id="photo_preview" src="#" alt="Photo Preview" 
                                                     style="display:none; max-width: 150px; max-height: 150px;" />
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="zmdi zmdi-check"></i> Update Student
                                </button>
                                <a href="<?= base_url('/dashboard/manageStudents'); ?>" class="btn btn-secondary btn-lg ml-2">
                                    <i class="zmdi zmdi-close"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!--End Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                 Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- JavaScript is copied from create_student and combined with the update fetch call -->
<script>
// Photo preview functionality
document.getElementById('photo').addEventListener('change', function(event) {
    const [file] = event.target.files;
    const preview = document.getElementById('photo_preview');
    if (file) {
        preview.src = URL.createObjectURL(file);
        preview.style.display = 'block';
    }
});

// Contact number validation
$('#contact_number').on('input', function() {
    this.value = this.value.replace(/[^0-9]/g, '');
});

// Status change handling
$('#status').on('change', function() {
    const status = $(this).val();
    if (status === 'alumni') {
        $('#completionYearRow').show();
        $('#actual_completion_year').prop('required', true);
    } else {
        $('#completionYearRow').hide();
        $('#actual_completion_year').prop('required', false);
        $('#actual_completion_year').val('');
    }
});

// Auto-calculate expected completion year based on program type
$('#program_type, #admission_year').on('change', function() {
    const programType = $('#program_type').val();
    const admissionYear = parseInt($('#admission_year').val());
    
    if (programType && admissionYear) {
        let duration = 4; // Default for Bachelor's
        switch (programType) {
            case "Master's": duration = 2; break;
            case "MPhil": duration = 2; break;
            case "PhD": duration = 4; break;
        }
        $('#expected_completion_year').val(admissionYear + duration);
    }
});

// Form submission for UPDATING a student
$('#editStudentForm').on('submit', function(e) {
    e.preventDefault();
    
    // NOTE: Your API controller expects JSON, not FormData, for updates.
    // This script sends JSON.
    const formData = new FormData(this);
    const jsonData = {};
    
    for (let [key, value] of formData.entries()) {
        // We don't send the photo data in the JSON body.
        // A separate upload process is typically needed for files with JSON APIs.
        if (key !== 'photo') { 
            jsonData[key] = value;
        }
    }

    // You can add your validation logic here if needed (e.g., check years, contact number)

    $.ajax({
        // The URL points to the specific student record for an update
        url: '<?= base_url('api/students/' . $student['record_id']) ?>',
        method: 'PUT', // Using PUT for updates is RESTful
        contentType: 'application/json',
        data: JSON.stringify(jsonData),
        success: function(response) {
            if (response.status === 'success') {
                alert('Student updated successfully!');
                window.location.href = '<?= base_url("/dashboard/manageStudents"); ?>';
            } else {
                alert('Failed to update student: ' + (response.message || 'Unknown error'));
            }
        },
        error: function(xhr) {
            let errorMsg = 'An error occurred. Please try again.';
            try {
                const response = JSON.parse(xhr.responseText);
                errorMsg = 'Failed to update student: ' + (response.message || 'Server error');
            } catch (e) {
                // Keep default error message
            }
            alert(errorMsg);
        }
    });
});
</script>

<?= $this->endSection(); ?>