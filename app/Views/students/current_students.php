<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>

<!--Start sidebar-wrapper-->
<?= $this->include('partials/sidebar'); ?>
<!--End sidebar-wrapper-->

<!--Start topbar header-->
<?= $this->include('partials/topbar'); ?>
<!--End topbar header-->

<div class="clearfix"></div>

<div class="content-wrapper">
    <div class="container-fluid">
        
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title">
                            <h4><i class="zmdi zmdi-account-box"></i> Current Students</h4>
                        </div>
                        <hr>
                        
                        <!-- Search Results Alert -->
                        <?php if (!empty($searchTerm)): ?>
                            <div class="alert alert-info">
                                <i class="zmdi zmdi-search"></i> 
                                Showing search results for: <strong>"<?= esc($searchTerm) ?>"</strong>
                                <a href="<?= base_url('dashboard/currentStudents') ?>" class="btn btn-sm btn-outline-secondary ml-2">
                                    <i class="zmdi zmdi-close"></i> Clear Search
                                </a>
                            </div>
                        <?php endif; ?>

                        <!-- Action Buttons and Filters -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <a href="<?= base_url('/dashboard/createStudent'); ?>" class="btn btn-primary">
                                    <i class="zmdi zmdi-account-add"></i> Add Student
                                </a>
                                <a href="<?= base_url('/dashboard/manageStudents'); ?>" class="btn btn-secondary">
                                    <i class="zmdi zmdi-accounts-list"></i> All Students
                                </a>
                            </div>
                            <div class="col-md-6">
                                <form method="GET" action="<?= base_url('dashboard/currentStudents') ?>">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" 
                                               placeholder="Search current students..." value="<?= esc($searchTerm ?? '') ?>">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="submit">
                                                <i class="zmdi zmdi-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <!-- Filter Options -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="btn-group" role="group">
                                    <a href="<?= base_url('dashboard/currentStudents') ?>" 
                                       class="btn btn-sm <?= empty($filterProgram) ? 'btn-primary' : 'btn-outline-primary' ?>">
                                        All Programs
                                    </a>
                                    <a href="<?= base_url('dashboard/currentStudents?program=Undergraduate') ?>" 
                                       class="btn btn-sm <?= $filterProgram === 'Undergraduate' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                        Undergraduate
                                    </a>
                                    <a href="<?= base_url('dashboard/currentStudents?program=Graduate') ?>" 
                                       class="btn btn-sm <?= $filterProgram === 'Graduate' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                        Graduate
                                    </a>
                                    <a href="<?= base_url('dashboard/currentStudents?program=PhD') ?>" 
                                       class="btn btn-sm <?= $filterProgram === 'PhD' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                        PhD
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Students Table -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Photo</th>
                                        <th>Student ID</th>
                                        <th>Name</th>
                                        <th>Program</th>
                                        <th>Department</th>
                                        <th>Admission Year</th>
                                        <th>Expected Graduation</th>
                                        <th>Contact</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($students)): ?>
                                        <?php foreach ($students as $student): ?>
                                            <tr>
                                                <td>
                                                    <?php if (!empty($student['photo'])): ?>
                                                        <img src="<?= base_url('uploads/students/' . $student['photo']) ?>" 
                                                             alt="<?= esc($student['full_name']) ?>" 
                                                             class="rounded-circle" 
                                                             style="width: 40px; height: 40px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="zmdi zmdi-account text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= esc($student['student_id']) ?></td>
                                                <td>
                                                    <strong><?= esc($student['full_name']) ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge badge-info"><?= esc($student['program_type']) ?></span>
                                                </td>
                                                <td>
                                                    <?= esc($student['department_name']) ?>
                                                    <small class="text-muted d-block"><?= esc($student['faculty_name']) ?></small>
                                                </td>
                                                <td><?= esc($student['admission_year']) ?></td>
                                                <td>
                                                    <?= !empty($student['graduation_year']) ? esc($student['graduation_year']) : 'Not specified' ?>
                                                </td>
                                                <td>
                                                    <small>
                                                        <i class="zmdi zmdi-email"></i> <?= esc($student['email']) ?><br>
                                                        <i class="zmdi zmdi-phone"></i> <?= esc($student['contact_number']) ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?= base_url('dashboard/viewStudent/' . $student['record_id']) ?>" 
                                                           class="btn btn-sm btn-info" title="View Profile">
                                                            <i class="zmdi zmdi-eye"></i>
                                                        </a>
                                                        <a href="<?= base_url('dashboard/editStudent/' . $student['record_id']) ?>" 
                                                           class="btn btn-sm btn-warning" title="Edit">
                                                            <i class="zmdi zmdi-edit"></i>
                                                        </a>
                                                        <button type="button" class="btn btn-sm btn-danger" 
                                                                onclick="deleteStudent(<?= $student['record_id'] ?>)" title="Delete">
                                                            <i class="zmdi zmdi-delete"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center">
                                                <div class="py-4">
                                                    <i class="zmdi zmdi-account-box" style="font-size: 48px; color: #ccc;"></i>
                                                    <p class="text-muted mt-2">No current students found</p>
                                                    <?php if (!empty($searchTerm)): ?>
                                                        <a href="<?= base_url('dashboard/currentStudents') ?>" class="btn btn-sm btn-primary">
                                                            View All Current Students
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <nav aria-label="Current students pagination">
                                        <ul class="pagination justify-content-center">
                                            <!-- Previous Page -->
                                            <?php if ($pagination['current_page'] > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= base_url('dashboard/currentStudents') ?>?page=<?= $pagination['current_page'] - 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?><?= !empty($filterProgram) ? '&program=' . urlencode($filterProgram) : '' ?>">
                                                        Previous
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <!-- Page Numbers -->
                                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                                    <a class="page-link" href="<?= base_url('dashboard/currentStudents') ?>?page=<?= $i ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?><?= !empty($filterProgram) ? '&program=' . urlencode($filterProgram) : '' ?>">
                                                        <?= $i ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <!-- Next Page -->
                                            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= base_url('dashboard/currentStudents') ?>?page=<?= $pagination['current_page'] + 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?><?= !empty($filterProgram) ? '&program=' . urlencode($filterProgram) : '' ?>">
                                                        Next
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                    
                                    <div class="text-center text-muted">
                                        Showing <?= count($students) ?> of <?= $pagination['total'] ?> current students
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script>
function deleteStudent(studentId) {
    if (confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
        fetch(`<?= base_url('api/students/') ?>${studentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                Toastify({
                    text: data.message,
                    duration: 3000,
                    gravity: "top",
                    position: "right",
                    backgroundColor: "#4CAF50",
                }).showToast();
                
                // Reload the page after a short delay
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                throw new Error(data.message || 'Delete failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Toastify({
                text: error.message || 'Failed to delete student',
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#f44336",
            }).showToast();
        });
    }
}
</script>
<?= $this->endSection(); ?>