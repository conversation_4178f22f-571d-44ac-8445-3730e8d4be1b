<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-10 offset-lg-1 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="zmdi zmdi-account-add"></i> Add New Student
                        </h5>
                        <hr>
                        <form id="createStudentForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="full_name">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               placeholder="Enter full name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="student_id">Student ID <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="student_id" name="student_id" 
                                               placeholder="e.g., STU001" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_number">Contact Number</label>
                                        <input type="tel" class="form-control" id="contact_number" name="contact_number" 
                                               placeholder="10-digit number" maxlength="10">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="department_id">Department <span class="text-danger">*</span></label>
                                        <select class="form-control" id="department_id" name="department_id" required>
                                            <option value="">Select Department</option>
                                            <?php if (isset($departments)): ?>
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= esc($dept['id']); ?>">
                                                        <?= esc($dept['department_name']); ?> 
                                                        (<?= esc($dept['faculty_name'] ?? 'No Faculty'); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="program_type">Program Type <span class="text-danger">*</span></label>
                                        <select class="form-control" id="program_type" name="program_type" required>
                                            <option value="">Select Program</option>
                                            <option value="Bachelor's">Bachelor's</option>
                                            <option value="Master's">Master's</option>
                                            <option value="MPhil">MPhil</option>
                                            <option value="PhD">PhD</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-calendar"></i> Academic Timeline</h6>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="admission_year">Admission Year <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="admission_year" name="admission_year" 
                                               placeholder="e.g., 2024" min="2000" max="<?= date('Y') + 1; ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="expected_completion_year">Expected Completion Year</label>
                                        <input type="number" class="form-control" id="expected_completion_year" name="expected_completion_year" 
                                               placeholder="e.g., 2028" min="2000" max="2040">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="current_semester">Current Semester</label>
                                        <input type="number" class="form-control" id="current_semester" name="current_semester" 
                                               placeholder="e.g., 1" min="1" max="12">
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-chart"></i> Academic Performance</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cgpa">CGPA</label>
                                        <input type="number" class="form-control" id="cgpa" name="cgpa" 
                                               placeholder="e.g., 3.75" min="0" max="4" step="0.01">
                                        <small class="form-text text-muted">On a scale of 0.00 to 4.00</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="current" selected>Current Student</option>
                                            <option value="alumni">Alumni</option>
                                            <option value="dropped">Dropped</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row" id="completionYearRow" style="display: none;">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="actual_completion_year">Actual Completion Year</label>
                                        <input type="number" class="form-control" id="actual_completion_year" name="actual_completion_year" 
                                               placeholder="e.g., 2024" min="2000" max="<?= date('Y') + 5; ?>">
                                        <small class="form-text text-muted">Required if status is Alumni</small>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Additional Information</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="photo">Profile Photo</label>
                                        <input type="file" class="form-control-file" id="photo" name="photo" accept="image/*">
                                        <small class="form-text text-muted">Optional: Upload a profile photo</small>
                                        <div class="mt-2">
                                            <img id="photo_preview" src="#" alt="Photo Preview" 
                                                 style="display:none; max-width: 150px; max-height: 150px;" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="zmdi zmdi-check"></i> Add Student
                                </button>
                                <a href="<?= base_url('/dashboard/manageStudents'); ?>" class="btn btn-secondary btn-lg ml-2">
                                    <i class="zmdi zmdi-close"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
// Photo preview functionality
document.getElementById('photo').addEventListener('change', function(event) {
    const [file] = event.target.files;
    const preview = document.getElementById('photo_preview');
    if (file) {
        preview.src = URL.createObjectURL(file);
        preview.style.display = 'block';
    } else {
        preview.src = '#';
        preview.style.display = 'none';
    }
});

// Contact number validation
$('#contact_number').on('input', function() {
    this.value = this.value.replace(/[^0-9]/g, '');
});

// Status change handling
$('#status').on('change', function() {
    const status = $(this).val();
    if (status === 'alumni') {
        $('#completionYearRow').show();
        $('#actual_completion_year').prop('required', true);
    } else {
        $('#completionYearRow').hide();
        $('#actual_completion_year').prop('required', false);
        $('#actual_completion_year').val('');
    }
});

// Year validation
$('#admission_year, #expected_completion_year, #actual_completion_year').on('input', function() {
    const currentYear = new Date().getFullYear();
    const year = parseInt(this.value);
    
    if (this.id === 'admission_year' && year > currentYear + 1) {
        this.value = currentYear + 1;
    } else if (this.id === 'expected_completion_year' && year > currentYear + 20) {
        this.value = currentYear + 20;
    } else if (this.id === 'actual_completion_year' && year > currentYear + 5) {
        this.value = currentYear + 5;
    }
});

// Auto-calculate expected completion year based on program type
$('#program_type, #admission_year').on('change', function() {
    const programType = $('#program_type').val();
    const admissionYear = parseInt($('#admission_year').val());
    
    if (programType && admissionYear) {
        let duration = 4; // Default for Bachelor's
        
        switch (programType) {
            case "Bachelor's":
                duration = 4;
                break;
            case "Master's":
                duration = 2;
                break;
            case "MPhil":
                duration = 2;
                break;
            case "PhD":
                duration = 4;
                break;
        }
        
        $('#expected_completion_year').val(admissionYear + duration);
    }
});

// Form submission
$('#createStudentForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const jsonData = {};
    
    // Convert FormData to JSON (excluding file)
    for (let [key, value] of formData.entries()) {
        if (key !== 'photo') {
            jsonData[key] = value;
        }
    }
    
    // Validate contact number if provided
    if (jsonData.contact_number && jsonData.contact_number.length !== 10) {
        alert('Contact number must be exactly 10 digits');
        return;
    }
    
    // Validate years
    const admissionYear = parseInt(jsonData.admission_year);
    const expectedYear = parseInt(jsonData.expected_completion_year);
    const actualYear = parseInt(jsonData.actual_completion_year);
    
    if (expectedYear && expectedYear <= admissionYear) {
        alert('Expected completion year must be after admission year');
        return;
    }
    
    if (actualYear && actualYear < admissionYear) {
        alert('Actual completion year cannot be before admission year');
        return;
    }
    
    // Validate CGPA
    if (jsonData.cgpa && (parseFloat(jsonData.cgpa) < 0 || parseFloat(jsonData.cgpa) > 4)) {
        alert('CGPA must be between 0.00 and 4.00');
        return;
    }
    
    // Remove empty values
    Object.keys(jsonData).forEach(key => {
        if (jsonData[key] === '') {
            delete jsonData[key];
        }
    });
    
    $.ajax({
        url: '<?= base_url(); ?>/api/students',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(jsonData),
        success: function(response) {
            if (response.status === 'success') {
                alert('Student added successfully!');
                window.location.href = '<?= base_url("/dashboard/manageStudents"); ?>';
            } else {
                alert('Failed to add student: ' + response.message);
            }
        },
        error: function(xhr) {
            try {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to add student: ' + response.message);
            } catch (e) {
                alert('Failed to add student. Please try again.');
            }
        }
    });
});

// Set default admission year to current year
$('#admission_year').val(new Date().getFullYear());
</script>

<?= $this->endSection(); ?>