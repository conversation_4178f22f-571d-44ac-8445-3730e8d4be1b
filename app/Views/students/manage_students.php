<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Manage Students</h4>
                    <hr>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['by_status']['current'] ?? 0; ?></h4>
                                            <p class="mb-0">Current Students</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-account-box" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['by_status']['alumni'] ?? 0; ?></h4>
                                            <p class="mb-0">Alumni</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-graduation-cap" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['by_program']['PhD'] ?? 0; ?></h4>
                                            <p class="mb-0">PhD Students</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-library" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['total_students'] ?? 0; ?></h4>
                                            <p class="mb-0">Total Students</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-accounts" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons and Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="<?= base_url('/dashboard/createStudent'); ?>" class="btn btn-primary">
                                <i class="zmdi zmdi-account-add"></i> Add Student
                            </a>
                            <button class="btn btn-success ml-2" onclick="showBulkAlumniModal()">
                                <i class="zmdi zmdi-graduation-cap"></i> Bulk Mark as Alumni
                            </button>
                        </div>
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="Search students...">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" onclick="searchStudents()">
                                        <i class="zmdi zmdi-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="mb-3">
                        <button class="btn btn-sm btn-outline-primary filter-btn active" data-filter="all">All</button>
                        <button class="btn btn-sm btn-outline-success filter-btn" data-filter="current">Current Students</button>
                        <button class="btn btn-sm btn-outline-info filter-btn" data-filter="alumni">Alumni</button>
                        <button class="btn btn-sm btn-outline-warning filter-btn" data-filter="Bachelor's">Bachelor's</button>
                        <button class="btn btn-sm btn-outline-danger filter-btn" data-filter="Master's">Master's</button>
                        <button class="btn btn-sm btn-outline-secondary filter-btn" data-filter="PhD">PhD</button>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped" id="studentsTable">
                            <thead>
                                <tr>
                                    <th scope="col">
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th scope="col">#</th>
                                    <th scope="col">Name</th>
                                    <th scope="col">Student ID</th>
                                    <th scope="col">Department</th>
                                    <th scope="col">Program</th>
                                    <th scope="col">Admission Year</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">CGPA</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($students)): ?>
                                    <?php $i = 1; ?>
                                    <?php foreach ($students as $student): ?>
                                        <tr data-status="<?= esc($student['status']); ?>" data-program="<?= esc($student['program_type']); ?>">
                                            <td>
                                                <input type="checkbox" class="student-checkbox" value="<?= $student['record_id']; ?>">
                                            </td>
                                            <th scope="row"><?= $i++; ?></th>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($student['photo'])): ?>
                                                        <img src="<?= base_url('uploads/students/' . $student['photo']); ?>" 
                                                             alt="Photo" class="rounded-circle mr-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="rounded-circle mr-2 bg-secondary d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="zmdi zmdi-account text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <strong><?= esc($student['full_name']); ?></strong>
                                                        <br><small class="text-muted"><?= esc($student['email']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary"><?= esc($student['student_id']); ?></span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?= esc($student['department_name'] ?? 'N/A'); ?>
                                                    <br><span class="text-muted"><?= esc($student['faculty_name'] ?? ''); ?></span>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= $student['program_type'] === 'PhD' ? 'danger' : ($student['program_type'] === 'Master\'s' ? 'warning' : 'info'); ?>">
                                                    <?= esc($student['program_type']); ?>
                                                </span>
                                            </td>
                                            <td><?= esc($student['admission_year']); ?></td>
                                            <td>
                                                <span class="badge badge-<?= $student['status'] === 'current' ? 'primary' : ($student['status'] === 'alumni' ? 'success' : 'secondary'); ?>">
                                                    <?= ucfirst($student['status']); ?>
                                                    <?php if ($student['status'] === 'alumni' && !empty($student['actual_completion_year'])): ?>
                                                        <br><small>(<?= $student['actual_completion_year']; ?>)</small>
                                                    <?php endif; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if (!empty($student['cgpa'])): ?>
                                                    <span class="badge badge-<?= $student['cgpa'] >= 3.5 ? 'success' : ($student['cgpa'] >= 3.0 ? 'warning' : 'danger'); ?>">
                                                        <?= number_format($student['cgpa'], 2); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('/dashboard/editStudent/' . $student['record_id']); ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="zmdi zmdi-edit"></i>
                                                    </a>
                                                    <?php if ($student['status'] === 'current'): ?>
                                                        <button class="btn btn-sm btn-success" 
                                                                onclick="markAsAlumni(<?= $student['record_id']; ?>)" 
                                                                title="Mark as Alumni">
                                                            <i class="zmdi zmdi-graduation-cap"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-warning" 
                                                                onclick="markAsCurrent(<?= $student['record_id']; ?>)" 
                                                                title="Mark as Current">
                                                            <i class="zmdi zmdi-refresh"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-danger" 
                                                            onclick="deleteStudent(<?= $student['record_id']; ?>)" 
                                                            title="Delete">
                                                        <i class="zmdi zmdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="10" class="text-center">
                                            <div class="py-4">
                                                <i class="zmdi zmdi-account-box" style="font-size: 48px; color: #ccc;"></i>
                                                <p class="mt-2 text-muted">No students found. <a href="<?= base_url('/dashboard/createStudent'); ?>">Add your first student</a></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!-- Bulk Alumni Modal -->
<div class="modal fade" id="bulkAlumniModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Mark as Alumni</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="bulkAlumniForm">
                    <div class="form-group">
                        <label for="completion_year">Completion Year</label>
                        <input type="number" class="form-control" id="completion_year" name="completion_year" 
                               value="<?= date('Y'); ?>" min="2000" max="<?= date('Y') + 5; ?>" required>
                    </div>
                    <div class="alert alert-info">
                        <i class="zmdi zmdi-info-outline"></i>
                        <span id="selectedCount">0</span> students selected for alumni marking.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="processBulkAlumni()">Mark as Alumni</button>
            </div>
        </div>
    </div>
</div>

<!-- Single Alumni Modal -->
<div class="modal fade" id="singleAlumniModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Mark as Alumni</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="singleAlumniForm">
                    <input type="hidden" id="single_student_id">
                    <div class="form-group">
                        <label for="single_completion_year">Completion Year</label>
                        <input type="number" class="form-control" id="single_completion_year" name="completion_year" 
                               value="<?= date('Y'); ?>" min="2000" max="<?= date('Y') + 5; ?>" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="processSingleAlumni()">Mark as Alumni</button>
            </div>
        </div>
    </div>
</div>

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
// Select all functionality
$('#selectAll').on('change', function() {
    $('.student-checkbox').prop('checked', this.checked);
    updateSelectedCount();
});

$('.student-checkbox').on('change', function() {
    updateSelectedCount();
});

function updateSelectedCount() {
    const count = $('.student-checkbox:checked').length;
    $('#selectedCount').text(count);
}

// Filter functionality
$('.filter-btn').on('click', function() {
    $('.filter-btn').removeClass('active');
    $(this).addClass('active');
    
    const filter = $(this).data('filter');
    const rows = $('#studentsTable tbody tr');
    
    if (filter === 'all') {
        rows.show();
    } else {
        rows.hide();
        if (filter === 'current' || filter === 'alumni') {
            rows.filter(`[data-status="${filter}"]`).show();
        } else {
            rows.filter(`[data-program="${filter}"]`).show();
        }
    }
});

// Search functionality
function searchStudents() {
    const searchTerm = $('#searchInput').val();
    
    if (searchTerm.length > 0) {
        $.get(`<?= base_url(); ?>/api/students?search=${encodeURIComponent(searchTerm)}`, function(response) {
            if (response.status === 'success') {
                updateTable(response.data);
            }
        });
    } else {
        location.reload();
    }
}

$('#searchInput').on('keypress', function(e) {
    if (e.which === 13) {
        searchStudents();
    }
});

function updateTable(students) {
    const tbody = $('#studentsTable tbody');
    tbody.empty();
    
    if (students.length === 0) {
        tbody.append('<tr><td colspan="10" class="text-center">No students found.</td></tr>');
        return;
    }
    
    students.forEach((student, index) => {
        const row = `
            <tr data-status="${student.status}" data-program="${student.program_type}">
                <td><input type="checkbox" class="student-checkbox" value="${student.record_id}"></td>
                <th scope="row">${index + 1}</th>
                <td>
                    <div class="d-flex align-items-center">
                        ${student.photo ? 
                            `<img src="<?= base_url('uploads/students/'); ?>${student.photo}" alt="Photo" class="rounded-circle mr-2" style="width: 40px; height: 40px; object-fit: cover;">` :
                            `<div class="rounded-circle mr-2 bg-secondary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;"><i class="zmdi zmdi-account text-white"></i></div>`
                        }
                        <div>
                            <strong>${student.full_name}</strong>
                            <br><small class="text-muted">${student.email}</small>
                        </div>
                    </div>
                </td>
                <td><span class="badge badge-secondary">${student.student_id}</span></td>
                <td><small>${student.department_name || 'N/A'}<br><span class="text-muted">${student.faculty_name || ''}</span></small></td>
                <td><span class="badge badge-primary">${student.program_type}</span></td>
                <td>${student.admission_year}</td>
                <td><span class="badge badge-${student.status === 'current' ? 'primary' : 'success'}">${student.status}</span></td>
                <td>${student.cgpa ? `<span class="badge badge-success">${parseFloat(student.cgpa).toFixed(2)}</span>` : '<span class="text-muted">N/A</span>'}</td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?= base_url('/dashboard/editStudent/'); ?>${student.record_id}" class="btn btn-sm btn-primary" title="Edit"><i class="zmdi zmdi-edit"></i></a>
                        <button class="btn btn-sm btn-${student.status === 'current' ? 'success' : 'warning'}" onclick="${student.status === 'current' ? 'markAsAlumni' : 'markAsCurrent'}(${student.record_id})" title="${student.status === 'current' ? 'Mark as Alumni' : 'Mark as Current'}"><i class="zmdi zmdi-${student.status === 'current' ? 'graduation-cap' : 'refresh'}"></i></button>
                        <button class="btn btn-sm btn-danger" onclick="deleteStudent(${student.record_id})" title="Delete"><i class="zmdi zmdi-delete"></i></button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
    
    // Re-bind checkbox events
    $('.student-checkbox').on('change', function() {
        updateSelectedCount();
    });
}

function showBulkAlumniModal() {
    const selectedCount = $('.student-checkbox:checked').length;
    if (selectedCount === 0) {
        alert('Please select at least one student to mark as alumni.');
        return;
    }
    updateSelectedCount();
    $('#bulkAlumniModal').modal('show');
}

function processBulkAlumni() {
    const selectedIds = $('.student-checkbox:checked').map(function() {
        return parseInt(this.value);
    }).get();
    
    const completionYear = $('#completion_year').val();
    
    $.ajax({
        url: '<?= base_url(); ?>/api/students/bulk-mark-alumni',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            student_ids: selectedIds,
            completion_year: completionYear
        }),
        success: function(response) {
            if (response.status === 'success') {
                alert(`Successfully marked ${response.success_count} students as alumni.`);
                $('#bulkAlumniModal').modal('hide');
                location.reload();
            } else {
                alert('Failed to mark students as alumni: ' + response.message);
            }
        },
        error: function() {
            alert('Failed to mark students as alumni');
        }
    });
}

function markAsAlumni(studentId) {
    $('#single_student_id').val(studentId);
    $('#singleAlumniModal').modal('show');
}

function processSingleAlumni() {
    const studentId = $('#single_student_id').val();
    const completionYear = $('#single_completion_year').val();
    
    $.ajax({
        url: `<?= base_url(); ?>/api/students/${studentId}/mark-alumni`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ completion_year: completionYear }),
        success: function(response) {
            if (response.status === 'success') {
                $('#singleAlumniModal').modal('hide');
                location.reload();
            } else {
                alert('Failed to mark student as alumni: ' + response.message);
            }
        },
        error: function() {
            alert('Failed to mark student as alumni');
        }
    });
}

function markAsCurrent(studentId) {
    if (confirm('Are you sure you want to mark this student as current?')) {
        $.ajax({
            url: `<?= base_url(); ?>/api/students/${studentId}/mark-current`,
            method: 'POST',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Failed to mark student as current: ' + response.message);
                }
            },
            error: function() {
                alert('Failed to mark student as current');
            }
        });
    }
}

function deleteStudent(studentId) {
    if (confirm('Are you sure you want to delete this student? This action cannot be undone.')) {
        $.ajax({
            url: `<?= base_url(); ?>/api/students/${studentId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Failed to delete student: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to delete student: ' + response.message);
            }
        });
    }
}
</script>

<?= $this->endSection(); ?>