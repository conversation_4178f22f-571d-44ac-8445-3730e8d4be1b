<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>

<!--Start sidebar-wrapper-->
<?= $this->include('partials/sidebar'); ?>
<!--End sidebar-wrapper-->

<!--Start topbar header-->
<?= $this->include('partials/topbar'); ?>
<!--End topbar header-->

<div class="clearfix"></div>

<div class="content-wrapper">
    <div class="container-fluid">
        
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <!-- Profile Photo and Basic Info -->
                            <div class="col-md-4">
                                <div class="text-center">
                                    <?php if (!empty($facultyMember['photo'])): ?>
                                        <img src="<?= base_url($facultyMember['photo']) ?>" 
                                             alt="<?= esc($facultyMember['full_name']) ?>" 
                                             class="img-fluid rounded-circle mb-3" 
                                             style="width: 200px; height: 200px; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="bg-secondary rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" 
                                             style="width: 200px; height: 200px;">
                                            <i class="zmdi zmdi-account" style="font-size: 80px; color: white;"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <h3><?= esc($facultyMember['full_name']) ?></h3>
                                    <p class="text-muted"><?= esc($facultyMember['designation']) ?></p>
                                    <p class="text-muted"><?= esc($facultyMember['department_name']) ?></p>
                                    
                                    <?php if (!empty($assignments)): ?>
                                        <div class="mt-3">
                                            <h6>Current Positions:</h6>
                                            <?php foreach ($assignments as $assignment): ?>
                                                <span class="badge badge-primary mr-1">
                                                    <?= ucfirst($assignment['type']) ?> of <?= esc($assignment['entity_name']) ?>
                                                </span>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <!-- Detailed Information -->
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-12">
                                        <h4>Contact Information</h4>
                                        <hr>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Email:</strong> 
                                            <a href="mailto:<?= esc($facultyMember['email']) ?>">
                                                <?= esc($facultyMember['email']) ?>
                                            </a>
                                        </p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Contact Number:</strong> <?= esc($facultyMember['contact_number']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Employee ID:</strong> <?= esc($facultyMember['employee_id']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Faculty:</strong> <?= esc($facultyMember['faculty_name']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-12 mt-4">
                                        <h4>Academic Qualifications</h4>
                                        <hr>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Highest Qualification:</strong> <?= esc($facultyMember['highest_qualification']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Discipline:</strong> <?= esc($facultyMember['discipline']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>Year of Completion:</strong> <?= esc($facultyMember['year_completion']) ?></p>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <p><strong>University:</strong> <?= esc($facultyMember['university_name']) ?></p>
                                    </div>
                                    
                                    <?php if (!empty($facultyMember['bio'])): ?>
                                        <div class="col-md-12 mt-4">
                                            <h4>Biography</h4>
                                            <hr>
                                            <p><?= nl2br(esc($facultyMember['bio'])) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <a href="<?= base_url('dashboard/manageFacultyMembers') ?>" class="btn btn-secondary">
                                    <i class="zmdi zmdi-arrow-left"></i> Back to Faculty Members
                                </a>
                                <a href="<?= base_url('dashboard/editFacultyMember/' . $facultyMember['record_id']) ?>" class="btn btn-primary">
                                    <i class="zmdi zmdi-edit"></i> Edit Profile
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script>
// Any additional JavaScript for the profile page can go here
</script>
<?= $this->endSection(); ?>