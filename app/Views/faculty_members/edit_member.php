<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>

<!--Start sidebar-wrapper-->
<?= $this->include('partials/sidebar'); ?>
<!--End sidebar-wrapper-->

<!--Start topbar header-->
<?= $this->include('partials/topbar'); ?>
<!--End topbar header-->

<div class="clearfix"></div>

<div class="content-wrapper">
    <div class="container-fluid">
        
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="card-title">
                            <h4>Edit Faculty Member: <?= esc($facultyMember['full_name']) ?></h4>
                        </div>
                        <hr>
                        
                        <form id="editFacultyMemberForm" method="post" enctype="multipart/form-data">
                            <div class="row">
                                <!-- Personal Information -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="full_name">Full Name *</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               value="<?= esc($facultyMember['full_name']) ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?= esc($facultyMember['email']) ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_number">Contact Number *</label>
                                        <input type="text" class="form-control" id="contact_number" name="contact_number" 
                                               value="<?= esc($facultyMember['contact_number']) ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="employee_id">Employee ID *</label>
                                        <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                               value="<?= esc($facultyMember['employee_id']) ?>" required>
                                    </div>
                                </div>
                                
                                <!-- Academic Information -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="designation">Designation *</label>
                                        <select class="form-control" id="designation" name="designation" required>
                                            <option value="">Select Designation</option>
                                            <option value="Professor" <?= $facultyMember['designation'] === 'Professor' ? 'selected' : '' ?>>Professor</option>
                                            <option value="Associate Professor" <?= $facultyMember['designation'] === 'Associate Professor' ? 'selected' : '' ?>>Associate Professor</option>
                                            <option value="Assistant Professor" <?= $facultyMember['designation'] === 'Assistant Professor' ? 'selected' : '' ?>>Assistant Professor</option>
                                            <option value="Lecturer" <?= $facultyMember['designation'] === 'Lecturer' ? 'selected' : '' ?>>Lecturer</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="department_id">Department *</label>
                                        <select class="form-control" id="department_id" name="department_id" required>
                                            <option value="">Select Department</option>
                                            <?php foreach ($departments as $dept): ?>
                                                <option value="<?= $dept['id'] ?>" <?= $facultyMember['department_id'] == $dept['id'] ? 'selected' : '' ?>>
                                                    <?= esc($dept['department_name']) ?> (<?= esc($dept['faculty_name']) ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="highest_qualification">Highest Qualification *</label>
                                        <select class="form-control" id="highest_qualification" name="highest_qualification" required>
                                            <option value="">Select Qualification</option>
                                            <option value="PhD" <?= $facultyMember['highest_qualification'] === 'PhD' ? 'selected' : '' ?>>PhD</option>
                                            <option value="MPhil" <?= $facultyMember['highest_qualification'] === 'MPhil' ? 'selected' : '' ?>>MPhil</option>
                                            <option value="Master's" <?= $facultyMember['highest_qualification'] === "Master's" ? 'selected' : '' ?>>Master's</option>
                                            <option value="Bachelor's" <?= $facultyMember['highest_qualification'] === "Bachelor's" ? 'selected' : '' ?>>Bachelor's</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="discipline">Discipline *</label>
                                        <input type="text" class="form-control" id="discipline" name="discipline" 
                                               value="<?= esc($facultyMember['discipline']) ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="year_completion">Year of Completion *</label>
                                        <input type="number" class="form-control" id="year_completion" name="year_completion" 
                                               value="<?= esc($facultyMember['year_completion']) ?>" min="1950" max="<?= date('Y') ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="university_name">University Name *</label>
                                        <input type="text" class="form-control" id="university_name" name="university_name" 
                                               value="<?= esc($facultyMember['university_name']) ?>" required>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="active" <?= $facultyMember['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= $facultyMember['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <!-- Photo Upload -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="photo">Photo</label>
                                        <input type="file" class="form-control-file" id="photo" name="photo" accept="image/*">
                                        <small class="form-text text-muted">Optional: Upload a new profile photo</small>
                                        <?php if (!empty($facultyMember['photo'])): ?>
                                            <div class="mt-2">
                                                <small class="text-muted">Current photo:</small><br>
                                                <img src="<?= base_url($facultyMember['photo']) ?>" alt="Current Photo" 
                                                     style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;" />
                                            </div>
                                        <?php endif; ?>
                                        <div class="mt-2">
                                            <img id="photo_preview" src="#" alt="Photo Preview" 
                                                 style="display:none; max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;" />
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Bio -->
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="bio">Biography</label>
                                        <textarea class="form-control" id="bio" name="bio" rows="4"><?= esc($facultyMember['bio']) ?></textarea>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="zmdi zmdi-check"></i> Update Faculty Member
                                </button>
                                <a href="<?= base_url('dashboard/manageFacultyMembers') ?>" class="btn btn-secondary">
                                    <i class="zmdi zmdi-arrow-left"></i> Back to List
                                </a>
                            </div>
                        </form>
                        
                        <!-- Assignments Information -->
                        <?php if (!empty($assignments)): ?>
                            <hr>
                            <h5>Current Assignments</h5>
                            <div class="alert alert-info">
                                <strong>This faculty member is currently assigned as:</strong>
                                <ul class="mb-0">
                                    <?php foreach ($assignments as $assignment): ?>
                                        <li><?= ucfirst($assignment['type']) ?> of <?= esc($assignment['entity_name']) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script>
// Photo preview functionality
document.getElementById('photo').addEventListener('change', function(event) {
    const [file] = event.target.files;
    const preview = document.getElementById('photo_preview');
    if (file) {
        preview.src = URL.createObjectURL(file);
        preview.style.display = 'block';
    } else {
        preview.src = '#';
        preview.style.display = 'none';
    }
});

document.getElementById('editFacultyMemberForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Updating...';
    submitBtn.disabled = true;
    
    // Handle photo upload first if a file is selected
    const photoFile = formData.get('photo');
    if (photoFile && photoFile.size > 0) {
        // Upload photo using MediaController
        const photoFormData = new FormData();
        photoFormData.append('file', photoFile);
        
        fetch('<?= base_url(); ?>/api/v1/media/upload', {
            method: 'POST',
            body: photoFormData
        })
        .then(response => response.json())
        .then(photoResponse => {
            if (photoResponse.status === 'success') {
                // Extract the relative path from the URL
                const photoUrl = photoResponse.data.url;
                const baseUrl = '<?= base_url(); ?>';
                const photoPath = photoUrl.replace(baseUrl, '').replace(/^\//, '');
                
                // Update faculty member with photo path
                updateFacultyMember(formData, photoPath, submitBtn, originalText);
            } else {
                throw new Error('Failed to upload photo: ' + photoResponse.message);
            }
        })
        .catch(error => {
            console.error('Photo upload error:', error);
            Toastify({
                text: error.message || 'Failed to upload photo',
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#f44336",
            }).showToast();
            
            // Restore button state
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    } else {
        // No photo selected, update without photo
        updateFacultyMember(formData, null, submitBtn, originalText);
    }
});

function updateFacultyMember(formData, photoUrl, submitBtn, originalText) {
    // Convert FormData to JSON for faculty member update
    const jsonData = {};
    for (let [key, value] of formData.entries()) {
        if (key !== 'photo') { // Exclude the file input
            jsonData[key] = value;
        }
    }
    
    // Add photo URL if uploaded
    if (photoUrl) {
        // Organize the photo using our helper
        fetch('<?= base_url(); ?>/api/faculty-members/organize-photo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ mediaUrl: photoUrl })
        })
        .then(response => response.json())
        .then(organizeResponse => {
            if (organizeResponse.status === 'success') {
                jsonData.photo = organizeResponse.data.path;
            } else {
                // If organization fails, use the original path
                const baseUrl = '<?= base_url(); ?>';
                jsonData.photo = photoUrl.replace(baseUrl, '').replace(/^\//, '');
            }
            // Proceed with faculty member update
            performFacultyMemberUpdate(jsonData, submitBtn, originalText);
        })
        .catch(error => {
            console.error('Photo organization error:', error);
            // If organization fails, use the original path
            const baseUrl = '<?= base_url(); ?>';
            jsonData.photo = photoUrl.replace(baseUrl, '').replace(/^\//, '');
            performFacultyMemberUpdate(jsonData, submitBtn, originalText);
        });
    } else {
        performFacultyMemberUpdate(jsonData, submitBtn, originalText);
    }
}

function performFacultyMemberUpdate(jsonData, submitBtn, originalText) {
    fetch(`<?= base_url('api/faculty-members/' . $facultyMember['record_id']) ?>`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(jsonData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            Toastify({
                text: data.message,
                duration: 3000,
                gravity: "top",
                position: "right",
                backgroundColor: "#4CAF50",
            }).showToast();
            
            // Optionally redirect after success
            setTimeout(() => {
                window.location.href = '<?= base_url('dashboard/manageFacultyMembers') ?>';
            }, 2000);
        } else {
            throw new Error(data.message || 'Update failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Toastify({
            text: error.message || 'Failed to update faculty member',
            duration: 3000,
            gravity: "top",
            position: "right",
            backgroundColor: "#f44336",
        }).showToast();
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});
</script>
<?= $this->endSection(); ?>