<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-10 offset-lg-1 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="zmdi zmdi-account-add"></i> Add New Faculty Member
                        </h5>
                        <hr>
                        <form id="createFacultyMemberForm" enctype="multipart/form-data">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="full_name">Full Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" 
                                               placeholder="Enter full name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="employee_id">Employee ID <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="employee_id" name="employee_id" 
                                               placeholder="e.g., EMP001" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="designation">Designation <span class="text-danger">*</span></label>
                                        <select class="form-control" id="designation" name="designation" required>
                                            <option value="">Select Designation</option>
                                            <option value="Professor">Professor</option>
                                            <option value="Associate Professor">Associate Professor</option>
                                            <option value="Assistant Professor">Assistant Professor</option>
                                            <option value="Lecturer">Lecturer</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="department_id">Department <span class="text-danger">*</span></label>
                                        <select class="form-control" id="department_id" name="department_id" required>
                                            <option value="">Select Department</option>
                                            <?php if (isset($departments)): ?>
                                                <?php foreach ($departments as $dept): ?>
                                                    <option value="<?= esc($dept['id']); ?>">
                                                        <?= esc($dept['department_name']); ?> 
                                                        (<?= esc($dept['faculty_name'] ?? 'No Faculty'); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="email">Email Address <span class="text-danger">*</span></label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="contact_number">Contact Number <span class="text-danger">*</span></label>
                                        <input type="tel" class="form-control" id="contact_number" name="contact_number" 
                                               placeholder="10-digit number" maxlength="10" required>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-graduation-cap"></i> Academic Information</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="highest_qualification">Highest Qualification <span class="text-danger">*</span></label>
                                        <select class="form-control" id="highest_qualification" name="highest_qualification" required>
                                            <option value="">Select Qualification</option>
                                            <option value="PhD">PhD</option>
                                            <option value="MPhil">MPhil</option>
                                            <option value="Master's">Master's</option>
                                            <option value="Bachelor's">Bachelor's</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="discipline">Discipline/Subject <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="discipline" name="discipline" 
                                               placeholder="e.g., Computer Science" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="year_completion">Year of Completion <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control" id="year_completion" name="year_completion" 
                                               placeholder="e.g., 2020" min="1950" max="2030" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="university_name">University Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="university_name" name="university_name" 
                                               placeholder="Name of the university" required>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Additional Information</h6>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="photo">Profile Photo</label>
                                        <input type="file" class="form-control-file" id="photo" name="photo" accept="image/*">
                                        <small class="form-text text-muted">Optional: Upload a profile photo</small>
                                        <div class="mt-2">
                                            <img id="photo_preview" src="#" alt="Photo Preview" 
                                                 style="display:none; max-width: 150px; max-height: 150px;" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select class="form-control" id="status" name="status">
                                            <option value="active" selected>Active</option>
                                            <option value="inactive">Inactive</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="bio">Biography/Profile</label>
                                <textarea class="form-control" id="bio" name="bio" rows="4" 
                                          placeholder="Brief biography or profile information..."></textarea>
                            </div>

                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="zmdi zmdi-check"></i> Add Faculty Member
                                </button>
                                <a href="<?= base_url('/dashboard/manageFacultyMembers'); ?>" class="btn btn-secondary btn-lg ml-2">
                                    <i class="zmdi zmdi-close"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
// Photo preview functionality
document.getElementById('photo').addEventListener('change', function(event) {
    const [file] = event.target.files;
    const preview = document.getElementById('photo_preview');
    if (file) {
        preview.src = URL.createObjectURL(file);
        preview.style.display = 'block';
    } else {
        preview.src = '#';
        preview.style.display = 'none';
    }
});

// Contact number validation
$('#contact_number').on('input', function() {
    this.value = this.value.replace(/[^0-9]/g, '');
});

// Year validation
$('#year_completion').on('input', function() {
    const currentYear = new Date().getFullYear();
    const year = parseInt(this.value);
    
    if (year > currentYear + 5) {
        this.value = currentYear + 5;
    }
});

// Form submission
$('#createFacultyMemberForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    // Validate contact number
    const contactNumber = formData.get('contact_number');
    if (contactNumber.length !== 10) {
        alert('Contact number must be exactly 10 digits');
        return;
    }
    
    // Validate year
    const currentYear = new Date().getFullYear();
    const yearCompletion = parseInt(formData.get('year_completion'));
    if (yearCompletion > currentYear + 5) {
        alert('Year of completion cannot be more than 5 years in the future');
        return;
    }
    
    // Handle photo upload first if a file is selected
    const photoFile = formData.get('photo');
    if (photoFile && photoFile.size > 0) {
        // Upload photo using MediaController
        const photoFormData = new FormData();
        photoFormData.append('file', photoFile);
        
        $.ajax({
            url: '<?= base_url(); ?>/api/v1/media/upload',
            method: 'POST',
            data: photoFormData,
            processData: false,
            contentType: false,
            success: function(photoResponse) {
                if (photoResponse.status === 'success') {
                    // Extract the relative path from the URL
                    const photoUrl = photoResponse.data.url;
                    const baseUrl = '<?= base_url(); ?>';
                    const photoPath = photoUrl.replace(baseUrl, '').replace(/^\//, '');
                    
                    // Add photo path to form data and submit
                    submitFacultyMember(formData, photoPath);
                } else {
                    alert('Failed to upload photo: ' + photoResponse.message);
                }
            },
            error: function() {
                alert('Failed to upload photo. Please try again.');
            }
        });
    } else {
        // No photo selected, submit without photo
        submitFacultyMember(formData, null);
    }
});

function submitFacultyMember(formData, photoUrl) {
    // Convert FormData to JSON for faculty member creation
    const jsonData = {};
    for (let [key, value] of formData.entries()) {
        if (key !== 'photo') { // Exclude the file input
            jsonData[key] = value;
        }
    }
    
    // Add photo URL if uploaded (MediaController returns full URL)
    if (photoUrl) {
        // Organize the photo using our helper
        $.ajax({
            url: '<?= base_url(); ?>/api/faculty-members/organize-photo',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ mediaUrl: photoUrl }),
            success: function(organizeResponse) {
                if (organizeResponse.status === 'success') {
                    jsonData.photo = organizeResponse.data.path;
                }
                // Proceed with faculty member creation
                createFacultyMemberRecord(jsonData);
            },
            error: function() {
                // If organization fails, use the original path
                const baseUrl = '<?= base_url(); ?>';
                jsonData.photo = photoUrl.replace(baseUrl, '').replace(/^\//, '');
                createFacultyMemberRecord(jsonData);
            }
        });
    } else {
        createFacultyMemberRecord(jsonData);
    }
}

function createFacultyMemberRecord(jsonData) {
    $.ajax({
        url: '<?= base_url(); ?>/api/faculty-members',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(jsonData),
        success: function(response) {
            if (response.status === 'success') {
                alert('Faculty member added successfully!');
                window.location.href = '<?= base_url("/dashboard/manageFacultyMembers"); ?>';
            } else {
                alert('Failed to add faculty member: ' + response.message);
            }
        },
        error: function(xhr) {
            try {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to add faculty member: ' + response.message);
            } catch (e) {
                alert('Failed to add faculty member. Please try again.');
            }
        }
    });
}
</script>

<?= $this->endSection(); ?>