<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Manage Faculty Members</h4>
                    <hr>
                    
                    <!-- Statistics Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['total_active'] ?? 0; ?></h4>
                                            <p class="mb-0">Total Active</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-accounts" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['by_qualification']['PhD'] ?? 0; ?></h4>
                                            <p class="mb-0">PhD Holders</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-graduation-cap" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= $statistics['by_designation']['Professor'] ?? 0; ?></h4>
                                            <p class="mb-0">Professors</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-account-box" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><?= count($statistics['by_faculty'] ?? []); ?></h4>
                                            <p class="mb-0">Faculties</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="zmdi zmdi-city" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search Results Alert -->
                    <?php if (!empty($searchTerm)): ?>
                        <div class="alert alert-info">
                            <i class="zmdi zmdi-search"></i> 
                            Showing search results for: <strong>"<?= esc($searchTerm) ?>"</strong>
                            <a href="<?= base_url('dashboard/manageFacultyMembers') ?>" class="btn btn-sm btn-outline-secondary ml-2">
                                <i class="zmdi zmdi-close"></i> Clear Search
                            </a>
                        </div>
                    <?php endif; ?>

                    <!-- Action Buttons and Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <a href="<?= base_url('/dashboard/createFacultyMember'); ?>" class="btn btn-primary">
                                <i class="zmdi zmdi-account-add"></i> Add Faculty Member
                            </a>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" action="<?= base_url('dashboard/manageFacultyMembers') ?>">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" 
                                           placeholder="Search faculty members..." value="<?= esc($searchTerm ?? '') ?>">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="zmdi zmdi-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Filter Buttons -->
                    <div class="mb-3">
                        <button class="btn btn-sm btn-outline-primary filter-btn active" data-filter="all">All</button>
                        <button class="btn btn-sm btn-outline-success filter-btn" data-filter="Professor">Professors</button>
                        <button class="btn btn-sm btn-outline-info filter-btn" data-filter="Associate Professor">Associate Professors</button>
                        <button class="btn btn-sm btn-outline-warning filter-btn" data-filter="Assistant Professor">Assistant Professors</button>
                        <button class="btn btn-sm btn-outline-secondary filter-btn" data-filter="Lecturer">Lecturers</button>
                        <button class="btn btn-sm btn-outline-danger filter-btn" data-filter="PhD">PhD Holders</button>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped" id="facultyMembersTable">
                            <thead>
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Name</th>
                                    <th scope="col">Designation</th>
                                    <th scope="col">Department</th>
                                    <th scope="col">Faculty</th>
                                    <th scope="col">Qualification</th>
                                    <th scope="col">Contact</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($facultyMembers)): ?>
                                    <?php $i = 1; ?>
                                    <?php foreach ($facultyMembers as $member): ?>
                                        <tr data-designation="<?= esc($member['designation']); ?>" data-qualification="<?= esc($member['highest_qualification']); ?>">
                                            <th scope="row"><?= $i++; ?></th>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($member['photo'])): ?>
                                                        <img src="<?= base_url($member['photo']); ?>" 
                                                             alt="Photo" class="rounded-circle mr-2" style="width: 40px; height: 40px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="rounded-circle mr-2 bg-secondary d-flex align-items-center justify-content-center" 
                                                             style="width: 40px; height: 40px;">
                                                            <i class="zmdi zmdi-account text-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div style="width:74%; overflow:hidden; text-overflow: ellipsis">
                                                        <strong style="white-space:nowrap;"><?= esc($member['full_name']); ?></strong>
                                                        <br><small class="text-muted"><?= esc($member['employee_id']); ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= $member['designation'] === 'Professor' ? 'success' : ($member['designation'] === 'Associate Professor' ? 'info' : ($member['designation'] === 'Assistant Professor' ? 'warning' : 'secondary')); ?>">
                                                    <?= esc($member['designation']); ?>
                                                </span>
                                            </td>
                                            <td><?= esc($member['department_name'] ?? 'N/A'); ?></td>
                                            <td><?= esc($member['faculty_name'] ?? 'N/A'); ?></td>
                                            <td>
                                                <span class="badge badge-<?= $member['highest_qualification'] === 'PhD' ? 'danger' : 'primary'; ?>">
                                                    <?= esc($member['highest_qualification']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <i class="zmdi zmdi-phone"></i> <?= esc($member['contact_number']); ?><br>
                                                    <i class="zmdi zmdi-email"></i> <?= esc($member['email']); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= $member['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                                    <?= ucfirst($member['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('/dashboard/editFacultyMember/' . $member['record_id']); ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit">
                                                        <i class="zmdi zmdi-edit"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-info" 
                                                            onclick="viewAssignments(<?= $member['record_id']; ?>)" 
                                                            title="View Assignments">
                                                        <i class="zmdi zmdi-assignment"></i>
                                                    </button>
                                                    <?php if ($member['status'] === 'active'): ?>
                                                        <button class="btn btn-sm btn-warning" 
                                                                onclick="deactivateMember(<?= $member['record_id']; ?>)" 
                                                                title="Deactivate">
                                                            <i class="zmdi zmdi-pause"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-success" 
                                                                onclick="activateMember(<?= $member['record_id']; ?>)" 
                                                                title="Activate">
                                                            <i class="zmdi zmdi-play"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <button class="btn btn-sm btn-danger" 
                                                            onclick="deleteMember(<?= $member['record_id']; ?>)" 
                                                            title="Delete">
                                                        <i class="zmdi zmdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="9" class="text-center">
                                            <div class="py-4">
                                                <i class="zmdi zmdi-accounts" style="font-size: 48px; color: #ccc;"></i>
                                                <p class="mt-2 text-muted">No faculty members found. <a href="<?= base_url('/dashboard/createFacultyMember'); ?>">Add your first faculty member</a></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <nav aria-label="Faculty members pagination">
                                    <ul class="pagination justify-content-center">
                                        <!-- Previous Page -->
                                        <?php if ($pagination['current_page'] > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?= base_url('dashboard/manageFacultyMembers') ?>?page=<?= $pagination['current_page'] - 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                    Previous
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                        
                                        <!-- Page Numbers -->
                                        <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                            <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                                <a class="page-link" href="<?= base_url('dashboard/manageFacultyMembers') ?>?page=<?= $i ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>
                                        
                                        <!-- Next Page -->
                                        <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="<?= base_url('dashboard/manageFacultyMembers') ?>?page=<?= $pagination['current_page'] + 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                    Next
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                                
                                <div class="text-center text-muted">
                                    Showing <?= count($facultyMembers) ?> of <?= $pagination['total'] ?> faculty members
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!-- Assignments Modal -->
<div class="modal fade" id="assignmentsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="background: #2f2f2f; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); height: 100%;">
            <div class="modal-header" style="padding: 1rem; border-bottom: 1px solid #454545; display: flex; justify-content: space-between; align-items: center;">
                <h5 class="modal-title">Faculty Member Assignments</h5>
                <button type="button" style="color: #fff;" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="assignmentsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer" style="padding: 1rem; border-top: 1px solid #454545; display: flex; justify-content: space-between; align-items: center;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
// Filter functionality
$('.filter-btn').on('click', function() {
    $('.filter-btn').removeClass('active');
    $(this).addClass('active');
    
    const filter = $(this).data('filter');
    const rows = $('#facultyMembersTable tbody tr');
    
    if (filter === 'all') {
        rows.show();
    } else if (filter === 'PhD') {
        rows.hide();
        rows.filter('[data-qualification="PhD"]').show();
    } else {
        rows.hide();
        rows.filter(`[data-designation="${filter}"]`).show();
    }
});

// Search functionality
function searchMembers() {
    const searchTerm = $('#searchInput').val();
    
    if (searchTerm.length > 0) {
        $.get(`<?= base_url(); ?>/api/faculty-members?search=${encodeURIComponent(searchTerm)}`, function(response) {
            if (response.status === 'success') {
                updateTable(response.data);
            }
        });
    } else {
        location.reload();
    }
}

$('#searchInput').on('keypress', function(e) {
    if (e.which === 13) {
        searchMembers();
    }
});

function updateTable(members) {
    const tbody = $('#facultyMembersTable tbody');
    tbody.empty();
    
    if (members.length === 0) {
        tbody.append('<tr><td colspan="9" class="text-center">No faculty members found.</td></tr>');
        return;
    }
    
    members.forEach((member, index) => {
        const row = `
            <tr data-designation="${member.designation}" data-qualification="${member.highest_qualification}">
                <th scope="row">${index + 1}</th>
                <td>
                    <div class="d-flex align-items-center">
                        ${member.photo ? 
                            `<img src="<?= base_url(); ?>${member.photo}" alt="Photo" class="rounded-circle mr-2" style="width: 40px; height: 40px; object-fit: cover;">` :
                            `<div class="rounded-circle mr-2 bg-secondary d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;"><i class="zmdi zmdi-account text-white"></i></div>`
                        }
                        <div>
                            <strong>${member.full_name}</strong>
                            <br><small class="text-muted">${member.employee_id}</small>
                        </div>
                    </div>
                </td>
                <td><span class="badge badge-primary">${member.designation}</span></td>
                <td>${member.department_name || 'N/A'}</td>
                <td>${member.faculty_name || 'N/A'}</td>
                <td><span class="badge badge-${member.highest_qualification === 'PhD' ? 'danger' : 'primary'}">${member.highest_qualification}</span></td>
                <td><small><i class="zmdi zmdi-phone"></i> ${member.contact_number}<br><i class="zmdi zmdi-email"></i> ${member.email}</small></td>
                <td><span class="badge badge-${member.status === 'active' ? 'success' : 'secondary'}">${member.status}</span></td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="<?= base_url('/dashboard/editFacultyMember/'); ?>${member.record_id}" class="btn btn-sm btn-primary" title="Edit"><i class="zmdi zmdi-edit"></i></a>
                        <button class="btn btn-sm btn-info" onclick="viewAssignments(${member.record_id})" title="View Assignments"><i class="zmdi zmdi-assignment"></i></button>
                        <button class="btn btn-sm btn-${member.status === 'active' ? 'warning' : 'success'}" onclick="${member.status === 'active' ? 'deactivateMember' : 'activateMember'}(${member.record_id})" title="${member.status === 'active' ? 'Deactivate' : 'Activate'}"><i class="zmdi zmdi-${member.status === 'active' ? 'pause' : 'play'}"></i></button>
                        <button class="btn btn-sm btn-danger" onclick="deleteMember(${member.record_id})" title="Delete"><i class="zmdi zmdi-delete"></i></button>
                    </div>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

function viewAssignments(memberId) {
    $.get(`<?= base_url(); ?>/api/faculty-members/${memberId}/assignments`, function(response) {
        if (response.status === 'success') {
            let content = '<div class="list-group">';
            
            if (response.data.length === 0) {
                content += '<div class="alert alert-info">No assignments found for this faculty member.</div>';
            } else {
                response.data.forEach(assignment => {
                    const icon = assignment.type === 'dean' ? 'graduation-cap' : 'city';
                    const color = assignment.type === 'dean' ? 'success' : 'info';
                    content += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1" style="display: flex; flex-direction: row; gap:8px;"><i class="zmdi zmdi-${icon}"></i> ${assignment.type === 'dean' ? 'Dean of' : 'Head of'} ${assignment.entity_name}</h6>
                                <span style="
    align-content: center;" class="badge badge-${color}">${assignment.type.toUpperCase()}</span>
                            </div>
                        </div>
                    `;
                });
            }
            
            content += '</div>';
            $('#assignmentsContent').html(content);
            $('#assignmentsModal').modal('show');
        }
    });
}

function deactivateMember(memberId) {
    if (confirm('Are you sure you want to deactivate this faculty member?')) {
        $.ajax({
            url: `<?= base_url(); ?>/api/faculty-members/${memberId}/deactivate`,
            method: 'POST',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Failed to deactivate member: ' + response.message);
                }
            },
            error: function() {
                alert('Failed to deactivate member');
            }
        });
    }
}

function activateMember(memberId) {
    $.ajax({
        url: `<?= base_url(); ?>/api/faculty-members/${memberId}/activate`,
        method: 'POST',
        success: function(response) {
            if (response.status === 'success') {
                location.reload();
            } else {
                alert('Failed to activate member: ' + response.message);
            }
        },
        error: function() {
            alert('Failed to activate member');
        }
    });
}

function deleteMember(memberId) {
    if (confirm('Are you sure you want to delete this faculty member? This action cannot be undone.')) {
        $.ajax({
            url: `<?= base_url(); ?>/api/faculty-members/${memberId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Failed to delete member: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to delete member: ' + response.message);
            }
        });
    }
}
</script>

<?= $this->endSection(); ?>