<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-6 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <form method="post" id="headerHeadingForm">
                            <!-- 🔹 Render Basic Fields -->
                            <h5 class="mb-3">Header Heading</h5>
                            <?php foreach ($formData['fields'] as $field): ?>
                                <div class="form-group mb-3">
                                    <label for="<?= esc($field['name']) ?>"><?= esc($field['label']) ?></label>
                                    <input type="<?= esc($field['type']) ?>"
                                        name="<?= esc($field['name']) ?>"
                                        id="<?= esc($field['name']) ?>"
                                        class="form-control"
                                        value="<?= esc($field['value']) ?>">
                                </div>
                            <?php endforeach; ?>

                            <button type="submit" class="btn btn-primary">Update</button>
                        </form>

                    </div>
                </div>
            </div>



            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<!-- custom scripts for saving the form -->
<script>
    document.getElementById('headerHeadingForm').addEventListener('submit', function(e) {
        e.preventDefault(); // prevent full page reload

        const form = e.target;
        const inputs = form.querySelectorAll('input');

        const fields = [];

        inputs.forEach(input => {
            const label = form.querySelector(`label[for="${input.id}"]`);
            fields.push({
                label: label ? label.textContent.trim() : input.name,
                name: input.name,
                value: input.value,
                type: input.type
            });
        });

        const finalJson = JSON.stringify({
            fields: fields
        }, null, 2); // just to visualize

        // ✅ Send to backend
        fetch("<?= base_url(); ?>dashboard/updateHeroHeading", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: finalJson
            })
            .then(res => res.json())
            .then(data => {
                Toastify({
                    text: 'Data updated successfully!',
                    duration: 2000,
                    close: true,
                    gravity: "top", // top or bottom
                    position: "right", // left, center or right
                    backgroundColor: "linear-gradient(to right, #00b09b, #96c93d)",
                }).showToast();
                location.reload(); // reload to see changes
            })
            .catch(err => {
                console.error("Error saving form data:", err);
            });
    });
</script>

<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>