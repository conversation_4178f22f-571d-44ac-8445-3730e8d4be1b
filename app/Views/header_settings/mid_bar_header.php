<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-6 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <form method="post" id="midbarheaderForm" enctype="multipart/form-data">
                            <!-- 🔹 Render Basic Fields -->
                            <h5 class="mb-3">Header Mid Bar</h5>
                            <?php foreach ($formData['fields'] as $field): ?>

                                <?php if ($field['type'] == 'file'): ?>
                                    <div class="form-group mb-3">
                                        <label for="<?= esc($field['name']) ?>"><?= esc($field['label']) ?></label>
                                        <input type="file"
                                            name="<?= esc($field['name']) ?>"
                                            id="<?= esc($field['name']) ?>"
                                            src="<?= esc($field['src']) ?>"
                                            onchange="previewLogomainImage(this)"
                                            class="form-control mb-2">
                                        <img id="logoPreview" src="<?= base_url().esc($field['src']) ?>" alt="Logo Preview" style="max-width: 200px; max-height: 200px;" />
                                    </div>
                                <?php else: ?>
                                    <div class="form-group mb-3">
                                        <label for="<?= esc($field['name']) ?>"><?= esc($field['label']) ?></label>
                                        <input type="<?= esc($field['type']) ?>"
                                            name="<?= esc($field['name']) ?>"
                                            class="form-control"
                                            value="<?= esc($field['value']) ?>">

                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>

                            <button type="submit" class="btn btn-primary">Update</button>
                        </form>

                    </div>
                </div>
            </div>



            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<!-- custom scripts for saving the form -->

<script>
    document.getElementById('midbarheaderForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = e.target;
        const inputs = form.querySelectorAll('input');

        const fields = [];
        const fileReadPromises = [];

        inputs.forEach(input => {
            const label = form.querySelector(`label[for="${input.id}"]`);
            const field = {
                label: label ? label.textContent.trim() : input.name,
                name: input.name,
                type: input.type
            };

            if (input.type === 'file') {
                if (input.files.length > 0) {
                    const file = input.files[0];
                    const reader = new FileReader();

                    // Wrap FileReader in a Promise to wait later
                    const promise = new Promise((resolve, reject) => {
                        reader.onload = function(event) {
                            field.src = event.target.result;
                            resolve(field);
                        };
                        reader.onerror = reject;
                    });

                    reader.readAsDataURL(file);
                    fileReadPromises.push(promise);
                } else {
                    field.src = '';
                    fields.push(field);
                }
            } else {
                field.value = input.value;
                fields.push(field);
            }
        });

        // Once all file reads are complete, send the full JSON
        Promise.all(fileReadPromises).then(fileFields => {
            const allFields = [...fields, ...fileFields];

            const finalJson = JSON.stringify({
                fields: allFields
            }, null, 2);

            fetch("<?= base_url(); ?>dashboard/updateMidBarHeader", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    body: finalJson
                })
                .then(res => res.json())
                .then(data => {
                    alert("Saved successfully!");
                    location.reload(); // Reload the page to see changes
                })
                .catch(err => {
                    console.error("Error saving form data:", err);
                });
        });
    });
</script>

<script>
    function previewLogomainImage(input) {
        const file = input.files[0];
        const preview = document.getElementById('logoPreview');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
            }
            reader.readAsDataURL(file);
        }
    }
</script>
<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>