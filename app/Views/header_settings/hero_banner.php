<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->


            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Banner List</h4>
                    <hr>
                    <div class="row gy-3 mb-4">
                        <div class="col-md-10">
                            <button type="button" onclick="addNewBanner();" class="btn btn-success">
                                <i class="fa fa-plus"></i> Add New Banner
                            </button>
                        </div>
                    </div>
                    <?php foreach ($banner as $banner): ?>
                        <div class="row align-items-start mb-4">
                            <!-- Left: Banner Image Preview -->
                            <div class="col-md-8">
                                <?php 
                                    // Check if banner is a URL or local file
                                    $imageSrc = filter_var($banner->banner, FILTER_VALIDATE_URL) 
                                        ? $banner->banner 
                                        : base_url('uploads/hero/' . $banner->banner);
                                ?>
                                <img id="bannerImagePreview_<?= $banner->id ?>"
                                    src="<?= $imageSrc ?>"
                                    alt="Banner Image"
                                    class="img-fluid rounded shadow"
                                    style="max-width: 100%; height: auto;">
                            </div>

                            <!-- Right: Title, Subtitle, Upload -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Banner Image Source</label>
                                    <div class="custom-dropdown" data-banner-id="<?= $banner->id ?>">
                                        <div class="custom-dropdown-selected" onclick="toggleCustomDropdown(<?= $banner->id ?>)">
                                            <span id="selectedText_<?= $banner->id ?>">Upload File</span>
                                            <i class="fa fa-chevron-down dropdown-arrow" id="dropdownArrow_<?= $banner->id ?>"></i>
                                        </div>
                                        <div class="custom-dropdown-options" id="dropdownOptions_<?= $banner->id ?>">
                                            <div class="custom-dropdown-option" data-value="upload" onclick="selectDropdownOption(<?= $banner->id ?>, 'upload', 'Upload File')">
                                                <i class="fa fa-upload"></i> Upload File
                                            </div>
                                            <div class="custom-dropdown-option" data-value="url" onclick="selectDropdownOption(<?= $banner->id ?>, 'url', 'Paste URL')">
                                                <i class="fa fa-link"></i> Paste URL
                                            </div>
                                            <div class="custom-dropdown-option" data-value="filemanager" onclick="selectDropdownOption(<?= $banner->id ?>, 'filemanager', 'Choose from File Manager')">
                                                <i class="fa fa-folder-open"></i> Choose from File Manager
                                            </div>
                                        </div>
                                        <input type="hidden" id="imageSourceType_<?= $banner->id ?>" value="upload">
                                    </div>
                                </div>

                                <div class="mb-3" id="uploadSection_<?= $banner->id ?>">
                                    <label class="form-label">Change Banner Image</label>
                                    <input type="file"
                                        class="form-control banner-file-input"
                                        data-id="<?= $banner->id ?>"
                                        onchange="previewBannerImage(this)">
                                </div>

                                <div class="mb-3" id="urlSection_<?= $banner->id ?>" style="display: none;">
                                    <label class="form-label">Banner Image URL</label>
                                    <input type="url"
                                        class="form-control banner-url-input"
                                        data-id="<?= $banner->id ?>"
                                        placeholder="https://example.com/image.jpg"
                                        onchange="previewBannerImageFromUrl(this)">
                                </div>

                                <div class="mb-3" id="fileManagerSection_<?= $banner->id ?>" style="display: none;">
                                    <label class="form-label">Choose from File Manager</label>
                                    <div class="input-group">
                                        <input type="text"
                                            class="form-control banner-filemanager-input"
                                            data-id="<?= $banner->id ?>"
                                            placeholder="Selected file will appear here..."
                                            readonly>
                                        <button class="btn btn-outline-secondary" type="button" onclick="openFileManagerPicker(<?= $banner->id ?>)">
                                            <i class="fa fa-folder-open"></i> Browse
                                        </button>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Banner Title</label>
                                    <input type="text"
                                        id="bannerTitle_<?= $banner->id ?>"
                                        class="form-control"
                                        value="<?= esc($banner->title); ?>">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Banner Sub-Title</label>
                                    <input type="text"
                                        id="bannerSubtitle_<?= $banner->id ?>"
                                        class="form-control"
                                        value="<?= esc($banner->subtitle); ?>">
                                </div>

                                <button type="button"
                                    class="btn btn-primary me-2"
                                    onclick="editBanner(<?= $banner->id ?>)">Update Banner</button>
                                
                                <button type="button"
                                    class="btn btn-danger"
                                    onclick="deleteBanner(<?= $banner->id ?>)">Delete Banner</button>
                            </div>
                        </div>
                    <?php endforeach; ?>

                </div>

            </div>
        </div>

        <!-- Add New Banner Modal -->
        <div class="modal fade" id="addNewBannerModal" tabindex="-1" aria-labelledby="addNewBannerModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content" style="background: rgba(48, 48, 48, 1); border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #dddddd1f;">
                        <h5 class="modal-title" id="addNewBannerModalLabel">
                            <i class="fa fa-plus"></i> Add New Banner
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="padding: 20px;">
                        <form id="addBannerForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Banner Title</label>
                                        <input type="text" id="newBannerTitle" class="form-control" placeholder="Enter banner title..." required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Banner Subtitle</label>
                                        <input type="text" id="newBannerSubtitle" class="form-control" placeholder="Enter banner subtitle..." required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Banner Image Source</label>
                                <div class="custom-dropdown" data-banner-id="new">
                                    <div class="custom-dropdown-selected" onclick="toggleCustomDropdown('new')">
                                        <span id="selectedText_new">Upload File</span>
                                        <i class="fa fa-chevron-down dropdown-arrow" id="dropdownArrow_new"></i>
                                    </div>
                                    <div class="custom-dropdown-options" id="dropdownOptions_new">
                                        <div class="custom-dropdown-option" data-value="upload" onclick="selectDropdownOption('new', 'upload', 'Upload File')">
                                            <i class="fa fa-upload"></i> Upload File
                                        </div>
                                        <div class="custom-dropdown-option" data-value="url" onclick="selectDropdownOption('new', 'url', 'Paste URL')">
                                            <i class="fa fa-link"></i> Paste URL
                                        </div>
                                        <div class="custom-dropdown-option" data-value="filemanager" onclick="selectDropdownOption('new', 'filemanager', 'Choose from File Manager')">
                                            <i class="fa fa-folder-open"></i> Choose from File Manager
                                        </div>
                                    </div>
                                    <input type="hidden" id="imageSourceType_new" value="upload">
                                </div>
                            </div>

                            <div class="mb-3" id="uploadSection_new">
                                <label class="form-label">Choose Banner Image</label>
                                <input type="file" id="newBannerFile" class="form-control" accept="image/*" onchange="previewNewBannerImage(this)">
                            </div>

                            <div class="mb-3" id="urlSection_new" style="display: none;">
                                <label class="form-label">Banner Image URL</label>
                                <input type="url" id="newBannerUrl" class="form-control" placeholder="https://example.com/image.jpg" onchange="previewNewBannerImageFromUrl(this)">
                            </div>

                            <div class="mb-3" id="fileManagerSection_new" style="display: none;">
                                <label class="form-label">Choose from File Manager</label>
                                <div class="input-group">
                                    <input type="text" id="newBannerFileManager" class="form-control" placeholder="Selected file will appear here..." readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="openFileManagerPicker('new')">
                                        <i class="fa fa-folder-open"></i> Browse
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Image Preview</label>
                                <div class="border rounded p-3 text-center" style="min-height: 200px; background: #f8f9fa;">
                                    <img id="newBannerPreview" src="" alt="Preview" class="img-fluid rounded" style="max-height: 180px; display: none;">
                                    <div id="previewPlaceholder" class="text-muted">
                                        <i class="fa fa-image fa-3x mb-2"></i>
                                        <p>Image preview will appear here</p>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer" style="padding: 20px; border-top: 1px solid #dddddd1f;">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="submitAddBannerForm()">
                            <i class="fa fa-plus"></i> Add Banner
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>

        <!--End Dashboard Content-->

        <!-- File Manager Picker Modal -->
        <div class="modal fade" id="fileManagerPickerModal" tabindex="-1" aria-labelledby="fileManagerPickerModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content" style="background: rgba(48, 48, 48, 1); border-radius: 1rem; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                    <div class="modal-header" style="padding: 20px; border-bottom: 1px solid #dddddd1f; display: flex; justify-content: space-between; align-items: center;">
                        <h5 class="modal-title" id="fileManagerPickerModalLabel">
                            <i class="fa fa-folder-open"></i> Choose Image from File Manager
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="height: 500px; overflow-y: auto;">
                        <div id="fileManagerPickerContent">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p>Loading file manager...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer" style="padding: 20px; border-top: 1px solid #dddddd1f; display: flex; justify-content: flex-end; gap: 10px;">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="selectFileBtn" onclick="selectCurrentFile()" disabled>Select File</button>
                    </div>
                </div>
            </div>
        </div>

        <!--start overlay-->
        <div class="overlay toggle-menu"></div>
        <!--end overlay-->

    </div>
    <!-- End container-fluid-->

</div><!--End content-wrapper-->
<!--Start Back To Top Button-->
<a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
<!--End Back To Top Button-->

<!--Start footer-->

<!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('styles'); ?>
<style>
/* Custom Dropdown Styles */
.custom-dropdown {
    position: relative;
    width: 100%;
}

.custom-dropdown-selected {
    background: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.575rem 0.75rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.2s ease;
    user-select: none;
}

.custom-dropdown-selected:hover {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.custom-dropdown-selected.active {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.dropdown-arrow {
    transition: transform 0.2s ease;
    color: #6c757d;
}

.dropdown-arrow.rotated {
    transform: rotate(180deg);
}

.custom-dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: none;
    max-height: 200px;
    overflow-y: auto;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.custom-dropdown-options.show {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.custom-dropdown-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.custom-dropdown-option:hover {
    background-color: #f8f9fa;
}

.custom-dropdown-option:active {
    background-color: #e9ecef;
}

.custom-dropdown-option i {
    width: 16px;
    color: #6c757d;
}

/* Dark theme support for modals */
.modal-content {
    color: #fff;
}

.modal-content .form-control {
    background-color: #495057;
    border-color: #6c757d;
    color: #fff;
}

.modal-content .form-control:focus {
    background-color: #495057;
    border-color: #86b7fe;
    color: #fff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.modal-content .form-label {
    color: #fff;
}

/* Custom dropdown in dark modal */
.modal-content .custom-dropdown-selected {
    background: #495057;
    border-color: #6c757d;
    color: #fff;
}

.modal-content .custom-dropdown-selected:hover {
    border-color: #86b7fe;
}

.modal-content .custom-dropdown-options {
    background: #495057;
    border-color: #6c757d;
    color: #fff;
}

.modal-content .custom-dropdown-option:hover {
    background-color: #6c757d;
}

.modal-content .custom-dropdown-option:active {
    background-color: #5a6268;
}

.modal-content .custom-dropdown-option i {
    color: #fff;
}
</style>
<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<!-- custom scripts for saving the form -->


<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
    // Initialize page when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Initializing custom dropdowns...');
        
        // Ensure all dropdowns are closed initially
        document.querySelectorAll('.custom-dropdown-options').forEach((options, index) => {
            options.classList.remove('show');
            options.style.display = 'none'; // Force hide
            console.log('Hidden dropdown', index, options.id);
        });
        
        // Ensure all arrows are in correct position
        document.querySelectorAll('.dropdown-arrow').forEach(arrow => {
            arrow.classList.remove('rotated');
        });
        
        // Ensure all selected elements are not active
        document.querySelectorAll('.custom-dropdown-selected').forEach(selected => {
            selected.classList.remove('active');
        });
        
        console.log('Custom dropdowns initialized');
    });

    // Custom Dropdown Functions
    function toggleCustomDropdown(id) {
        console.log('Toggling dropdown:', id);
        
        const options = document.getElementById('dropdownOptions_' + id);
        const arrow = document.getElementById('dropdownArrow_' + id);
        const selected = options.parentElement.querySelector('.custom-dropdown-selected');
        
        if (!options || !arrow || !selected) {
            console.error('Missing dropdown elements for:', id);
            return;
        }
        
        // Close all other dropdowns first
        document.querySelectorAll('.custom-dropdown-options').forEach(opt => {
            if (opt.id !== 'dropdownOptions_' + id) {
                opt.classList.remove('show');
                opt.style.display = 'none';
                const otherArrow = opt.parentElement.querySelector('.dropdown-arrow');
                const otherSelected = opt.parentElement.querySelector('.custom-dropdown-selected');
                if (otherArrow) otherArrow.classList.remove('rotated');
                if (otherSelected) otherSelected.classList.remove('active');
            }
        });
        
        // Toggle current dropdown
        const isOpen = options.classList.contains('show');
        
        if (isOpen) {
            // Close dropdown
            options.classList.remove('show');
            options.style.display = 'none';
            arrow.classList.remove('rotated');
            selected.classList.remove('active');
            console.log('Closed dropdown:', id);
        } else {
            // Open dropdown
            options.style.display = 'block';
            options.classList.add('show');
            arrow.classList.add('rotated');
            selected.classList.add('active');
            console.log('Opened dropdown:', id);
        }
    }

    function selectDropdownOption(id, value, text) {
        console.log('Selecting option:', id, value, text);
        
        const selectedText = document.getElementById('selectedText_' + id);
        const hiddenInput = document.getElementById('imageSourceType_' + id);
        const options = document.getElementById('dropdownOptions_' + id);
        const arrow = document.getElementById('dropdownArrow_' + id);
        const selected = options.parentElement.querySelector('.custom-dropdown-selected');
        
        // Update text and value
        selectedText.textContent = text;
        hiddenInput.value = value;
        
        // Close dropdown
        options.classList.remove('show');
        options.style.display = 'none';
        arrow.classList.remove('rotated');
        selected.classList.remove('active');
        
        // Toggle sections based on selection
        toggleImageSource(id);
        
        console.log('Option selected and dropdown closed');
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.custom-dropdown')) {
            document.querySelectorAll('.custom-dropdown-options').forEach(options => {
                options.classList.remove('show');
                options.style.display = 'none';
                const arrow = options.parentElement.querySelector('.dropdown-arrow');
                const selected = options.parentElement.querySelector('.custom-dropdown-selected');
                if (arrow) arrow.classList.remove('rotated');
                if (selected) selected.classList.remove('active');
            });
        }
    });

    function toggleImageSource(id) {
        const sourceType = document.getElementById('imageSourceType_' + id).value;
        const uploadSection = document.getElementById('uploadSection_' + id);
        const urlSection = document.getElementById('urlSection_' + id);
        const fileManagerSection = document.getElementById('fileManagerSection_' + id);

        // Hide all sections first
        if (uploadSection) uploadSection.style.display = 'none';
        if (urlSection) urlSection.style.display = 'none';
        if (fileManagerSection) fileManagerSection.style.display = 'none';

        // Show the selected section
        if (sourceType === 'upload' && uploadSection) {
            uploadSection.style.display = 'block';
        } else if (sourceType === 'url' && urlSection) {
            urlSection.style.display = 'block';
        } else if (sourceType === 'filemanager' && fileManagerSection) {
            fileManagerSection.style.display = 'block';
        }
    }

    function previewBannerImage(input) {
        const file = input.files[0];
        const id = input.getAttribute('data-id');
        const preview = document.getElementById('bannerImagePreview_' + id);

        if (file && preview) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }

    function previewBannerImageFromUrl(input) {
        const url = input.value;
        const id = input.getAttribute('data-id');
        const preview = document.getElementById('bannerImagePreview_' + id);

        if (url && preview) {
            preview.src = url;
            preview.onerror = function() {
                alert('Failed to load image from URL. Please check the URL.');
                // Keep the original image if URL fails
                const originalSrc = preview.getAttribute('data-original-src');
                if (originalSrc) {
                    preview.src = originalSrc;
                }
            };
            // Store original src for fallback
            if (!preview.getAttribute('data-original-src')) {
                preview.setAttribute('data-original-src', preview.src);
            }
        }
    }

    function editBanner(id) {
        const title = document.getElementById('bannerTitle_' + id).value;
        const subtitle = document.getElementById('bannerSubtitle_' + id).value;
        const sourceType = document.getElementById('imageSourceType_' + id).value;
        const imageInput = document.querySelector(`input[data-id="${id}"].banner-file-input`);
        const urlInput = document.querySelector(`input[data-id="${id}"].banner-url-input`);
        const fileManagerInput = document.querySelector(`input[data-id="${id}"].banner-filemanager-input`);

        const formData = new FormData();
        formData.append('bannerId', id);
        formData.append('title', title);
        formData.append('subtitle', subtitle);
        formData.append('imageSourceType', sourceType);

        if (sourceType === 'upload' && imageInput.files.length > 0) {
            formData.append('image', imageInput.files[0]);
        } else if (sourceType === 'url' && urlInput.value) {
            formData.append('imageUrl', urlInput.value);
        } else if (sourceType === 'filemanager' && fileManagerInput.value) {
            formData.append('imageUrl', fileManagerInput.value);
        }

        fetch("<?= base_url('dashboard/updateHeroBanner') ?>", {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                alert(data.message);
                location.reload();
            })
            .catch(err => {
                console.error("Banner update error:", err);
            });
    }

    function deleteBanner(id) {
        if (confirm('Are you sure you want to delete this banner?')) {
            const formData = new FormData();
            formData.append('bannerId', id);

            fetch("<?= base_url('dashboard/deleteHeroBanner') ?>", {
                    method: 'POST',
                    body: formData
                })
                .then(res => res.json())
                .then(data => {
                    alert(data.message);
                    location.reload();
                })
                .catch(err => {
                    console.error("Banner delete error:", err);
                });
        }
    }

    function addNewBanner() {
        // Reset form
        document.getElementById('addBannerForm').reset();
        document.getElementById('newBannerTitle').value = '';
        document.getElementById('newBannerSubtitle').value = '';
        document.getElementById('imageSourceType_new').value = 'upload';
        document.getElementById('selectedText_new').textContent = 'Upload File';
        
        // Reset sections
        document.getElementById('uploadSection_new').style.display = 'block';
        document.getElementById('urlSection_new').style.display = 'none';
        document.getElementById('fileManagerSection_new').style.display = 'none';
        
        // Reset preview
        document.getElementById('newBannerPreview').style.display = 'none';
        document.getElementById('previewPlaceholder').style.display = 'block';
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('addNewBannerModal'));
        modal.show();
    }

    function previewNewBannerImage(input) {
        const file = input.files[0];
        const preview = document.getElementById('newBannerPreview');
        const placeholder = document.getElementById('previewPlaceholder');

        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                preview.style.display = 'block';
                placeholder.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }
    }

    function previewNewBannerImageFromUrl(input) {
        const url = input.value;
        const preview = document.getElementById('newBannerPreview');
        const placeholder = document.getElementById('previewPlaceholder');

        if (url) {
            preview.src = url;
            preview.style.display = 'block';
            placeholder.style.display = 'none';
            preview.onerror = function() {
                alert('Failed to load image from URL. Please check the URL.');
                preview.style.display = 'none';
                placeholder.style.display = 'block';
            };
        }
    }

    function submitAddBannerForm() {
        const title = document.getElementById('newBannerTitle').value.trim();
        const subtitle = document.getElementById('newBannerSubtitle').value.trim();
        const sourceType = document.getElementById('imageSourceType_new').value;
        
        if (!title) {
            alert('Please enter a banner title');
            return;
        }
        
        if (!subtitle) {
            alert('Please enter a banner subtitle');
            return;
        }

        const formData = new FormData();
        formData.append('title', title);
        formData.append('subtitle', subtitle);
        formData.append('imageSourceType', sourceType);

        if (sourceType === 'upload') {
            const fileInput = document.getElementById('newBannerFile');
            if (fileInput.files.length === 0) {
                alert('Please select an image file');
                return;
            }
            formData.append('image', fileInput.files[0]);
        } else if (sourceType === 'url') {
            const urlInput = document.getElementById('newBannerUrl');
            if (!urlInput.value.trim()) {
                alert('Please enter an image URL');
                return;
            }
            formData.append('imageUrl', urlInput.value.trim());
        } else if (sourceType === 'filemanager') {
            const fileManagerInput = document.getElementById('newBannerFileManager');
            if (!fileManagerInput.value.trim()) {
                alert('Please select a file from the file manager');
                return;
            }
            formData.append('imageUrl', fileManagerInput.value.trim());
        }

        // Submit the form
        submitNewBanner(formData);
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('addNewBannerModal'));
        modal.hide();
    }

    function submitNewBanner(formData) {
        fetch("<?= base_url('dashboard/addHeroBanner') ?>", {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                alert(data.message);
                location.reload();
            })
            .catch(err => {
                console.error("Banner add error:", err);
            });
    }

    // File Manager Picker Variables
    let currentBannerId = null;
    let currentBannerAction = null; // 'edit' or 'add'
    let currentBannerData = null;
    let selectedFileUrl = null;

    function openFileManagerPicker(bannerId) {
        currentBannerId = bannerId;
        if (bannerId === 'new') {
            currentBannerAction = 'add';
        } else {
            currentBannerAction = 'edit';
        }
        openFileManagerPickerModal();
    }

    function openFileManagerPickerModal() {
        const modal = new bootstrap.Modal(document.getElementById('fileManagerPickerModal'));
        modal.show();
        
        // Load image files from file manager
        fetch('<?= base_url('dashboard/getFileManagerImages') ?>')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    renderFilePickerGrid(data.files);
                } else {
                    document.getElementById('fileManagerPickerContent').innerHTML = 
                        '<div class="alert alert-warning">No images found in file manager.</div>';
                }
            })
            .catch(err => {
                console.error('Error loading images:', err);
                document.getElementById('fileManagerPickerContent').innerHTML = 
                    '<div class="alert alert-danger">Error loading images. Please try again.</div>';
            });
    }

    function renderFilePickerGrid(files) {
        if (files.length === 0) {
            document.getElementById('fileManagerPickerContent').innerHTML = 
                '<div class="alert alert-info">No image files found in the file manager.</div>';
            return;
        }

        let html = '<div class="row g-3">';
        files.forEach(file => {
            html += `
                <div class="col-md-3 col-sm-4 col-6">
                    <div class="card file-picker-item" style="cursor: pointer;" onclick="selectFile('${file.url}', '${file.name}')">
                        <div class="position-relative">
                            <img src="${file.url}" class="card-img-top" style="height: 150px; object-fit: contain;" alt="${file.name}">
                            <div class="position-absolute top-0 end-0 p-2">
                                <span class="badge bg-primary">${getFileExtension(file.name)}</span>
                            </div>
                        </div>
                        <div class="card-body p-2">
                            <h6 class="card-title text-truncate mb-1" style="font-size: 0.85rem;">${file.name}</h6>
                            <small class="text-muted">${formatFileSize(file.size)}</small>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';

        document.getElementById('fileManagerPickerContent').innerHTML = html;
    }

    function selectFile(fileUrl, fileName) {
        // Remove previous selection
        document.querySelectorAll('.file-picker-item').forEach(item => {
            item.classList.remove('border-primary');
            item.style.borderWidth = '';
        });
        
        // Mark current selection
        event.currentTarget.classList.add('border-primary');
        event.currentTarget.style.borderWidth = '3px';
        
        selectedFileUrl = fileUrl;
        document.getElementById('selectFileBtn').disabled = false;
        
        console.log('Selected file:', fileName, fileUrl);
    }

    function getFileExtension(fileName) {
        return fileName.split('.').pop().toUpperCase();
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function selectCurrentFile() {
        if (!selectedFileUrl) {
            alert('Please select a file first.');
            return;
        }

        if (currentBannerAction === 'edit' && currentBannerId && currentBannerId !== 'new') {
            // Update existing banner
            const fileManagerInput = document.querySelector(`input[data-id="${currentBannerId}"].banner-filemanager-input`);
            if (fileManagerInput) {
                fileManagerInput.value = selectedFileUrl;
                
                // Update preview
                const preview = document.getElementById('bannerImagePreview_' + currentBannerId);
                if (preview) {
                    preview.src = selectedFileUrl;
                }
            }
        } else if (currentBannerAction === 'add' && currentBannerId === 'new') {
            // Update new banner form
            const fileManagerInput = document.getElementById('newBannerFileManager');
            const preview = document.getElementById('newBannerPreview');
            const placeholder = document.getElementById('previewPlaceholder');
            
            if (fileManagerInput) {
                fileManagerInput.value = selectedFileUrl;
                
                // Update preview
                if (preview) {
                    preview.src = selectedFileUrl;
                    preview.style.display = 'block';
                    placeholder.style.display = 'none';
                }
            }
        }

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('fileManagerPickerModal'));
        modal.hide();
        
        // Reset variables
        selectedFileUrl = null;
        currentBannerId = null;
        currentBannerAction = null;
        currentBannerData = null;
    }
</script>

<?= $this->endSection(); ?>