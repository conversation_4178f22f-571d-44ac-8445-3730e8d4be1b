<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->


            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Footer Settings</h4>
                    <hr>
                    <div class="row gy-3">
                        <div class="col-md-12 d-flex justify-content-between align-items-center mb-3">
                            <!-- Left Button -->
                            <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#bannerFormCollapse" aria-expanded="false" aria-controls="bannerFormCollapse">
                                Create New
                            </button>

                            <!-- Right Button -->
                            <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#categoryFormCollapse" aria-expanded="false" aria-controls="bannerFormCollapse">
                                Create a Category
                            </button>
                        </div>
                    </div>

                    <!-- Collapsible Form -->
                    <div class="collapse mt-4" id="bannerFormCollapse">

                        <form class="row g-3" id="footer_settingsForm" enctype="multipart/form-data">

                            <!-- Footer Name Dropdown -->
                            <div class="col-md-4">
                                <label for="footername" class="form-label">Select Footer Name</label>
                                <select class="form-control" id="footername" name="footercategory" required>
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= esc($category['id']) ?>"><?= esc($category['category_name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- Repeatable Meta Title & Link Fields -->
                            <div class="col-12">
                                <label class="form-label">Footer Meta Links</label>
                                <div id="metaFieldsContainer">
                                    <!-- Initial Meta Title & Link -->
                                    <div class="row mb-2 align-items-center meta-group">
                                        <div class="col-md-5">
                                            <input type="text" name="metatitle[]" class="form-control" placeholder="Meta Title" required>
                                        </div>
                                        <div class="col-md-5">
                                            <input type="url" name="metalink[]" class="form-control" placeholder="Meta Link" required>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-danger btn-sm w-100 remove-meta" disabled>Remove</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add More Button -->
                                <div class="mt-2">
                                    <button type="button" class="btn btn-secondary btn-sm" onclick="addMetaField()">+ Add More</button>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <button type="submit" class="btn btn-light px-5">Submit</button>
                            </div>
                        </form>

                    </div>
                    <div class="collapse mt-4" id="categoryFormCollapse">

                        <form class="row g-3" id="footer_settingsCategoryForm" enctype="multipart/form-data">
                            <div class="col-md-6">
                                <label for="categoryname" class="form-label">Category Name</label>
                                <input type="text" name="categoryname" class="form-control" id="categoryname" placeholder="Enter footer name" required>
                            </div>
                            <div class="col-12 mt-2">
                                <button type="submit" class="btn btn-light px-5">Create Category</button>
                            </div>
                        </form>

                    </div>




                </div>

            </div>
            <div class="form-row mt-3">
                <div class="col-12">
                    <div id="todo-container">
                        <?php foreach ($footer_settings as $footer): ?>
                            <div class="pb-3 todo-item">

                                <div class="accordion" id="accordion_<?= esc($footer['footer_category']) ?>">
                                    <div class="card shadow-sm mb-3">
                                        <!-- Card Header -->
                                        <div data-bs-toggle="collapse" data-bs-target="#collapse_<?= esc($footer['footer_category']) ?>" aria-expanded="true" aria-controls="collapse_<?= esc($footer['footer_category']) ?>" class="card-header d-flex justify-content-between align-items-center" id="heading_<?= esc($footer['footer_category']) ?>">
                                            <h5 class="mb-0 d-flex align-items-center">
                                                <button class="btn btn-link" type="button">
                                                    <?= esc($footer['category_name']) ?>
                                                </button>
                                            </h5>
                                        </div>

                                        <!-- Card Body -->
                                        <div id="collapse_<?= esc($footer['footer_category']) ?>" class="collapse" aria-labelledby="heading_<?= esc($footer['footer_category']) ?>" data-bs-parent="#accordion_<?= esc($footer['footer_category']) ?>">
                                            <form id="footerForm_<?= esc($footer['footer_category']) ?>" class="editfooter_form" enctype="multipart/form-data">
                                                <input type="hidden" name="footer_category" value="<?= esc($footer['footer_category']) ?>">

                                                <div class="card-body">

                                                    <!-- Repeatable Meta Title & Link Fields -->
                                                    <div id="metaFieldsContainer_<?= esc($footer['footer_category']) ?>">

                                                        <?php
                                                        // Decode JSON data
                                                        $footerData = json_decode($footer['footer_data']);
                                                        if (!empty($footerData) && is_array($footerData)) {
                                                            foreach ($footerData as $item):
                                                        ?>
                                                                <div class="row mb-2 align-items-center meta-group">
                                                                    <!-- Hidden ID Input -->
                                                                    <input type="hidden" name="link_id[]" value="<?= esc($item->id) ?>">

                                                                    <div class="col-md-3">
                                                                        <input type="text" name="metatitle[]" id="metatitle_<?= esc($item->id) ?>" class="form-control" placeholder="Meta Title" value="<?= esc($item->title) ?>" required>
                                                                    </div>
                                                                    <div class="col-md-5">
                                                                        <input type="url" name="metalink[]" id="metalink_<?= esc($item->id) ?>" class="form-control" placeholder="Meta Link" value="<?= esc($item->link) ?>" required>
                                                                    </div>
                                                                    <div class="col-md-2">
                                                                        <button type="button" onclick="updateMetaField(<?= esc($item->id) ?>)" class="btn btn-success btn-sm w-100 remove-meta">Update</button>
                                                                    </div>
                                                                    <div class="col-md-2">

                                                                        <button type="button" onclick="removeMetaField(<?= esc($item->id) ?>)" class="btn btn-danger btn-sm w-100 remove-meta">Remove</button>
                                                                    </div>
                                                                </div>
                                                        <?php
                                                            endforeach;
                                                        }
                                                        ?>

                                                    </div>

                        
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>




                <!--End Dashboard Content-->

                <!--start overlay-->
                <div class="overlay toggle-menu"></div>
                <!--end overlay-->

            </div>

            <!-- End container-fluid-->

        </div><!--End content-wrapper-->
        <div class="row">
            <div class="col-md-12">
                <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
            </div>
        </div>
        <!--Start Back To Top Button-->
        <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
        <!--End Back To Top Button-->

        <!--Start footer-->

        <!--End footer-->


    </div><!--End wrapper-->

    <!--start color switcher-->
    <?= $this->include('partials/colorswitcher'); ?>
    <!--end color switcher-->

    <?= $this->endSection(); ?>

    <?= $this->section('scripts'); ?>
    <!-- Chart.js -->
    <script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
    <!-- custom scripts for saving the form -->


    <!-- custom form ends here -->
    <!-- Dashboard Index Script -->
    <script src="<?= base_url('assets/js/index.js'); ?>"></script>

    <script>
        $('#footer_settingsForm').on('submit', function(e) {

            e.preventDefault();

            const formData = new FormData(this); // Automatically includes file input

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxAddfooter_settingsForm", formData, function(res) {
                if (res.status === 'success') {

                    // $('#editNoticeForm')[0].reset(); // Optional reset
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            });
        });
        $('#footer_settingsCategoryForm').on('submit', function(e) {

            e.preventDefault();

            const formData = new FormData(this); // Automatically includes file input

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxAddfooter_category", formData, function(res) {
                if (res.status === 'success') {

                    // $('#editNoticeForm')[0].reset(); // Optional reset
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            });
        });


        function updateMetaField(id) {
            const title = document.getElementById(`metatitle_${id}`).value;
            const link = document.getElementById(`metalink_${id}`).value;

            if (!title || !link) {
                alert("Both fields are required.");
                return;
            }
            $.ajax({
                url: "<?= base_url('dashboard/ajaxEditfooter_settingsForm'); ?>",
                type: "POST",
                data: {
                    id: id,
                    metatitle: title,
                    metalink: link
                },
                success: function(response) {
                    if (response.status === 'success') {
                        alert("Meta field updated successfully.");
                        window.location.reload();
                    } else {
                        alert("Failed to update meta field.");
                    }
                },
                error: function() {
                    alert("An error occurred while updating the meta field.");
                }
            });
        }

        function removeMetaField(id) {
            if (confirm("Are you sure you want to remove this meta field?")) {
                $.ajax({
                    url: "<?= base_url('dashboard/ajaxDeletefooter_settingsForm'); ?>",
                    type: "POST",
                    data: {
                        id: id
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            alert("Meta field removed successfully.");
                            window.location.reload();
                        } else {
                            alert("Failed to remove meta field.");
                        }
                    },
                    error: function() {
                        alert("An error occurred while removing the meta field.");
                    }
                });
            }
        }
    </script>
    <script>
        function addMetaField() {
            const container = document.getElementById("metaFieldsContainer");

            // Create new row
            const newRow = document.createElement("div");
            newRow.classList.add("row", "mb-2", "align-items-center", "meta-group");

            newRow.innerHTML = `
            <div class="col-md-5">
                <input type="text" name="metatitle[]" class="form-control" placeholder="Meta Title" required>
            </div>
            <div class="col-md-5">
                <input type="url" name="metalink[]" class="form-control" placeholder="Meta Link" required>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-danger btn-sm w-100 remove-meta">Remove</button>
            </div>
        `;

            container.appendChild(newRow);

            // Attach remove event
            newRow.querySelector('.remove-meta').addEventListener('click', function() {
                newRow.remove();
            });
        }

        // Optional: Disable remove button on first item
        document.addEventListener("DOMContentLoaded", function() {
            const firstRemoveBtn = document.querySelector(".meta-group .remove-meta");
            if (firstRemoveBtn) {
                firstRemoveBtn.disabled = true;
            }
        });
    </script>

    <?= $this->endSection(); ?>