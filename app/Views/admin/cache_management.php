<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= $title ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-memory mr-2"></i>
                        Cache Management
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-info" onclick="refreshCacheStatus()">
                            <i class="fas fa-sync-alt"></i> Refresh Status
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Cache Status Overview -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Cache Status Overview</h5>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm">
                                    <thead>
                                        <tr>
                                            <th>Cache Key</th>
                                            <th>Status</th>
                                            <th>Size (bytes)</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cache-status-table">
                                        <?php foreach ($cache_status as $key => $status): ?>
                                        <tr>
                                            <td><code><?= esc($key) ?></code></td>
                                            <td>
                                                <?php if ($status['exists']): ?>
                                                    <span class="badge badge-success">Cached</span>
                                                <?php else: ?>
                                                    <span class="badge badge-secondary">Not Cached</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= number_format($status['size']) ?></td>
                                            <td>
                                                <?php if ($key === 'navigation_faculty_hierarchy'): ?>
                                                    <button class="btn btn-sm btn-warning" onclick="clearHierarchyCache()">
                                                        <i class="fas fa-trash"></i> Clear
                                                    </button>
                                                <?php elseif ($key === 'navigation_university_stats'): ?>
                                                    <button class="btn btn-sm btn-warning" onclick="clearStatsCache()">
                                                        <i class="fas fa-trash"></i> Clear
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <small class="text-muted">Last updated: <?= $last_updated ?></small>
                        </div>
                    </div>

                    <!-- Bulk Cache Operations -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Bulk Cache Operations</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-danger" onclick="clearAllNavigationCache()">
                                    <i class="fas fa-trash-alt"></i> Clear All Navigation Caches
                                </button>
                                <button type="button" class="btn btn-warning" onclick="clearHierarchyCache()">
                                    <i class="fas fa-sitemap"></i> Clear Hierarchy Only
                                </button>
                                <button type="button" class="btn btn-warning" onclick="clearStatsCache()">
                                    <i class="fas fa-chart-bar"></i> Clear Statistics Only
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Selective Cache Clearing -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Clear Faculty Cache</h6>
                                </div>
                                <div class="card-body">
                                    <form id="clear-faculty-cache-form">
                                        <div class="form-group">
                                            <label for="faculty_select">Select Faculty:</label>
                                            <select class="form-control" id="faculty_select" name="faculty_id" required>
                                                <option value="">Choose a faculty...</option>
                                                <?php foreach ($faculties as $faculty): ?>
                                                    <option value="<?= $faculty['id'] ?>"><?= esc($faculty['faculty_name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-warning btn-sm">
                                            <i class="fas fa-trash"></i> Clear Faculty Cache
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Clear Department Cache</h6>
                                </div>
                                <div class="card-body">
                                    <form id="clear-department-cache-form">
                                        <div class="form-group">
                                            <label for="department_select">Select Department:</label>
                                            <select class="form-control" id="department_select" name="department_id" required>
                                                <option value="">Choose a department...</option>
                                                <?php foreach ($departments as $department): ?>
                                                    <option value="<?= $department['id'] ?>"><?= esc($department['department_name']) ?></option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <button type="submit" class="btn btn-warning btn-sm">
                                            <i class="fas fa-trash"></i> Clear Department Cache
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<div id="cache-messages"></div>

<script>
// Cache management functions
function showMessage(message, type = 'success') {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
    
    const messageHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            <i class="fas fa-${icon} mr-2"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    
    $('#cache-messages').html(messageHtml);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}

function clearAllNavigationCache() {
    if (!confirm('Are you sure you want to clear all navigation caches? This will affect API performance temporarily.')) {
        return;
    }
    
    $.ajax({
        url: '<?= base_url('cache/ajax/clear-navigation') ?>',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showMessage(response.message, 'success');
                refreshCacheStatus();
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage('Failed to clear navigation cache. Please try again.', 'error');
        }
    });
}

function clearHierarchyCache() {
    $.ajax({
        url: '<?= base_url('api/cache/clear/hierarchy') ?>',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showMessage(response.message, 'success');
                refreshCacheStatus();
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage('Failed to clear hierarchy cache. Please try again.', 'error');
        }
    });
}

function clearStatsCache() {
    $.ajax({
        url: '<?= base_url('api/cache/clear/stats') ?>',
        type: 'POST',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showMessage(response.message, 'success');
                refreshCacheStatus();
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage('Failed to clear statistics cache. Please try again.', 'error');
        }
    });
}

function refreshCacheStatus() {
    $.ajax({
        url: '<?= base_url('cache/ajax/status') ?>',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                updateCacheStatusTable(response.data);
                showMessage('Cache status refreshed successfully', 'success');
            } else {
                showMessage('Failed to refresh cache status', 'error');
            }
        },
        error: function() {
            showMessage('Failed to refresh cache status. Please try again.', 'error');
        }
    });
}

function updateCacheStatusTable(cacheStatus) {
    let tableHtml = '';
    
    for (const [key, status] of Object.entries(cacheStatus)) {
        const statusBadge = status.exists 
            ? '<span class="badge badge-success">Cached</span>'
            : '<span class="badge badge-secondary">Not Cached</span>';
        
        let actionButton = '';
        if (key === 'navigation_faculty_hierarchy') {
            actionButton = '<button class="btn btn-sm btn-warning" onclick="clearHierarchyCache()"><i class="fas fa-trash"></i> Clear</button>';
        } else if (key === 'navigation_university_stats') {
            actionButton = '<button class="btn btn-sm btn-warning" onclick="clearStatsCache()"><i class="fas fa-trash"></i> Clear</button>';
        }
        
        tableHtml += `
            <tr>
                <td><code>${key}</code></td>
                <td>${statusBadge}</td>
                <td>${status.size.toLocaleString()}</td>
                <td>${actionButton}</td>
            </tr>
        `;
    }
    
    $('#cache-status-table').html(tableHtml);
}

// Form submissions
$('#clear-faculty-cache-form').on('submit', function(e) {
    e.preventDefault();
    
    const facultyId = $('#faculty_select').val();
    if (!facultyId) {
        showMessage('Please select a faculty', 'error');
        return;
    }
    
    $.ajax({
        url: '<?= base_url('cache/ajax/clear-faculty') ?>',
        type: 'POST',
        data: { faculty_id: facultyId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showMessage(response.message, 'success');
                refreshCacheStatus();
                $('#faculty_select').val('');
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage('Failed to clear faculty cache. Please try again.', 'error');
        }
    });
});

$('#clear-department-cache-form').on('submit', function(e) {
    e.preventDefault();
    
    const departmentId = $('#department_select').val();
    if (!departmentId) {
        showMessage('Please select a department', 'error');
        return;
    }
    
    $.ajax({
        url: '<?= base_url('cache/ajax/clear-department') ?>',
        type: 'POST',
        data: { department_id: departmentId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success') {
                showMessage(response.message, 'success');
                refreshCacheStatus();
                $('#department_select').val('');
            } else {
                showMessage(response.message, 'error');
            }
        },
        error: function() {
            showMessage('Failed to clear department cache. Please try again.', 'error');
        }
    });
});
</script>
<?= $this->endSection() ?>