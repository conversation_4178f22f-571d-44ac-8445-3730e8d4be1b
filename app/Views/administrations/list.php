<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->
            <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-4">
                <?php foreach ($administrations as $user): ?>
                    <div class="col">
                        <div class="card radius-15">
                            <div class="card-body text-center">
                                <div class="p-4 radius-15 text-center">
                                    <form id="editadministration_<?= esc($user->id); ?>" class="form-administration">
                                        <input type="hidden" name="id" value="<?= esc($user->id); ?>">
                                        <img src="<?= base_url('uploads/administrations/') . esc($user->profile_image) ?>"
                                            width="110" height="110"
                                            class="rounded-circle shadow mb-3"
                                            alt="<?= esc($user->name) ?>">
                                        <div class="mb-3">
                                            <label class="form-label">Name</label>
                                            <input type="text"
                                                class="form-control"
                                                value="<?= esc($user->name); ?>"
                                                name="name">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control"
                                                rows="3"
                                                name="description"><?= esc($user->description); ?></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Designation</label>
                                            <textarea class="form-control"
                                                rows="3"
                                                name="designation"><?= esc($user->designation); ?></textarea>
                                        </div>

                                        <ul class="list-unstyled small mb-3">
                                            <li><strong>Archived:</strong> <?= esc($user->archived) ?></li>
                                        </ul>

                                        <div class="d-grid">
                                            <button type="submit" class="btn btn-outline-primary radius-15">
                                                <i class="zmdi zmdi-edit"></i> Edit Profile
                                            </button>
                                            <button type="button" onclick="deleteadministration(<?= esc($user->id); ?>)" class="btn btn-outline-danger radius-15">
                                                <i class="zmdi zmdi-delete"></i> Delete
                                            </button>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>


            </div>
            <!--End Dashboard Content-->
            <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>
            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>

<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<script>
    $('.form-administration').on('submit', function(e) {

        e.preventDefault();

        const formData = new FormData(this); // Automatically includes file input

        ajaxPostData("<?= base_url(); ?>dashboard/ajaxeditAdministrations", formData, function(res) {
            if (res.status === 'success') {

                // $('#editNoticeForm')[0].reset(); // Optional reset
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            }
        });
    });

    function deleteadministration(id) {
        if (confirm("Are you sure you want to delete this administration?")) {
            $.ajax({
                url: "<?= base_url(); ?>dashboard/ajaxdeleteAdministrations",
                type: "POST",
                data: { id: id },
                success: function(res) {
                    if (res.status === 'success') {
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }
                }
            });
        }
    }
</script>
<?= $this->endSection(); ?>