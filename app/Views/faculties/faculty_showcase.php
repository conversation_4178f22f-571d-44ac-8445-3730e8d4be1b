<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Faculty Showcase Content-->

            <!-- Faculty Header -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h2 class="mb-3"><?= esc($showcase['faculty']['faculty_name']); ?></h2>
                            <?php if (!empty($showcase['faculty']['faculty_description'])): ?>
                                <p class="lead"><?= esc($showcase['faculty']['faculty_description']); ?></p>
                            <?php endif; ?>
                            
                            <?php if (!empty($showcase['faculty']['dean_name'])): ?>
                                <div class="alert alert-info">
                                    <i class="zmdi zmdi-account-circle"></i>
                                    <strong>Dean:</strong> <?= esc($showcase['faculty']['dean_name']); ?>
                                    <?php if (!empty($showcase['faculty']['dean_designation'])): ?>
                                        (<?= esc($showcase['faculty']['dean_designation']); ?>)
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4">
                            <?php if (!empty($showcase['faculty']['faculty_image'])): ?>
                                <img src="<?= base_url('uploads/faculties/' . $showcase['faculty']['faculty_image']); ?>" 
                                     alt="<?= esc($showcase['faculty']['faculty_name']); ?>" 
                                     class="img-fluid rounded">
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-2">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h3><?= $showcase['statistics']['total_departments']; ?></h3>
                            <p class="mb-0">Departments</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3><?= $showcase['statistics']['total_faculty_members']; ?></h3>
                            <p class="mb-0">Faculty Members</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h3><?= $showcase['statistics']['total_phd_awardees']; ?></h3>
                            <p class="mb-0">PhD Holders</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3><?= $showcase['statistics']['total_current_students']; ?></h3>
                            <p class="mb-0">Current Students</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3><?= $showcase['statistics']['total_alumni']; ?></h3>
                            <p class="mb-0">Alumni</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card bg-dark text-white">
                        <div class="card-body text-center">
                            <h3><?= $showcase['statistics']['total_news_events']; ?></h3>
                            <p class="mb-0">News & Events</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabbed Content -->
            <div class="card">
                <div class="card-body">
                    <ul class="nav nav-tabs" id="facultyTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab">Overview</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="departments-tab" data-toggle="tab" href="#departments" role="tab">Departments</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="faculty-members-tab" data-toggle="tab" href="#faculty-members" role="tab">Faculty Members</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="phd-awardees-tab" data-toggle="tab" href="#phd-awardees" role="tab">PhD Awardees</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="students-tab" data-toggle="tab" href="#students" role="tab">Students</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="alumni-tab" data-toggle="tab" href="#alumni" role="tab">Alumni</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="news-tab" data-toggle="tab" href="#news" role="tab">News & Events</a>
                        </li>
                    </ul>

                    <div class="tab-content mt-4" id="facultyTabsContent">
                        <!-- Overview Tab -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <div class="row">
                                <?php if (!empty($showcase['faculty']['profile'])): ?>
                                    <div class="col-md-6">
                                        <h5>Faculty Profile</h5>
                                        <p><?= nl2br(esc($showcase['faculty']['profile'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($showcase['faculty']['programmes_curriculum'])): ?>
                                    <div class="col-md-6">
                                        <h5>Programmes & Curriculum</h5>
                                        <p><?= nl2br(esc($showcase['faculty']['programmes_curriculum'])); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="row mt-4">
                                <?php if (!empty($showcase['faculty']['research_groups'])): ?>
                                    <div class="col-md-6">
                                        <h5>Research Groups</h5>
                                        <p><?= nl2br(esc($showcase['faculty']['research_groups'])); ?></p>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($showcase['faculty']['contact_info'])): ?>
                                    <div class="col-md-6">
                                        <h5>Contact Information</h5>
                                        <p><?= nl2br(esc($showcase['faculty']['contact_info'])); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Departments Tab -->
                        <div class="tab-pane fade" id="departments" role="tabpanel">
                            <div class="row">
                                <?php if (!empty($showcase['departments'])): ?>
                                    <?php foreach ($showcase['departments'] as $dept): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h6 class="card-title"><?= esc($dept['department_name']); ?></h6>
                                                    <p class="card-text"><?= esc(substr($dept['department_description'] ?? '', 0, 100)); ?>...</p>
                                                    <?php if (!empty($dept['head_name'])): ?>
                                                        <small class="text-muted">Head: <?= esc($dept['head_name']); ?></small>
                                                    <?php endif; ?>
                                                    <br>
                                                    <a href="<?= base_url('/department/' . $dept['id'] . '/showcase'); ?>" class="btn btn-sm btn-primary mt-2">View Department</a>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <p class="text-muted">No departments found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Faculty Members Tab -->
                        <div class="tab-pane fade" id="faculty-members" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Designation</th>
                                            <th>Department</th>
                                            <th>Qualification</th>
                                            <th>Contact</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($showcase['faculty_members'])): ?>
                                            <?php foreach ($showcase['faculty_members'] as $member): ?>
                                                <tr>
                                                    <td><?= esc($member['full_name']); ?></td>
                                                    <td><span class="badge badge-primary"><?= esc($member['designation']); ?></span></td>
                                                    <td><?= esc($member['department_name']); ?></td>
                                                    <td><span class="badge badge-secondary"><?= esc($member['highest_qualification']); ?></span></td>
                                                    <td><?= esc($member['email']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No faculty members found.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- PhD Awardees Tab -->
                        <div class="tab-pane fade" id="phd-awardees" role="tabpanel">
                            <div class="row">
                                <?php if (!empty($showcase['phd_awardees'])): ?>
                                    <?php foreach ($showcase['phd_awardees'] as $phd): ?>
                                        <div class="col-md-4 mb-3">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h6 class="card-title"><?= esc($phd['full_name']); ?></h6>
                                                    <p class="card-text">
                                                        <strong><?= esc($phd['designation']); ?></strong><br>
                                                        <small class="text-muted"><?= esc($phd['department_name']); ?></small><br>
                                                        <small>PhD in <?= esc($phd['discipline']); ?> (<?= esc($phd['year_completion']); ?>)</small><br>
                                                        <small><?= esc($phd['university_name']); ?></small>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <p class="text-muted">No PhD awardees found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Students Tab -->
                        <div class="tab-pane fade" id="students" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Student ID</th>
                                            <th>Department</th>
                                            <th>Program</th>
                                            <th>Admission Year</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($showcase['current_students'])): ?>
                                            <?php foreach ($showcase['current_students'] as $student): ?>
                                                <tr>
                                                    <td><?= esc($student['full_name']); ?></td>
                                                    <td><?= esc($student['student_id']); ?></td>
                                                    <td><?= esc($student['department_name']); ?></td>
                                                    <td><span class="badge badge-info"><?= esc($student['program_type']); ?></span></td>
                                                    <td><?= esc($student['admission_year']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No current students found.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Alumni Tab -->
                        <div class="tab-pane fade" id="alumni" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Student ID</th>
                                            <th>Department</th>
                                            <th>Program</th>
                                            <th>Graduation Year</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($showcase['alumni'])): ?>
                                            <?php foreach ($showcase['alumni'] as $alumnus): ?>
                                                <tr>
                                                    <td><?= esc($alumnus['full_name']); ?></td>
                                                    <td><?= esc($alumnus['student_id']); ?></td>
                                                    <td><?= esc($alumnus['department_name']); ?></td>
                                                    <td><span class="badge badge-success"><?= esc($alumnus['program_type']); ?></span></td>
                                                    <td><?= esc($alumnus['actual_completion_year']); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center text-muted">No alumni found.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- News & Events Tab -->
                        <div class="tab-pane fade" id="news" role="tabpanel">
                            <div class="row">
                                <?php if (!empty($showcase['news_events'])): ?>
                                    <?php foreach ($showcase['news_events'] as $news): ?>
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-body">
                                                    <h6 class="card-title"><?= esc($news['title']); ?></h6>
                                                    <p class="card-text"><?= esc($news['description']); ?></p>
                                                    <small class="text-muted">
                                                        <?= esc($news['department_name']); ?> • 
                                                        <?= date('M d, Y', strtotime($news['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <p class="text-muted">No news and events found.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--End Faculty Showcase Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
// Initialize Bootstrap tabs
$('#facultyTabs a').on('click', function (e) {
    e.preventDefault();
    $(this).tab('show');
});
</script>

<?= $this->endSection(); ?>