<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Manage Faculties</h4>
                    <hr>
                    <div class="row gy-3">
                        <div class="col-md-2 text-end d-grid">
                            <a href="<?= base_url('/dashboard/createFaculty'); ?>" class="btn btn-primary">
                                <i class="zmdi zmdi-plus-circle"></i> Add Faculty
                            </a>
                        </div>
                    </div>

                    <div class="table-responsive mt-4">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Faculty Name</th>
                                    <th scope="col">Description</th>
                                    <th scope="col">Dean</th>
                                    <th scope="col">Departments</th>
                                    <th scope="col">Created</th>
                                    <th scope="col">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($faculties)): ?>
                                    <?php $i = 1; ?>
                                    <?php foreach ($faculties as $faculty): ?>
                                        <tr>
                                            <th scope="row"><?= $i++; ?></th>
                                            <td>
                                                <strong><?= esc($faculty['faculty_name']); ?></strong>
                                            </td>
                                            <td>
                                                <?= esc(substr($faculty['faculty_description'] ?? '', 0, 100)); ?>
                                                <?= strlen($faculty['faculty_description'] ?? '') > 100 ? '...' : ''; ?>
                                            </td>
                                            <td>
                                                <?php if ($faculty['dean_id']): ?>
                                                    <span class="badge badge-success">
                                                        <i class="zmdi zmdi-account"></i> Assigned
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge badge-warning">
                                                        <i class="zmdi zmdi-account-o"></i> Not Assigned
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge badge-info">
                                                    <i class="zmdi zmdi-city"></i> View Departments
                                                </span>
                                            </td>
                                            <td>
                                                <?= date('M d, Y', strtotime($faculty['created_at'])); ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url('/dashboard/editFaculty/' . $faculty['id']); ?>" 
                                                       class="btn btn-sm btn-primary" title="Edit Faculty">
                                                        <i class="zmdi zmdi-edit"></i>
                                                    </a>
                                                    <?php if (!$faculty['dean_id']): ?>
                                                        <button class="btn btn-sm btn-success" 
                                                                onclick="assignDean(<?= $faculty['id']; ?>)" 
                                                                title="Assign Dean">
                                                            <i class="zmdi zmdi-account-add"></i>
                                                        </button>
                                                    <?php else: ?>
                                                        <button class="btn btn-sm btn-warning" 
                                                                onclick="changeDean(<?= $faculty['id']; ?>)" 
                                                                title="Change Dean">
                                                            <i class="zmdi zmdi-swap"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    <a href="<?= base_url('/faculty/' . $faculty['id'] . '/showcase'); ?>" 
                                                       class="btn btn-sm btn-info" title="View Showcase" target="_blank">
                                                        <i class="zmdi zmdi-eye"></i>
                                                    </a>
                                                    <button class="btn btn-sm btn-danger" 
                                                            onclick="deleteFaculty(<?= $faculty['id']; ?>)" 
                                                            title="Delete Faculty">
                                                        <i class="zmdi zmdi-delete"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="py-4">
                                                <i class="zmdi zmdi-graduation-cap" style="font-size: 48px; color: #ccc;"></i>
                                                <p class="mt-2 text-muted">No faculties found. <a href="<?= base_url('/dashboard/createFaculty'); ?>">Create your first faculty</a></p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!-- Dean Assignment Modal -->
<div class="modal fade" id="deanModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deanModalTitle">Assign Dean</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="deanForm">
                    <input type="hidden" id="facultyId" name="faculty_id">
                    <div class="form-group">
                        <label for="deanSelect">Select Dean</label>
                        <select class="form-control" id="deanSelect" name="dean_id" required>
                            <option value="">Choose a faculty member...</option>
                        </select>
                        <small class="form-text text-muted">Only faculty members from this faculty's departments are shown.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveDean()">Save</button>
            </div>
        </div>
    </div>
</div>

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
function assignDean(facultyId) {
    $('#facultyId').val(facultyId);
    $('#deanModalTitle').text('Assign Dean');
    loadAvailableDeans(facultyId);
    $('#deanModal').modal('show');
}

function changeDean(facultyId) {
    $('#facultyId').val(facultyId);
    $('#deanModalTitle').text('Change Dean');
    loadAvailableDeans(facultyId);
    $('#deanModal').modal('show');
}

function loadAvailableDeans(facultyId) {
    $.get(`<?= base_url(); ?>/api/faculties/${facultyId}/available-deans`, function(response) {
        const select = $('#deanSelect');
        select.empty().append('<option value="">Choose a faculty member...</option>');
        
        if (response.status === 'success' && response.data.length > 0) {
            response.data.forEach(function(dean) {
                select.append(`<option value="${dean.record_id}">${dean.full_name} (${dean.designation}) - ${dean.department_name}</option>`);
            });
        } else {
            select.append('<option value="" disabled>No eligible faculty members found</option>');
        }
    }).fail(function() {
        alert('Failed to load available deans');
    });
}

function saveDean() {
    const facultyId = $('#facultyId').val();
    const deanId = $('#deanSelect').val();
    
    if (!deanId) {
        alert('Please select a dean');
        return;
    }
    
    $.ajax({
        url: `<?= base_url(); ?>/api/faculties/${facultyId}/update-dean`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ dean_id: deanId }),
        success: function(response) {
            if (response.status === 'success') {
                $('#deanModal').modal('hide');
                location.reload();
            } else {
                alert('Failed to assign dean: ' + response.message);
            }
        },
        error: function() {
            alert('Failed to assign dean');
        }
    });
}

function deleteFaculty(facultyId) {
    if (confirm('Are you sure you want to delete this faculty? This action cannot be undone.')) {
        $.ajax({
            url: `<?= base_url(); ?>/api/faculties/${facultyId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Failed to delete faculty: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to delete faculty: ' + response.message);
            }
        });
    }
}
</script>

<?= $this->endSection(); ?>