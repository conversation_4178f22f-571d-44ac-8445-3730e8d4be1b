<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-8 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="zmdi zmdi-edit"></i> Edit Faculty: <?= esc($faculty['faculty_name']); ?>
                        </h5>
                        <hr>
                        <form id="editFacultyForm">
                            <input type="hidden" id="faculty_id" value="<?= $faculty['id']; ?>">
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="faculty_name">Faculty Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="faculty_name" name="faculty_name" 
                                               value="<?= esc($faculty['faculty_name']); ?>" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="faculty_description">Faculty Description</label>
                                <textarea class="form-control" id="faculty_description" name="faculty_description" 
                                          rows="4"><?= esc($faculty['faculty_description'] ?? ''); ?></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="faculty_image">Faculty Image</label>
                                        <input type="file" class="form-control-file" id="faculty_image" 
                                               name="faculty_image" accept="image/*">
                                        <small class="form-text text-muted">Leave empty to keep current image</small>
                                        <?php if (!empty($faculty['faculty_image'])): ?>
                                            <div class="mt-2">
                                                <img src="<?= base_url('uploads/faculties/' . $faculty['faculty_image']); ?>" 
                                                     alt="Current Image" style="max-width: 200px; max-height: 200px;" />
                                            </div>
                                        <?php endif; ?>
                                        <div class="mt-2">
                                            <img id="faculty_image_preview" src="#" alt="New Image Preview" 
                                                 style="display:none; max-width: 200px; max-height: 200px;" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="dean_select">Current Dean</label>
                                        <?php if (!empty($faculty['dean_name'])): ?>
                                            <div class="alert alert-success">
                                                <i class="zmdi zmdi-account"></i> 
                                                <strong><?= esc($faculty['dean_name']); ?></strong>
                                                <br><small><?= esc($faculty['dean_designation'] ?? ''); ?></small>
                                                <br>
                                                <button type="button" class="btn btn-sm btn-warning mt-2" onclick="changeDean()">
                                                    <i class="zmdi zmdi-swap"></i> Change Dean
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removeDean()">
                                                    <i class="zmdi zmdi-close"></i> Remove Dean
                                                </button>
                                            </div>
                                        <?php else: ?>
                                            <div class="alert alert-warning">
                                                <i class="zmdi zmdi-account-o"></i> No dean assigned
                                                <br>
                                                <button type="button" class="btn btn-sm btn-success mt-2" onclick="assignDean()">
                                                    <i class="zmdi zmdi-account-add"></i> Assign Dean
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Showcase Information</h6>

                            <div class="form-group">
                                <label for="profile">Faculty Profile</label>
                                <textarea class="form-control" id="profile" name="profile" rows="3"><?= esc($faculty['profile'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="programmes_curriculum">Programmes & Curriculum</label>
                                <textarea class="form-control" id="programmes_curriculum" name="programmes_curriculum" 
                                          rows="3"><?= esc($faculty['programmes_curriculum'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="research_groups">Research Groups</label>
                                <textarea class="form-control" id="research_groups" name="research_groups" 
                                          rows="3"><?= esc($faculty['research_groups'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="contact_info">Contact Information</label>
                                <textarea class="form-control" id="contact_info" name="contact_info" 
                                          rows="3"><?= esc($faculty['contact_info'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="zmdi zmdi-check"></i> Update Faculty
                                </button>
                                <a href="<?= base_url('/dashboard/manageFaculties'); ?>" class="btn btn-secondary btn-lg ml-2">
                                    <i class="zmdi zmdi-arrow-left"></i> Back to List
                                </a>
                                <a href="<?= base_url('/faculty/' . $faculty['id'] . '/showcase'); ?>" 
                                   class="btn btn-info btn-lg ml-2" target="_blank">
                                    <i class="zmdi zmdi-eye"></i> View Showcase
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!-- Dean Assignment Modal -->
<div class="modal fade" id="deanModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deanModalTitle">Assign Dean</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="deanForm">
                    <div class="form-group">
                        <label for="deanSelect">Select Dean</label>
                        <select class="form-control" id="deanSelect" name="dean_id" required>
                            <option value="">Choose a faculty member...</option>
                        </select>
                        <small class="form-text text-muted">Only faculty members from this faculty's departments are shown.</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveDean()">Save</button>
            </div>
        </div>
    </div>
</div>

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
const facultyId = <?= $faculty['id']; ?>;

// Image preview functionality
document.getElementById('faculty_image').addEventListener('change', function(event) {
    const [file] = event.target.files;
    const preview = document.getElementById('faculty_image_preview');
    if (file) {
        preview.src = URL.createObjectURL(file);
        preview.style.display = 'block';
    } else {
        preview.src = '#';
        preview.style.display = 'none';
    }
});

// Form submission
$('#editFacultyForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const jsonData = {};
    
    // Convert FormData to JSON (excluding file)
    for (let [key, value] of formData.entries()) {
        if (key !== 'faculty_image') {
            jsonData[key] = value;
        }
    }
    
    $.ajax({
        url: `<?= base_url(); ?>/api/faculties/${facultyId}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(jsonData),
        success: function(response) {
            if (response.status === 'success') {
                alert('Faculty updated successfully!');
                location.reload();
            } else {
                alert('Failed to update faculty: ' + response.message);
            }
        },
        error: function(xhr) {
            try {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to update faculty: ' + response.message);
            } catch (e) {
                alert('Failed to update faculty. Please try again.');
            }
        }
    });
});

function assignDean() {
    $('#deanModalTitle').text('Assign Dean');
    loadAvailableDeans();
    $('#deanModal').modal('show');
}

function changeDean() {
    $('#deanModalTitle').text('Change Dean');
    loadAvailableDeans();
    $('#deanModal').modal('show');
}

function removeDean() {
    if (confirm('Are you sure you want to remove the current dean?')) {
        $.ajax({
            url: `<?= base_url(); ?>/api/faculties/${facultyId}/remove-dean`,
            method: 'POST',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Failed to remove dean: ' + response.message);
                }
            },
            error: function() {
                alert('Failed to remove dean');
            }
        });
    }
}

function loadAvailableDeans() {
    $.get(`<?= base_url(); ?>/api/faculties/${facultyId}/available-deans`, function(response) {
        const select = $('#deanSelect');
        select.empty().append('<option value="">Choose a faculty member...</option>');
        
        if (response.status === 'success' && response.data.length > 0) {
            response.data.forEach(function(dean) {
                select.append(`<option value="${dean.record_id}">${dean.full_name} (${dean.designation}) - ${dean.department_name}</option>`);
            });
        } else {
            select.append('<option value="" disabled>No eligible faculty members found</option>');
        }
    }).fail(function() {
        alert('Failed to load available deans');
    });
}

function saveDean() {
    const deanId = $('#deanSelect').val();
    
    if (!deanId) {
        alert('Please select a dean');
        return;
    }
    
    $.ajax({
        url: `<?= base_url(); ?>/api/faculties/${facultyId}/update-dean`,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({ dean_id: deanId }),
        success: function(response) {
            if (response.status === 'success') {
                $('#deanModal').modal('hide');
                location.reload();
            } else {
                alert('Failed to assign dean: ' + response.message);
            }
        },
        error: function() {
            alert('Failed to assign dean');
        }
    });
}
</script>

<?= $this->endSection(); ?>