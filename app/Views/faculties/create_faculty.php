<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-8 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="zmdi zmdi-graduation-cap"></i> Create New Faculty
                        </h5>
                        <hr>
                        <form id="createFacultyForm">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="faculty_name">Faculty Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="faculty_name" name="faculty_name" 
                                               placeholder="e.g., Faculty of Science" required>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="faculty_description">Faculty Description</label>
                                <textarea class="form-control" id="faculty_description" name="faculty_description" 
                                          rows="4" placeholder="Brief description of the faculty..."></textarea>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="faculty_image">Faculty Image</label>
                                        <input type="file" class="form-control-file" id="faculty_image" 
                                               name="faculty_image" accept="image/*">
                                        <small class="form-text text-muted">Optional: Upload a representative image for the faculty</small>
                                        <div class="mt-2">
                                            <img id="faculty_image_preview" src="#" alt="Image Preview" 
                                                 style="display:none; max-width: 200px; max-height: 200px;" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Showcase Information (Optional)</h6>
                            <p class="text-muted small">This information will be displayed on the faculty's public page</p>

                            <div class="form-group">
                                <label for="profile">Faculty Profile</label>
                                <textarea class="form-control" id="profile" name="profile" rows="3" 
                                          placeholder="Overview and mission of the faculty..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="programmes_curriculum">Programmes & Curriculum</label>
                                <textarea class="form-control" id="programmes_curriculum" name="programmes_curriculum" 
                                          rows="3" placeholder="Information about academic programs offered..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="research_groups">Research Groups</label>
                                <textarea class="form-control" id="research_groups" name="research_groups" rows="3" 
                                          placeholder="Active research groups and areas..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="contact_info">Contact Information</label>
                                <textarea class="form-control" id="contact_info" name="contact_info" rows="3" 
                                          placeholder="Office address, phone, email, etc..."></textarea>
                            </div>

                            <div class="alert alert-info">
                                <i class="zmdi zmdi-info-outline"></i>
                                <strong>Note:</strong> Dean assignment is optional during faculty creation. 
                                You can assign a dean later after adding departments and faculty members.
                            </div>

                            <div class="form-group text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="zmdi zmdi-check"></i> Create Faculty
                                </button>
                                <a href="<?= base_url('/dashboard/manageFaculties'); ?>" class="btn btn-secondary btn-lg ml-2">
                                    <i class="zmdi zmdi-close"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
// Image preview functionality
document.getElementById('faculty_image').addEventListener('change', function(event) {
    const [file] = event.target.files;
    const preview = document.getElementById('faculty_image_preview');
    if (file) {
        preview.src = URL.createObjectURL(file);
        preview.style.display = 'block';
    } else {
        preview.src = '#';
        preview.style.display = 'none';
    }
});

// Form submission
$('#createFacultyForm').on('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const jsonData = {};
    
    // Convert FormData to JSON (excluding file)
    for (let [key, value] of formData.entries()) {
        if (key !== 'faculty_image') {
            jsonData[key] = value;
        }
    }
    
    // Add created_by if available
    const userId = '<?= session()->get("id") ?? "1"; ?>';
    if (userId) {
        jsonData.created_by = userId;
    }
    
    $.ajax({
        url: '<?= base_url(); ?>/api/faculties',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(jsonData),
        success: function(response) {
            if (response.status === 'success') {
                // Show success message
                alert('Faculty created successfully!');
                
                // Redirect to manage faculties page
                window.location.href = '<?= base_url("/dashboard/manageFaculties"); ?>';
            } else {
                alert('Failed to create faculty: ' + response.message);
            }
        },
        error: function(xhr) {
            try {
                const response = JSON.parse(xhr.responseText);
                alert('Failed to create faculty: ' + response.message);
            } catch (e) {
                alert('Failed to create faculty. Please try again.');
            }
        }
    });
});
</script>

<?= $this->endSection(); ?>