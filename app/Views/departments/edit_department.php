<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-8 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Edit Department: <?= esc($department['department_name']); ?></h5>
                        <form id="editDepartmentForm">
                            <input type="hidden" id="department_id" name="department_id" value="<?= esc($department['id']); ?>">
                            
                            <div class="form-group">
                                <label for="faculty_id">Faculty <span class="text-danger">*</span></label>
                                <select class="form-control" id="faculty_id" name="faculty_id" required>
                                    <option value="">Select Faculty...</option>
                                    <?php if (isset($faculties)): ?>
                                        <?php foreach ($faculties as $faculty): ?>
                                            <option value="<?= esc($faculty['id']); ?>" 
                                                    <?= $faculty['id'] == $department['faculty_id'] ? 'selected' : ''; ?>>
                                                <?= esc($faculty['faculty_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="department_name">Department Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department_name" name="department_name" 
                                       value="<?= esc($department['department_name']); ?>" 
                                       placeholder="e.g., Department of Computer Science" required>
                            </div>

                            <div class="form-group">
                                <label for="department_description">Department Description</label>
                                <textarea class="form-control" id="department_description" name="department_description" 
                                          rows="3" placeholder="Brief description of the department..."><?= esc($department['department_description'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="department_image">Department Image (Optional)</label>
                                <input type="file" class="form-control-file" id="department_image" name="department_image" accept="image/*">
                                <div class="mt-2">
                                    <?php if (!empty($department['department_image'])): ?>
                                        <img id="current_image" src="<?= base_url('uploads/departments/' . $department['department_image']); ?>" 
                                             alt="Current Image" style="max-width: 200px; max-height: 200px;" />
                                        <p class="text-muted mt-1">Current image</p>
                                    <?php endif; ?>
                                    <img id="department_image_preview" src="#" alt="New Image Preview" 
                                         style="display:none; max-width: 200px; max-height: 200px;" />
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Showcase Information (Optional)</h6>
                            
                            <div class="form-group">
                                <label for="profile">Department Profile</label>
                                <textarea class="form-control" id="profile" name="profile" rows="3" 
                                          placeholder="Overview and mission of the department..."><?= esc($department['profile'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="programmes_curriculum">Programmes & Curriculum</label>
                                <textarea class="form-control" id="programmes_curriculum" name="programmes_curriculum" 
                                          rows="3" placeholder="Academic programs and curriculum details..."><?= esc($department['programmes_curriculum'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="research_groups">Research Groups</label>
                                <textarea class="form-control" id="research_groups" name="research_groups" rows="3" 
                                          placeholder="Research areas and groups..."><?= esc($department['research_groups'] ?? ''); ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="contact_info">Contact Information</label>
                                <textarea class="form-control" id="contact_info" name="contact_info" rows="3" 
                                          placeholder="Office address, phone, email, etc..."><?= esc($department['contact_info'] ?? ''); ?></textarea>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-account-circle"></i> Head of Department</h6>
                            
                            <?php if (!empty($department['head_name'])): ?>
                                <div class="alert alert-info">
                                    <strong>Current Head:</strong> <?= esc($department['head_name']); ?>
                                    <?php if (!empty($department['head_designation'])): ?>
                                        (<?= esc($department['head_designation']); ?>)
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="head_of_department_id">Assign Head of Department (Optional)</label>
                                <select class="form-control" id="head_of_department_id" name="head_of_department_id">
                                    <option value="">No Head Assigned</option>
                                    <?php if (isset($availableHeads) && !empty($availableHeads)): ?>
                                        <?php foreach ($availableHeads as $head): ?>
                                            <option value="<?= esc($head['record_id']); ?>" 
                                                    <?= $head['record_id'] == ($department['head_of_department_id'] ?? '') ? 'selected' : ''; ?>>
                                                <?= esc($head['full_name']); ?> - <?= esc($head['designation']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <option value="" disabled>No faculty members available in this department</option>
                                    <?php endif; ?>
                                </select>
                                <small class="form-text text-muted">
                                    Only faculty members from this department can be assigned as head.
                                </small>
                            </div>

                            <div class="form-group mt-4">
                                <button type="submit" class="btn btn-primary">
                                    <i class="zmdi zmdi-check"></i> Update Department
                                </button>
                                <a href="<?= base_url('/dashboard/listDepartments'); ?>" class="btn btn-secondary ml-2">
                                    <i class="zmdi zmdi-arrow-left"></i> Back to List
                                </a>
                                <a href="<?= base_url('/department/' . $department['id'] . '/showcase'); ?>" 
                                   class="btn btn-info ml-2" target="_blank">
                                    <i class="zmdi zmdi-eye"></i> View Showcase
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->

</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>

<script>
    document.getElementById('department_image').addEventListener('change', function(event) {
        const [file] = event.target.files;
        const preview = document.getElementById('department_image_preview');
        const currentImage = document.getElementById('current_image');
        
        if (file) {
            preview.src = URL.createObjectURL(file);
            preview.style.display = 'block';
            if (currentImage) {
                currentImage.style.display = 'none';
            }
        } else {
            preview.src = '#';
            preview.style.display = 'none';
            if (currentImage) {
                currentImage.style.display = 'block';
            }
        }
    });
</script>

<!-- Custom scripts for updating the form -->
<script>
    $('#editDepartmentForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const jsonData = {};
        const departmentId = $('#department_id').val();
        
        // Convert FormData to JSON (excluding file)
        for (let [key, value] of formData.entries()) {
            if (key !== 'department_image' && key !== 'department_id') {
                jsonData[key] = value;
            }
        }
        
        $.ajax({
            url: '<?= base_url(); ?>/api/departments/' + departmentId,
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(jsonData),
            success: function(response) {
                if (response.status === 'success') {
                    alert('Department updated successfully!');
                    setTimeout(function() {
                        window.location.href = '<?= base_url("/dashboard/listDepartments"); ?>';
                    }, 1000);
                } else {
                    alert('Failed to update department: ' + response.message);
                }
            },
            error: function(xhr) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    alert('Failed to update department: ' + response.message);
                } catch (e) {
                    alert('Failed to update department. Please try again.');
                }
            }
        });
    });

    // Update available heads when faculty changes
    $('#faculty_id').on('change', function() {
        const departmentId = $('#department_id').val();
        const headSelect = $('#head_of_department_id');
        
        // Reset head selection
        headSelect.html('<option value="">Loading...</option>');
        
        // Fetch available heads for this department
        $.ajax({
            url: '<?= base_url(); ?>/api/departments/' + departmentId + '/available-heads',
            method: 'GET',
            success: function(response) {
                if (response.status === 'success') {
                    headSelect.html('<option value="">No Head Assigned</option>');
                    response.data.forEach(function(head) {
                        headSelect.append(
                            '<option value="' + head.record_id + '">' + 
                            head.full_name + ' - ' + head.designation + 
                            '</option>'
                        );
                    });
                } else {
                    headSelect.html('<option value="">No faculty members available</option>');
                }
            },
            error: function() {
                headSelect.html('<option value="">Error loading heads</option>');
            }
        });
    });
</script>

<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>