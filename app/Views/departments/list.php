<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="modal fade" id="editCellModal" tabindex="-1" role="dialog" aria-labelledby="editCellModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-sm modal-dialog-slideout modal-dialog-bottom" role="document" style="max-width: 40rem;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCellModalLabel">Edit Department</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="editCellForm">
                        
                    </form>
                </div>
                <!-- <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>


                </div> -->
            </div>
        </div>
    </div>
    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

        <!-- Custom Confirmation Modal -->
<div id="deleteModal" class="modal fade" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div style="position: relative;display: -ms-flexbox;display: flex;-ms-flex-direction: column;flex-direction: column;width: 100%;pointer-events: auto;background-color: #4e4e4e33;background-clip: padding-box;border-radius: 1.3rem;backdrop-filter: blur(2rem);box-shadow: (0,0,0,0.3);box-shadow: 2px 4px 16px #000000d9;">
            <div class="modal-header" style="display: -ms-flexbox;display: flex;-ms-flex-align: start;align-items: flex-start;-ms-flex-pack: justify;justify-content: space-between;padding: 1rem 1rem;border-bottom: 1px solid #dee2e628;border-top-left-radius: .3rem;border-top-right-radius: .3rem;">
                <h5 class="modal-title">Confirm Delete</h5>
                <button style="color: #fff;" type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 3rem;"></i>
                    <h4>Are you sure?</h4>
                    <p class="text-muted">Are you sure you want to delete this department? This action cannot be undone.</p>
                </div>
            </div>
            <div class="modal-footer justify-content-center" style="display: -ms-flexbox;display: flex;-ms-flex-align: start;align-items: flex-start;-ms-flex-pack: justify;justify-content: space-between;padding: 1rem 1rem;border-top: 1px solid #dee2e628;border-top-left-radius: .3rem;border-top-right-radius: .3rem;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Yes, Delete</button>
            </div>
        </div>
    </div>
</div>

            <!-- Edit Cell Modal -->



            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 class="card-title mb-0">Departments List</h5>
                            <a href="<?= base_url('/dashboard/addDepartments'); ?>" class="btn btn-primary">
                                <i class="zmdi zmdi-plus"></i> Add Department
                            </a>
                        </div>
                        
                        <!-- Search Results Alert -->
                        <?php if (!empty($searchTerm)): ?>
                            <div class="alert alert-info">
                                <i class="zmdi zmdi-search"></i> 
                                Showing search results for: <strong>"<?= esc($searchTerm) ?>"</strong>
                                <a href="<?= base_url('dashboard/manageDepartments') ?>" class="btn btn-sm btn-outline-secondary ml-2">
                                    <i class="zmdi zmdi-close"></i> Clear Search
                                </a>
                            </div>
                        <?php endif; ?>

                        <!-- Search Bar -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <form method="GET" action="<?= base_url('dashboard/manageDepartments') ?>">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" 
                                               placeholder="Search departments..." value="<?= esc($searchTerm ?? '') ?>">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="submit">
                                                <i class="zmdi zmdi-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table" id="listRow">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Department Name</th>
                                        <th scope="col">Faculty</th>
                                        <th scope="col">Head of Department</th>
                                        <th scope="col">Description</th>
                                        <th scope="col">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($departments)): ?>
                                        <?php $i = 1; ?>
                                        <?php foreach ($departments as $dept): ?>
                                            <tr>
                                                <th scope="row"><?= $i++; ?></th>
                                                <td>
                                                    <strong><?= esc($dept['department_name']); ?></strong>
                                                </td>
                                                <td>
                                                    <span class="badge badge-primary">
                                                        <?= esc($dept['faculty_name'] ?? 'No Faculty'); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php if (!empty($dept['head_name'])): ?>
                                                        <span class="badge badge-success">
                                                            <i class="zmdi zmdi-account"></i> <?= esc($dept['head_name']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge badge-warning">
                                                            <i class="zmdi zmdi-account-o"></i> Not Assigned
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?= esc(substr($dept['department_description'] ?? '', 0, 80)); ?>
                                                    <?= strlen($dept['department_description'] ?? '') > 80 ? '...' : ''; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="<?= base_url('/dashboard/editDepartment/' . $dept['id']); ?>" 
                                                           class="btn btn-sm btn-primary" title="Edit Department">
                                                            <i class="zmdi zmdi-edit"></i>
                                                        </a>
                                                        <?php if (empty($dept['head_name'])): ?>
                                                            <button class="btn btn-sm btn-success" 
                                                                    onclick="assignHead(<?= $dept['id']; ?>)" 
                                                                    title="Assign Head">
                                                                <i class="zmdi zmdi-account-add"></i>
                                                            </button>
                                                        <?php else: ?>
                                                            <button class="btn btn-sm btn-warning" 
                                                                    onclick="changeHead(<?= $dept['id']; ?>)" 
                                                                    title="Change Head">
                                                                <i class="zmdi zmdi-swap"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                        <a href="<?= base_url('/department/' . $dept['id'] . '/showcase'); ?>" 
                                                           class="btn btn-sm btn-info" title="View Showcase" target="_blank">
                                                            <i class="zmdi zmdi-eye"></i>
                                                        </a>
                                                        <button class="btn btn-sm btn-danger" 
                                                                onclick="deleteDepartment(<?= $dept['id']; ?>)" 
                                                                title="Delete Department">
                                                            <i class="zmdi zmdi-delete"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <div class="py-4">
                                                    <i class="zmdi zmdi-city" style="font-size: 48px; color: #ccc;"></i>
                                                    <p class="mt-2 text-muted">No departments found. <a href="<?= base_url('/dashboard/addDepartments'); ?>">Create your first department</a></p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if (isset($pagination) && $pagination['total_pages'] > 1): ?>
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <nav aria-label="Departments pagination">
                                        <ul class="pagination justify-content-center">
                                            <!-- Previous Page -->
                                            <?php if ($pagination['current_page'] > 1): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= base_url('dashboard/manageDepartments') ?>?page=<?= $pagination['current_page'] - 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                        Previous
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                            
                                            <!-- Page Numbers -->
                                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                                <li class="page-item <?= $i === $pagination['current_page'] ? 'active' : '' ?>">
                                                    <a class="page-link" href="<?= base_url('dashboard/manageDepartments') ?>?page=<?= $i ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                        <?= $i ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>
                                            
                                            <!-- Next Page -->
                                            <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="<?= base_url('dashboard/manageDepartments') ?>?page=<?= $pagination['current_page'] + 1 ?><?= !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : '' ?>">
                                                        Next
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                    
                                    <div class="text-center text-muted">
                                        Showing <?= count($departments) ?> of <?= $pagination['total'] ?> departments
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Edit Cell Form -->




                <!--End Dashboard Content-->

                <!--start overlay-->
                <div class="overlay toggle-menu"></div>
                <!--end overlay-->

            </div>
            <!-- End container-fluid-->

        </div><!--End content-wrapper-->
        <!--Start Back To Top Button-->
        <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
        <!--End Back To Top Button-->

        <!--Start footer-->
        <footer class="footer">
            <div class="container">
                <div class="text-center">
                    Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
                </div>
            </div>
        </footer>
        <!--End footer-->


    </div><!--End wrapper-->

    <!-- Head Assignment Modal -->
    <div class="modal fade" id="headModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="headModalTitle">Assign Head of Department</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="headForm">
                        <input type="hidden" id="departmentId" name="department_id">
                        <div class="form-group">
                            <label for="headSelect">Select Head of Department</label>
                            <select class="form-control" id="headSelect" name="head_id" required>
                                <option value="">Choose a faculty member...</option>
                            </select>
                            <small class="form-text text-muted">Only faculty members from this department are shown.</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveHead()">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!--start color switcher-->
    <?= $this->include('partials/colorswitcher'); ?>
    <!--end color switcher-->

    <?= $this->endSection(); ?>

    <?= $this->section('scripts'); ?>
    <!-- Chart.js -->
    <script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
    <script src="<?= base_url('assets/js/index.js'); ?>"></script>
    
    <script>
        function assignHead(departmentId) {
            $('#departmentId').val(departmentId);
            $('#headModalTitle').text('Assign Head of Department');
            loadAvailableHeads(departmentId);
            $('#headModal').modal('show');
        }

        function changeHead(departmentId) {
            $('#departmentId').val(departmentId);
            $('#headModalTitle').text('Change Head of Department');
            loadAvailableHeads(departmentId);
            $('#headModal').modal('show');
        }

        function loadAvailableHeads(departmentId) {
            $.get(`<?= base_url(); ?>/api/departments/${departmentId}/available-heads`, function(response) {
                const select = $('#headSelect');
                select.empty().append('<option value="">Choose a faculty member...</option>');
                
                if (response.status === 'success' && response.data.length > 0) {
                    response.data.forEach(function(head) {
                        select.append(`<option value="${head.record_id}">${head.full_name} (${head.designation})</option>`);
                    });
                } else {
                    select.append('<option value="" disabled>No eligible faculty members found</option>');
                }
            }).fail(function() {
                alert('Failed to load available heads');
            });
        }

        function saveHead() {
            const departmentId = $('#departmentId').val();
            const headId = $('#headSelect').val();
            
            if (!headId) {
                alert('Please select a head of department');
                return;
            }
            
            $.ajax({
                url: `<?= base_url(); ?>/api/departments/${departmentId}/update-head`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ head_id: headId }),
                success: function(response) {
                    if (response.status === 'success') {
                        $('#headModal').modal('hide');
                        location.reload();
                    } else {
                        alert('Failed to assign head: ' + response.message);
                    }
                },
                error: function() {
                    alert('Failed to assign head');
                }
            });
        }

        function deleteDepartmentt(departmentId) {
            if (confirm('Are you sure you want to delete this department? This action cannot be undone.')) {
                $.ajax({
                    url: `<?= base_url(); ?>/api/departments/${departmentId}`,
                    method: 'DELETE',
                    success: function(response) {
                        if (response.status === 'success') {
                            location.reload();
                        } else {
                            alert('Failed to delete department: ' + response.message);
                        }
                    },
                    error: function(xhr) {
                        const response = JSON.parse(xhr.responseText);
                        alert('Failed to delete department: ' + response.message);
                    }
                });
            }
        }

        function deleteDepartment(departmentId) {
    // Show the custom modal
    $('#deleteModal').modal('show');
    
    // Handle the confirm delete button click
    $('#confirmDeleteBtn').off('click').on('click', function() {
        // Hide the modal
        $('#deleteModal').modal('hide');
        
        // Perform the delete operation
        $.ajax({
            url: `<?= base_url(); ?>/api/departments/${departmentId}`,
            method: 'DELETE',
            success: function(response) {
                if (response.status === 'success') {
                    // Show success message (optional)
                    showSuccessMessage('Department deleted successfully!');
                    location.reload();
                } else {
                    showErrorMessage('Failed to delete department: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = JSON.parse(xhr.responseText);
                showErrorMessage('Failed to delete department: ' + response.message);
            }
        });
    });
}

        $('#editCellForm').on('submit', function(e) {
            e.preventDefault();
            const formData = $(this).serialize();

            ajaxPostData("<?= base_url(); ?>dashboard/ajaxEditDepartments", formData, function(res) {
                if (res.status === 'success') {
                    // Handle success
                }
            });
        });
    </script>
    <?= $this->endSection(); ?>