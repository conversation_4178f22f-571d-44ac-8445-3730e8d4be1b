<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->

            <div class="col-lg-6 offset-lg-2 mt-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Add Department</h5>
                        <form id="addDepartmentForm">
                            <div class="form-group">
                                <label for="faculty_id">Faculty <span class="text-danger">*</span></label>
                                <select class="form-control" id="faculty_id" name="faculty_id" required>
                                    <option value="">Select Faculty...</option>
                                    <?php if (isset($faculties)): ?>
                                        <?php foreach ($faculties as $faculty): ?>
                                            <option value="<?= esc($faculty['id']); ?>"><?= esc($faculty['faculty_name']); ?></option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="department_name">Department Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department_name" name="department_name" 
                                       placeholder="e.g., Department of Computer Science" required>
                            </div>

                            <div class="form-group">
                                <label for="department_description">Department Description</label>
                                <textarea class="form-control" id="department_description" name="department_description" 
                                          rows="3" placeholder="Brief description of the department..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="department_image">Department Image (Optional)</label>
                                <input type="file" class="form-control-file" id="department_image" name="department_image" accept="image/*">
                                <div class="mt-2">
                                    <img id="department_image_preview" src="#" alt="Image Preview" style="display:none; max-width: 200px; max-height: 200px;" />
                                </div>
                            </div>

                            <hr>
                            <h6><i class="zmdi zmdi-info"></i> Showcase Information (Optional)</h6>
                            
                            <div class="form-group">
                                <label for="profile">Department Profile</label>
                                <textarea class="form-control" id="profile" name="profile" rows="3" 
                                          placeholder="Overview and mission of the department..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="programmes_curriculum">Programmes & Curriculum</label>
                                <textarea class="form-control" id="programmes_curriculum" name="programmes_curriculum" 
                                          rows="3" placeholder="Academic programs and curriculum details..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="research_groups">Research Groups</label>
                                <textarea class="form-control" id="research_groups" name="research_groups" rows="3" 
                                          placeholder="Research areas and groups..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="contact_info">Contact Information</label>
                                <textarea class="form-control" id="contact_info" name="contact_info" rows="3" 
                                          placeholder="Office address, phone, email, etc..."></textarea>
                            </div>

                            <div class="alert alert-info">
                                <i class="zmdi zmdi-info-outline"></i>
                                <strong>Note:</strong> Head of department assignment is optional during creation. 
                                You can assign a head later after adding faculty members to this department.
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="zmdi zmdi-check"></i> Add Department
                            </button>
                        </form>
                    </div>
                </div>
            </div>



            <!--End Dashboard Content-->

            <!--start overlay-->
            <div class="overlay toggle-menu"></div>
            <!--end overlay-->

        </div>
        <!-- End container-fluid-->

    </div><!--End content-wrapper-->
    <!--Start Back To Top Button-->
    <a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
    <!--End Back To Top Button-->

    <!--Start footer-->
    <footer class="footer">
        <div class="container">
            <div class="text-center">
                Copyright © 2025 JBU (Autonomous) || Designed by Digitalpanda Axom 
            </div>
        </div>
    </footer>
    <!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>

<script>
    document.getElementById('department_image').addEventListener('change', function(event) {
        const [file] = event.target.files;
        const preview = document.getElementById('department_image_preview');
        if (file) {
            preview.src = URL.createObjectURL(file);
            preview.style.display = 'block';
        } else {
            preview.src = '#';
            preview.style.display = 'none';
        }
    });
</script>
<!-- custom scripts for saving the form -->
<script>
    $('#addDepartmentForm').on('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const jsonData = {};
        
        // Convert FormData to JSON (excluding file)
        for (let [key, value] of formData.entries()) {
            if (key !== 'department_image') {
                jsonData[key] = value;
            }
        }
        
        // Add created_by if available
        const userId = '<?= session()->get("id") ?? "1"; ?>';
        if (userId) {
            jsonData.created_by = userId;
        }
        
        $.ajax({
            url: '<?= base_url(); ?>/api/departments',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(jsonData),
            success: function(response) {
                if (response.status === 'success') {
                    alert('Department created successfully!');
                    $('#addDepartmentForm')[0].reset();
                    $('#department_image_preview').hide();
                    setTimeout(function() {
                        window.location.href = '<?= base_url("/dashboard/listDepartments"); ?>';
                    }, 1000);
                } else {
                    alert('Failed to create department: ' + response.message);
                }
            },
            error: function(xhr) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    alert('Failed to create department: ' + response.message);
                } catch (e) {
                    alert('Failed to create department. Please try again.');
                }
            }
        });
    });
</script>
<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>
<?= $this->endSection(); ?>