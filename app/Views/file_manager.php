<?php
/**
 * PHP File Upload Script - Traditional Form Submission
 *
 * This script allows users to upload a single file using a standard HTML form submission.
 * The uploaded file will be saved in the same directory where this PHP script is located.
 *
 * After upload, the page will fully reload, displaying a success or error message.
 *
 * For local testing, ensure your web server (e.g., Apache, Nginx with PHP-FPM)
 * has write permissions for the directory where you place this file.
 */

$message = ''; // To store messages for the user

// Check if a file was submitted via POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['uploaded_file'])) {
    $file = $_FILES['uploaded_file'];

    // Basic file information
    $fileName = $file['name'];
    $fileTmpName = $file['tmp_name'];
    $fileSize = $file['size'];
    $fileError = $file['error'];
    $fileType = $file['type'];

    // Define allowed file extensions (basic example, be more specific in production)
    $allowedExtensions = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip');

    // Get the file extension
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    // --- Validation Checks ---

    // 1. Check for upload errors
    if ($fileError !== UPLOAD_ERR_OK) {
        switch ($fileError) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                $message = '<p style="color: red;">Error: File is too large. Max size allowed by server is ' . ini_get('upload_max_filesize') . '.</p>';
                break;
            case UPLOAD_ERR_PARTIAL:
                $message = '<p style="color: red;">Error: The uploaded file was only partially uploaded.</p>';
                break;
            case UPLOAD_ERR_NO_FILE:
                $message = '<p style="color: red;">Error: No file was uploaded.</p>';
                break;
            case UPLOAD_ERR_NO_TMP_DIR:
                $message = '<p style="color: red;">Error: Missing a temporary folder.</p>';
                break;
            case UPLOAD_ERR_CANT_WRITE:
                $message = '<p style="color: red;">Error: Failed to write file to disk. Check folder permissions.</p>';
                break;
            case UPLOAD_ERR_EXTENSION:
                $message = '<p style="color: red;">Error: A PHP extension stopped the file upload. (Likely misconfigured PHP)</p>';
                break;
            default:
                $message = '<p style="color: red;">Error: An unknown upload error occurred.</p>';
                break;
        }
    }
    // 2. Check if file extension is allowed
    else if (!in_array($fileExt, $allowedExtensions)) {
        $message = '<p style="color: red;">Error: Invalid file type. Only ' . implode(', ', $allowedExtensions) . ' files are allowed.</p>';
    }
    // 3. Check file size (example: max 5MB)
    // Note: This is client-side/application level check. PHP.ini settings `upload_max_filesize` and `post_max_size` also apply.
    else if ($fileSize > 5 * 1024 * 1024) { // 5 MB in bytes
        $message = '<p style="color: red;">Error: Your file is too large. Max size is 5MB.</p>';
    }
    // 4. Check for malicious files (basic, not comprehensive)
    // This is just to prevent PHP scripts from being uploaded easily.
    else if (preg_match('/\.(php|phtml|phar|pl|py|cgi|exe|sh|bat)$/i', $fileName)) {
        $message = '<p style="color: red;">Error: Executable script files are not allowed.</p>';
    }
    else {
        // --- Upload the file ---

        // Define the target directory (same as this script)
        // Ensure this directory has write permissions for your web server user.
        $uploadDirectory = __DIR__ . DIRECTORY_SEPARATOR;

        // Create a unique filename to prevent overwriting existing files and
        // to mitigate some security risks (e.g., path traversal if filename wasn't cleaned)
        // Note: You could also use the original filename if you're sure it's safe and you want to allow overwriting.
        $newFileName = uniqid('upload_', true) . '.' . $fileExt;
        $destination = $uploadDirectory . $newFileName;

        // Move the uploaded file from the temporary directory to its final destination
        if (move_uploaded_file($fileTmpName, $destination)) {
            $message = '<p style="color: green;">File <strong>' . htmlspecialchars($fileName) . '</strong> uploaded successfully!</p>';
            $message .= '<p>Saved as: ' . htmlspecialchars($newFileName) . '</p>';
        } else {
            $message = '<p style="color: red;">Error: There was an issue moving the uploaded file. This could be due to insufficient permissions or temporary file issues.</p>';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple File Upload (Direct)</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        h2 {
            color: #333;
            margin-bottom: 20px;
        }
        form {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        input[type="file"] {
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="submit"] {
            background-color: #007bff;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s ease;
        }
        input[type="submit"]:hover {
            background-color: #0056b3;
        }
        .message {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .message p {
            margin: 0;
        }
        .message p[style="color: green;"] {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .message p[style="color: red;"] {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .hint {
            margin-top: 15px;
            font-size: 0.9em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Upload a File (Direct Submission)</h2>

        <?php if (!empty($message)): ?>
            <div class="message">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- The key change: no 'id="uploadForm"' or JS event listener on submit -->
        <form action="" method="post" enctype="multipart/form-data">
            <label for="file-upload">Choose File:</label>
            <input type="file" name="uploaded_file" id="file-upload" required>
            <input type="submit" value="Upload File">
        </form>

        <p class="hint">
            Allowed file types: JPG, JPEG, PNG, GIF, PDF, DOC, DOCX, TXT, ZIP. Max size: 5MB.
        </p>
    </div>
</body>
</html>