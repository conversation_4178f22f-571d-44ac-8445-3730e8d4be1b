<?= $this->extend('layouts/main'); ?>

<?= $this->section('content'); ?>
<!-- Start wrapper-->
<div id="wrapper">

    <!--Start sidebar-wrapper-->
    <?= $this->include('partials/sidebar'); ?>
    <!--End sidebar-wrapper-->

    <!--Start topbar header-->
    <?= $this->include('partials/topbar'); ?>
    <!--End topbar header-->

    <div class="clearfix"></div>

    <div class="content-wrapper">
        <div class="container-fluid">

            <!--Start Dashboard Content-->


            <div class="card">
                <div class="card-body">
                    <h4 class="mb-0">Upcoming Events</h4>
                    <hr>
                    <div class="row gy-3">
                        <div class="col-md-2 text-end d-grid">
                            <!-- Collapse Trigger Button -->
                            <button type="button" class="btn btn-light" data-bs-toggle="collapse" data-bs-target="#bannerFormCollapse" aria-expanded="false" aria-controls="bannerFormCollapse">
                                Add banner
                            </button>
                        </div>
                    </div>

                    <!-- Collapsible Form -->
                    <div class="collapse mt-4" id="bannerFormCollapse">

                        <form class="row g-3" id="upcomingevent" enctype="multipart/form-data">
                            <div class="col-md-6">
                                <label for="inputFirstName" class="form-label">Title</label>
                                <input type="text" name="title" class="form-control" id="inputFirstName">
                            </div>
                            <div class="col-md-6">
                                <label for="description" class="form-label">Description</label>
                                <textarea type="text" name="description" class="form-control" id="description"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label for="department" class="form-label">Department</label>
                                <select class="form-control" id="department" name="department">
                                    <option value="">None</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?= esc($dept['id']) ?>"><?= esc($dept['department_name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="files" class="form-label">Files</label>
                                <input type="file" name="files[]" class="form-control" id="files" multiple>
                            </div>
                            <div class="col-md-6">
                                <label for="starting_date" class="form-label">Starting Date</label>
                                <input type="datetime-local" name="starting_date" class="form-control" id="starting_date">
                            </div>
                            <div class="col-md-6">
                                <label for="ending_date" class="form-label">Ending Date</label>
                                <input type="datetime-local" name="ending_date" class="form-control" id="ending_date">
                            </div>

                            <div class="col-12 mt-2">
                                <button type="submit" class="btn btn-light px-5">Submit</button>
                            </div>
                        </form>

                    </div>

                    <?php foreach ($upcomingevents as $events): ?>
                        <div class="row align-items-start mb-4">
                            <!-- Left: Banner Image Preview -->
                            <div class="col-md-8">
                                <img id="bannerImagePreview_<?= $events->id ?>"
                                    src="<?= base_url('uploads/hero/' . $events->banner); ?>"
                                    alt="Banner Image"
                                    class="img-fluid rounded shadow"
                                    style="max-width: 100%; height: auto;">
                            </div>

                            <!-- Right: Title, Subtitle, Upload -->
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Change Banner Image</label>
                                    <input type="file"
                                        class="form-control banner-file-input"
                                        data-id="<?= $events->id ?>"
                                        onchange="previewBannerImage(this)">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Banner Title</label>
                                    <input type="text"
                                        id="bannerTitle_<?= $events->id ?>"
                                        class="form-control"
                                        value="<?= esc($events->title); ?>">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Banner Description</label>
                                    <input type="text"
                                        id="bannerDescription_<?= $events->id ?>"
                                        class="form-control"
                                        value="<?= esc($events->description); ?>">
                                </div>

                                <button type="button"
                                    class="btn btn-primary"
                                    onclick="editBanner(<?= $events->id ?>)">Update Banner</button>
                            </div>
                        </div>
                    <?php endforeach; ?>

                </div>

            </div>
        </div>

        <?= $pager->makeLinks($currentPage, $perPage, $total, 'bootstrap'); ?>

        <!--End Dashboard Content-->

        <!--start overlay-->
        <div class="overlay toggle-menu"></div>
        <!--end overlay-->

    </div>
    <!-- End container-fluid-->

</div><!--End content-wrapper-->
<!--Start Back To Top Button-->
<a href="javaScript:void();" class="back-to-top"><i class="fa fa-angle-double-up"></i> </a>
<!--End Back To Top Button-->

<!--Start footer-->

<!--End footer-->


</div><!--End wrapper-->

<!--start color switcher-->
<?= $this->include('partials/colorswitcher'); ?>
<!--end color switcher-->

<?= $this->endSection(); ?>

<?= $this->section('scripts'); ?>
<!-- Chart.js -->
<script src="<?= base_url('assets/plugins/Chart.js/Chart.min.js'); ?>"></script>
<!-- custom scripts for saving the form -->


<!-- custom form ends here -->
<!-- Dashboard Index Script -->
<script src="<?= base_url('assets/js/index.js'); ?>"></script>

<script>
    function previewBannerImage(input) {
        const file = input.files[0];
        const id = input.getAttribute('data-id');
        const preview = document.getElementById('bannerImagePreview_' + id);

        if (file && preview) {
            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    }


    function editBanner(id) {
        const title = document.getElementById('bannerTitle_' + id).value;
        const subtitle = document.getElementById('bannerSubtitle_' + id).value;
        const imageInput = document.querySelector(`input[data-id="${id}"]`);

        const formData = new FormData();
        formData.append('bannerId', id);
        formData.append('title', title);
        formData.append('subtitle', subtitle);

        if (imageInput.files.length > 0) {
            formData.append('image', imageInput.files[0]);
        }

        fetch("<?= base_url('dashboard/updateHeroBanner') ?>", {
                method: 'POST',
                body: formData
            })
            .then(res => res.json())
            .then(data => {
                alert(data.message);
                location.reload();
            })
            .catch(err => {
                console.error("Banner update error:", err);
            });
    }
</script>

<?= $this->endSection(); ?>