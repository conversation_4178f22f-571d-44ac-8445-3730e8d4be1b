<style>
/* 
 * ------------------------------------------------------------------------
 * PAGE RENDERER STYLES
 * ------------------------------------------------------------------------
 * This file contains all the CSS necessary to style the final, public-facing 
 * page. It is included in both the admin preview and the main site layout.
 */

/* --- Base Renderer Container --- */
.page-content-renderer { 
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.65;
    color: #3d4852;
    max-width: 1536px;
    margin: 0 auto;
    padding: 0; /* No padding on the main container itself */
  }
  .page-content-renderer .page-section { 
    padding: 3rem 1.5rem; /* Padding for mobile */
  }
  .page-content-renderer .page-section.hero-banner-section {
    padding: 2rem; /* Hero has its own padding */
  }
  .page-content-renderer .section-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: clamp(2rem, 5vw, 2.75rem);
    font-weight: 700;
    color: #2c3e50;
    border-bottom: 3px solid #ecf0f1;
    padding-bottom: 1rem;
    margin-bottom: 0.5rem;
  }
  .page-content-renderer .section-title img { height: 40px; }
  .page-content-renderer .section-subtitle {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-top: 0.5rem;
    margin-bottom: 2rem;
    max-width: 800px;
  }
  .page-content-renderer .content-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2.5rem;
    margin-top: 1.5rem;
  }
  .page-content-renderer h3, .page-content-renderer h4 { color: #34495e; font-weight: 600; }
  .page-content-renderer h3 { font-size: 1.5rem; margin-bottom: 0.5em; }
  .page-content-renderer h4 { font-size: 1.2rem; margin-bottom: 0.25em; }
  .page-content-renderer ul, .page-content-renderer ol { padding-left: 25px; }
  .page-content-renderer a { color: #3498db; font-weight: 600; text-decoration: none; transition: color 0.2s; }
  .page-content-renderer a:hover { color: #2980b9; text-decoration: underline; }
  .page-content-renderer table { width: 100%; border-collapse: collapse; margin-top: 1.5em; box-shadow: 0 2px 8px rgba(0,0,0,0.05); }
  .page-content-renderer th, .page-content-renderer td { border: 1px solid #e0e6ed; padding: 12px 15px; text-align: left; }
  .page-content-renderer th { background-color: #f9fafb; font-weight: 600; }

  /* --- HERO BANNER STYLING --- */
  .hero-banner-section {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    min-height: 36rem;
    color: #fff;
    background-size: cover;
    background-position: center;
    margin-bottom: 0rem;
  }
  .hero-banner-section::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Overlay for text readability */
  }
  .hero-banner-section .hero-content {
    position: relative;
    z-index: 1;
    max-width: 900px;
  }
  .hero-banner-section .hero-title {
    font-size: clamp(2.5rem, 8vw, 4.5rem);
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0px 2px 15px rgba(0, 0, 0, 0.2);
  }
  .hero-banner-section .hero-subtitle {
    font-size: clamp(1.2rem, 4vw, 1.5rem);
    font-weight: 400;
    text-shadow: 0px 2px 10px rgba(0, 0, 0, 0.2);
  }

  /* --- MEDIA QUERIES FOR RESPONSIVENESS --- */
  @media (min-width: 768px) {
    .page-content-renderer .page-section {
      padding: 4rem 2.5rem;
    }
    .page-content-renderer .content-row.two-column {
      grid-template-columns: 1fr 1fr;
      align-items: center;
    }
  }
  @media (min-width: 1024px) {
    .page-content-renderer .content-row.three-column {
      grid-template-columns: 1fr 1fr 1fr;
      align-items: flex-start;
    }
  }
</style>