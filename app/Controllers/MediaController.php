<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class MediaController extends ResourceController
{
    protected $format = 'json';

    /**
     * Upload media files for pages
     */
    public function upload()
    {
        // 1. Use CodeIgniter's Validation service for robustness
        $validation = \Config\Services::validation();

        // 2. Define validation rules. This is cleaner and catches all errors at once.
        $rules = [
    'file' => [
        'label' => 'Media File',
        'rules' => [
            // This single rule checks for ALL upload errors, including php.ini limits
            'uploaded[file]', 
            
            // Check file size in kilobytes (200MB = 204800 KB)
            'max_size[file,204800]',
            
            // Extended MIME type validation
            'mime_in[file,' .
                'image/jpeg,image/jpg,image/png,image/gif,image/webp,' .
                'image/svg+xml,image/bmp,image/x-icon,image/heif,image/heic,' .
                'image/tiff,image/avif,' .
                'video/mp4,video/mpeg,video/ogg,video/webm,video/3gpp,video/3gpp2,' .
                'video/x-msvideo,video/x-ms-wmv,video/quicktime,video/x-flv,' .
                'video/avi,video/x-matroska,video/mkv,' .
                'audio/mpeg,audio/mp3,audio/wav,audio/ogg,audio/webm,' .
                'audio/aac,audio/flac,audio/x-flac,audio/x-wav,audio/x-ms-wma,' .
                'audio/midi,audio/x-midi,audio/x-m4a,audio/mp4' .
            ']',
        ],
    ],
];

        // 3. Run validation
        // if (!$this->validate($rules)) {
        //     // If validation fails for ANY reason, return a proper JSON error
        //     return $this->fail($validation->getErrors(), 400);
        // }

        // 4. If validation passes, we know the file is good to go.
        try {
            $file = $this->request->getFile('file');

            // This is just a final failsafe, though validation should have caught it.
            if (!$file->isValid() || $file->hasMoved()) {
                return $this->fail($file->getErrorString(), 400);
            }

            $fileType = $file->getMimeType();

            // Your excellent subdirectory logic is preserved
            $uploadDir = 'uploads/pages/';
            if (strpos($fileType, 'image/') === 0) {
                $uploadDir .= 'images/';
            } elseif (strpos($fileType, 'video/') === 0) {
                $uploadDir .= 'videos/';
            } elseif (strpos($fileType, 'audio/') === 0) {
                $uploadDir .= 'audio/';
            } else {
                $uploadDir .= 'documents/'; // A fallback for other file types
            }

            // Create directory if it doesn't exist
            if (!is_dir(FCPATH . $uploadDir)) {
                mkdir(FCPATH . $uploadDir, 0775, true);
            }

            // Use CodeIgniter's random name generator for better security
            $newName = $file->getRandomName();
            
            // Move file
            $file->move(FCPATH . $uploadDir, $newName);

            // Generate URL
            $fileUrl = base_url($uploadDir . $newName);

            return $this->respond([
                'status' => 'success',
                'message' => 'File uploaded successfully',
                'data' => [
                    'url' => $fileUrl,
                ]
            ]);

        } catch (\Exception $e) {
            // This will catch any unexpected errors during the file move process
            return $this->fail('Server error during upload: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete uploaded media file
     */
    public function delete($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!isset($data['path'])) {
                return $this->fail('File path is required', 400);
            }

            $filePath = FCPATH . $data['path'];
            
            if (!file_exists($filePath)) {
                return $this->fail('File not found', 404);
            }

            if (unlink($filePath)) {
                return $this->respond([
                    'status' => 'success',
                    'message' => 'File deleted successfully'
                ]);
            } else {
                return $this->fail('Failed to delete file', 500);
            }

        } catch (\Exception $e) {
            return $this->fail('Delete failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * List uploaded media files
     */
    public function list()
    {
        try {
            $uploadDir = FCPATH . 'uploads/pages/';
            $files = [];

            if (is_dir($uploadDir)) {
                $directories = ['images', 'videos', 'audio'];
                
                foreach ($directories as $dir) {
                    $fullDir = $uploadDir . $dir . '/';
                    if (is_dir($fullDir)) {
                        $dirFiles = scandir($fullDir);
                        foreach ($dirFiles as $file) {
                            if ($file !== '.' && $file !== '..' && is_file($fullDir . $file)) {
                                $files[] = [
                                    'filename' => $file,
                                    'url' => base_url('uploads/pages/' . $dir . '/' . $file),
                                    'type' => $dir,
                                    'size' => filesize($fullDir . $file),
                                    'created' => date('Y-m-d H:i:s', filemtime($fullDir . $file))
                                ];
                            }
                        }
                    }
                }
            }

            return $this->respond([
                'status' => 'success',
                'data' => $files
            ]);

        } catch (\Exception $e) {
            return $this->fail('Failed to list files: ' . $e->getMessage(), 500);
        }
    }
}