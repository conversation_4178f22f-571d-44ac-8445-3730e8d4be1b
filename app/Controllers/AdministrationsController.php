<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class AdministrationsController extends BaseController
{
    public function index()
    {
        //
    }

    public function createAdminstrations()
    {
        // Load the view for adding a user
        return view('administrations/add');
    }

    public function ajaxAddAdministrations()
    {
        // Handle the AJAX request to add a new administration
        $name = $this->request->getPost('name');
        $description = $this->request->getPost('description');
        $designation = $this->request->getPost('designation');

        $file = $this->request->getFile('profile_image');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move(FCPATH . 'uploads/administrations', $newName); // 🔥 Save to public/uploads/administrations
            $profile_image = $newName;
        } else {
            $profile_image = null;
        }

        $data = [
            'name' => $name,
            'description' => $description,
            'designation' => $designation,
            'profile_image' => $profile_image,
            'created_at' => date('Y-m-d H:i:s'),
            'archived' => 0, // Default to not archived
        ];

        // Simulate saving to the database
        $db = \Config\Database::connect();
        $builder = $db->table('administrations');


        // Validate the input data
        if ($builder->insert($data)) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Administration added successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to add Administration.',
            ]);
        }
    }

    public function manageAdminstrations()
    {
        // Load the view for managing administrations
        $db = \Config\Database::connect();
        $builder = $db->table('administrations');
        $perPage = 3; // Number of items per page
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();
        $administrations = $builder
            ->select('id, name, description, designation, profile_image, archived')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        $data = [
            'administrations' => $administrations,
            'pager'       => \Config\Services::pager(),
            'total'       => $total,
            'perPage'     => $perPage,
            'currentPage' => $page,
        ];

        return view('administrations/list', $data);
    }

    public function ajaxEditAdministrations()
    {
        // Handle the AJAX request to edit an existing administration
        $id = $this->request->getPost('id');
        $name = $this->request->getPost('name');
        $description = $this->request->getPost('description');
        $designation = $this->request->getPost('designation');

        $file = $this->request->getFile('profile_image');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move(FCPATH . 'uploads/administrations', $newName); // 🔥 Save to public/uploads/administrations
            $profile_image = $newName;
        } else {
            $profile_image = null;
        }

        // Simulate updating the database
        $db = \Config\Database::connect();
        $builder = $db->table('administrations');

        // Validate the input data
        if ($builder->update([
            'name' => $name,
            'description' => $description,
            'designation' => $designation
        ], ['id' => $id])) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Administration updated successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to update Administration.',
            ]);
        }
    }

    public function ajaxdeleteAdministrations()
    {
        // Handle the AJAX request to delete an administration
        $id = $this->request->getPost('id');

        // Simulate deleting from the database
        $db = \Config\Database::connect();
        $builder = $db->table('administrations');

        // Validate the input data
        if ($builder->delete(['id' => $id])) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Administration deleted successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to delete Administration.',
            ]);
        }
    }
}
