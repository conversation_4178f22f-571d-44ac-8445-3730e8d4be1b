<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class ImportantLinksController extends BaseController
{
    public function index()
    {
        //
    }
    public function studentsCorner()

    {

        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_students_corner');
        $perPage = 4;
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();

        $data['student_links'] = $builder
            ->select('imp_lin_students_corner.*')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        $data['pager'] = \Config\Services::pager();
        $data['total'] = $total;
        $data['perPage'] = $perPage;
        $data['currentPage'] = $page;
        return view('importantlinks/students_corner', $data);
    }

    public function ajaxAddStudentsCorner()
    {
        $title = $this->request->getPost('title');
        $link = $this->request->getPost('link');
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_students_corner');

        $data = [
            'title' => $title,
            'link' => $link
        ];

        if ($builder->insert($data)) {
            // Successfully inserted
            return $this->response->setJSON(['status' => 'success', 'message' => 'Link added successfully']);
        } else {
            // Insertion failed
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to add link']);
        }
    }

    public function ajaxDeleteStudentsCorner()
    {
        $id = $this->request->getPost('id');
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_students_corner');

        if ($builder->delete(['id' => $id])) {
            // Successfully deleted
            return $this->response->setJSON(['status' => 'success', 'message' => 'Link deleted successfully']);
        } else {
            // Deletion failed
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete link']);
        }
    }

    public function importantInformation()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_impoinformation');
        $perPage = 4;
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();

        $data['importantInformation'] = $builder
            ->select('imp_lin_impoinformation.*')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        $data['pager'] = \Config\Services::pager();
        $data['total'] = $total;
        $data['perPage'] = $perPage;
        $data['currentPage'] = $page;
        // Logic for important information
        return view('importantlinks/important_information', $data);
    }

    public function ajaxAddImportantInformation()
    {
        $title = $this->request->getPost('title');
        $link = $this->request->getPost('link');
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_impoinformation');

        $data = [
            'title' => $title,
            'link' => $link
        ];

        if ($builder->insert($data)) {
            // Successfully inserted
            return $this->response->setJSON(['status' => 'success', 'message' => 'Link added successfully']);
        } else {
            // Insertion failed
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to add link']);
        }
    }
    public function ajaxDeleteImportantInformation()
    {
        $id = $this->request->getPost('id');
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_impoinformation');

        if ($builder->delete(['id' => $id])) {
            // Successfully deleted
            return $this->response->setJSON(['status' => 'success', 'message' => 'Link deleted successfully']);
        } else {
            // Deletion failed
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete link']);
        }
    }

    public function onlineServices()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_onlineservices');
        $perPage = 4;
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();

        $data['onlineServices'] = $builder
            ->select('imp_lin_onlineservices.*')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        $data['pager'] = \Config\Services::pager();
        $data['total'] = $total;
        $data['perPage'] = $perPage;
        $data['currentPage'] = $page;
        // Logic for online services
        return view('importantlinks/online_services', $data);
    }
    public function ajaxAddOnlineServices()
    {
        $title = $this->request->getPost('title');
        $link = $this->request->getPost('link');
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_onlineservices');

        $data = [
            'title' => $title,
            'link' => $link
        ];

        if ($builder->insert($data)) {
            // Successfully inserted
            return $this->response->setJSON(['status' => 'success', 'message' => 'Link added successfully']);
        } else {
            // Insertion failed
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to add link']);
        }
    }
    public function ajaxDeleteOnlineServices()
    {
        $id = $this->request->getPost('id');
        $db = \Config\Database::connect();
        $builder = $db->table('imp_lin_onlineservices');

        if ($builder->delete(['id' => $id])) {
            // Successfully deleted
            return $this->response->setJSON(['status' => 'success', 'message' => 'Link deleted successfully']);
        } else {
            // Deletion failed
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete link']);
        }
    }
}
