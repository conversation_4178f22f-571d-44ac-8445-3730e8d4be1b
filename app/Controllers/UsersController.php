<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Controllers\BaseController;
use App\Models\DepartmentsModel;
use App\Models\UserModel;

class UsersController extends BaseController
{
    public function ajaxListUsers()
    {
        $departmentModel = new DepartmentsModel();
        $data['departments'] = $departmentModel->findAll();
        return view('users/list', $data);
    }

    public function ajaxAddUsers()
    {
        $request = service('request');
        $session = session();
        $username = $request->getPost('username');

        // Generate a 6-digit password: first 3 letters of username + 3 random digits
        $usernamePart = substr(preg_replace('/\W/', '', $username), 0, 3); // Only alphanumeric, max 3 chars
        $numberPart = str_pad(random_int(0, 999), 3, '0', STR_PAD_LEFT);
        $plainPassword = $usernamePart . $numberPart;
        $password = password_hash($plainPassword, PASSWORD_DEFAULT);

        $file = $this->request->getFile('profile_image');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move(FCPATH . 'uploads/users', $newName); // 🔥 Save to public/uploads/users
            $profile_image = $newName;
        } else {
            $profile_image = null;
        }
        $data = [
            'username'        => $request->getPost('username'),
            'email'           => $request->getPost('email'),
            'password'        => $password,
            'role'            => $request->getPost('role'),
            'department_id'      => $request->getPost('department') ? $request->getPost('department') : null,
            'profile_picture'   => $profile_image,
            'created_by'      => $session->get('id'),
        ];

        $db = \Config\Database::connect();
        $builder = $db->table('users');
        $existingUser = $builder->where('username', $data['username'])->get()->getRow();

        if ($existingUser) {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Username already exists. Please choose another.',
            ]);
        }

        // Save to DB

        $builder->insert($data);

        // ✅ Log the action
        $details = "Added user: " . $data['username'];
        \logUserAction('insert', $details);

        // Send email with credentials
        $this->_sendOtpEmail($data['email'], $data['email'], $data['username'], $plainPassword);

        // Return response
        return $this->response->setJSON([
            'status'  => 'success',
            'message' => 'User added successfully',
        ]);
    }



    public function addUsers()
    {
        $departmentModel = new DepartmentsModel();
        $data['departments'] = $departmentModel->findAll();
      
        return view('users/add', $data);
    }

    public function rolePermissionUsers()
    {
        return view('users/roles_permissions');
    }

    public function listUsers()
    {
        // 2. Load Paginated Departments
        $db = \Config\Database::connect();
        $builder = $db->table('users');

        
        $perPage = 4;
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();

        // 1. Load Users with Department Name (JOIN)
        $userModel = new \App\Models\UserModel();
        $data['users'] = $userModel
            ->select('users.*, departments.department_name')
            ->join('departments', 'departments.id = users.department_id', 'left')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        

        // Merge everything into view data
        // $data['departments'] = $departments;
        $data['pager'] = \Config\Services::pager();
        $data['total'] = $total;
        $data['perPage'] = $perPage;
        $data['currentPage'] = $page;

        return view('users/list', $data);
    }




    // PRIVATE HELPER FUNCTION: To send the email
    private function _sendOtpEmail($to, $mail, $name, $password)
    {
        $email = \Config\Services::email();

        $email->setTo($to);
        $email->setFrom('<EMAIL>'); // Change this
        $email->setSubject('WELCOME TO JBU CMS');
        $email->setMessage("Hello {$name},<br><br>Your credentials for JBU CMS is: <br><b>Email: {$mail}</b><br><b>Password: {$password}</b><br>Don't share this mail to anyone.<br>If you did not request this, please ignore this email.");

        // For debugging, you can check if the email was sent
        if (!$email->send()) {
            log_message('error', $email->printDebugger(['headers']));
        }
    }
}
