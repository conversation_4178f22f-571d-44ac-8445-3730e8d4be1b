<?php

namespace App\Controllers;

use App\Services\CacheInvalidationService;
use CodeIgniter\RESTful\ResourceController;

class CacheManagementController extends ResourceController
{
    protected $cacheInvalidationService;
    protected $format = 'json';

    public function __construct()
    {
        $this->cacheInvalidationService = new CacheInvalidationService();
    }

    /**
     * Get cache status overview
     * GET /api/cache/status
     */
    public function getStatus()
    {
        try {
            $status = $this->cacheInvalidationService->getCacheStatus();
            
            return $this->respond([
                'status' => 'success',
                'data' => $status,
                'timestamp' => date('c')
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to get cache status: ' . $e->getMessage());
        }
    }

    /**
     * Clear all navigation caches
     * POST /api/cache/clear/navigation
     */
    public function clearNavigationCache()
    {
        try {
            $result = $this->cacheInvalidationService->clearAllNavigationCaches();
            
            return $this->respond([
                'status' => 'success',
                'message' => 'Navigation caches cleared successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to clear navigation cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear specific faculty cache
     * POST /api/cache/clear/faculty/{id}
     */
    public function clearFacultyCache($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                return $this->fail('Valid faculty ID is required', 400);
            }

            $result = $this->cacheInvalidationService->invalidateFacultyCache($id);
            
            return $this->respond([
                'status' => 'success',
                'message' => "Faculty cache cleared for ID: {$id}",
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to clear faculty cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear specific department cache
     * POST /api/cache/clear/department/{id}
     */
    public function clearDepartmentCache($id = null)
    {
        try {
            if (!$id || !is_numeric($id)) {
                return $this->fail('Valid department ID is required', 400);
            }

            // Get faculty_id for the department
            $departmentsModel = new \App\Models\DepartmentsModel();
            $department = $departmentsModel->find($id);
            $facultyId = $department['faculty_id'] ?? null;

            $result = $this->cacheInvalidationService->invalidateDepartmentCache($id, $facultyId);
            
            return $this->respond([
                'status' => 'success',
                'message' => "Department cache cleared for ID: {$id}",
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to clear department cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear university statistics cache
     * POST /api/cache/clear/stats
     */
    public function clearStatsCache()
    {
        try {
            $result = $this->cacheInvalidationService->invalidateUniversityStats();
            
            return $this->respond([
                'status' => 'success',
                'message' => 'University statistics cache cleared successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to clear statistics cache: ' . $e->getMessage());
        }
    }

    /**
     * Clear hierarchy cache only
     * POST /api/cache/clear/hierarchy
     */
    public function clearHierarchyCache()
    {
        try {
            $result = $this->cacheInvalidationService->invalidateNavigationCache();
            
            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty hierarchy cache cleared successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to clear hierarchy cache: ' . $e->getMessage());
        }
    }

    // View Methods for Admin Interface

    /**
     * Cache management dashboard view
     */
    public function managementView()
    {
        try {
            $cacheStatus = $this->cacheInvalidationService->getCacheStatus();
            
            // Get faculties and departments for selective clearing
            $facultyModel = new \App\Models\FacultyModel();
            $departmentsModel = new \App\Models\DepartmentsModel();
            
            $faculties = $facultyModel->select('id, faculty_name')->findAll();
            $departments = $departmentsModel->select('id, department_name, faculty_id')->findAll();
            
            $data = [
                'title' => 'Cache Management',
                'cache_status' => $cacheStatus,
                'faculties' => $faculties,
                'departments' => $departments,
                'last_updated' => date('Y-m-d H:i:s')
            ];
            
            return view('admin/cache_management', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load cache management: ' . $e->getMessage());
        }
    }

    /**
     * AJAX endpoint for clearing navigation cache
     */
    public function ajaxClearNavigationCache()
    {
        try {
            $result = $this->cacheInvalidationService->clearAllNavigationCaches();
            
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'All navigation caches cleared successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to clear navigation cache: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX endpoint for clearing specific faculty cache
     */
    public function ajaxClearFacultyCache()
    {
        try {
            $facultyId = $this->request->getPost('faculty_id');
            
            if (!$facultyId || !is_numeric($facultyId)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Valid faculty ID is required'
                ]);
            }

            $result = $this->cacheInvalidationService->invalidateFacultyCache($facultyId);
            
            return $this->response->setJSON([
                'status' => 'success',
                'message' => "Faculty cache cleared successfully",
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to clear faculty cache: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX endpoint for clearing specific department cache
     */
    public function ajaxClearDepartmentCache()
    {
        try {
            $departmentId = $this->request->getPost('department_id');
            
            if (!$departmentId || !is_numeric($departmentId)) {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Valid department ID is required'
                ]);
            }

            // Get faculty_id for the department
            $departmentsModel = new \App\Models\DepartmentsModel();
            $department = $departmentsModel->find($departmentId);
            $facultyId = $department['faculty_id'] ?? null;

            $result = $this->cacheInvalidationService->invalidateDepartmentCache($departmentId, $facultyId);
            
            return $this->response->setJSON([
                'status' => 'success',
                'message' => "Department cache cleared successfully",
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to clear department cache: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * AJAX endpoint for getting current cache status
     */
    public function ajaxGetCacheStatus()
    {
        try {
            $status = $this->cacheInvalidationService->getCacheStatus();
            
            return $this->response->setJSON([
                'status' => 'success',
                'data' => $status,
                'timestamp' => date('c')
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to get cache status: ' . $e->getMessage()
            ]);
        }
    }
}