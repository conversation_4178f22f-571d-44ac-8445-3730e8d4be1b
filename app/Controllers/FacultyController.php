<?php

namespace App\Controllers;

use App\Models\FacultyModel;
use CodeIgniter\RESTful\ResourceController;

class FacultyController extends ResourceController
{
    protected $modelName = 'App\Models\FacultyModel';
    protected $format = 'json';
    protected $cacheInvalidationService;

    public function __construct()
    {
        $this->model = new FacultyModel();
        $this->cacheInvalidationService = new \App\Services\CacheInvalidationService();
    }

    /**
     * Get all faculties
     */
    public function index()
    {
        try {
            $faculties = $this->model->getAllFaculties();
            return $this->respond([
                'status' => 'success',
                'data' => $faculties
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculties: ' . $e->getMessage());
        }
    }

    /**
     * Get specific faculty by ID
     */
    public function show($id = null)
    {
        try {
            $faculty = $this->model->getFacultyWithDean($id);
            
            if (!$faculty) {
                return $this->failNotFound('Faculty not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $faculty
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculty: ' . $e->getMessage());
        }
    }

    /**
     * Create new faculty
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            // Validate required fields
            if (empty($data['faculty_name'])) {
                return $this->fail('Faculty name is required', 400);
            }

            $facultyId = $this->model->createFaculty($data);
            
            if (!$facultyId) {
                $errors = $this->model->errors();
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $newFaculty = $this->model->getFacultyWithDean($facultyId);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateFacultyCache($facultyId);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in FacultyController::create: ' . $e->getMessage());
            }

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Faculty created successfully',
                'data' => $newFaculty
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to create faculty: ' . $e->getMessage());
        }
    }

    /**
     * Update existing faculty
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            $existingFaculty = $this->model->find($id);
            if (!$existingFaculty) {
                return $this->failNotFound('Faculty not found');
            }

            $success = $this->model->updateFaculty($id, $data);
            
            if (!$success) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid dean selection. Dean must belong to this faculty\'s departments.', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $updatedFaculty = $this->model->getFacultyWithDean($id);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateFacultyCache($id);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in FacultyController::update: ' . $e->getMessage());
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty updated successfully',
                'data' => $updatedFaculty
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update faculty: ' . $e->getMessage());
        }
    }

    /**
     * Delete faculty
     */
    public function delete($id = null)
    {
        try {
            $existingFaculty = $this->model->find($id);
            if (!$existingFaculty) {
                return $this->failNotFound('Faculty not found');
            }

            // Check if faculty can be deleted (no departments)
            if (!$this->model->canDelete($id)) {
                return $this->respond([
                    'status' => 'error',
                    'message' => 'Cannot delete faculty: Faculty has departments. Please remove all departments first.'
                ], 403);
            }

            $this->model->delete($id);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateFacultyCache($id);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in FacultyController::delete: ' . $e->getMessage());
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to delete faculty: ' . $e->getMessage());
        }
    }

    /**
     * Get available deans for a faculty
     */
    public function getAvailableDeans($id = null)
    {
        try {
            $existingFaculty = $this->model->find($id);
            if (!$existingFaculty) {
                return $this->failNotFound('Faculty not found');
            }

            $availableDeans = $this->model->getAvailableDeans($id);

            return $this->respond([
                'status' => 'success',
                'data' => $availableDeans
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch available deans: ' . $e->getMessage());
        }
    }

    /**
     * Update dean for a faculty
     */
    public function updateDean($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data || !isset($data['dean_id'])) {
                return $this->fail('Dean ID is required', 400);
            }

            $existingFaculty = $this->model->find($id);
            if (!$existingFaculty) {
                return $this->failNotFound('Faculty not found');
            }

            $success = $this->model->updateDean($id, $data['dean_id']);
            
            if (!$success) {
                return $this->fail('Invalid dean selection. Dean must belong to this faculty\'s departments.', 400);
            }

            $updatedFaculty = $this->model->getFacultyWithDean($id);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateFacultyCache($id);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in FacultyController::updateDean: ' . $e->getMessage());
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Dean assigned successfully',
                'data' => $updatedFaculty
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update dean: ' . $e->getMessage());
        }
    }

    /**
     * Remove dean from faculty
     */
    public function removeDean($id = null)
    {
        try {
            $existingFaculty = $this->model->find($id);
            if (!$existingFaculty) {
                return $this->failNotFound('Faculty not found');
            }

            $success = $this->model->removeDean($id);
            
            if (!$success) {
                return $this->fail('Failed to remove dean', 500);
            }

            $updatedFaculty = $this->model->getFacultyWithDean($id);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateFacultyCache($id);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in FacultyController::removeDean: ' . $e->getMessage());
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Dean removed successfully',
                'data' => $updatedFaculty
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to remove dean: ' . $e->getMessage());
        }
    }

    /**
     * Get faculty showcase data
     */
    public function getShowcase($id = null)
    {
        try {
            $existingFaculty = $this->model->find($id);
            if (!$existingFaculty) {
                return $this->failNotFound('Faculty not found');
            }

            $showcaseData = $this->model->getFacultyShowcase($id);

            return $this->respond([
                'status' => 'success',
                'data' => $showcaseData
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculty showcase: ' . $e->getMessage());
        }
    }

    // View Methods for CMS Interface

    /**
     * Faculty management view
     */
    public function manageFacultiesView()
    {
        try {
            $data = [
                'title' => 'Manage Faculties',
                'faculties' => $this->model->getAllFaculties()
            ];
            
            return view('faculties/manage_faculties', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load faculties: ' . $e->getMessage());
        }
    }

    /**
     * Create faculty view
     */
    public function createFacultyView()
    {
        $data = [
            'title' => 'Create New Faculty'
        ];
        
        return view('faculties/create_faculty', $data);
    }

    /**
     * Edit faculty view
     */
    public function editFacultyView($id)
    {
        try {
            $faculty = $this->model->getFacultyWithDean($id);
            
            if (!$faculty) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Faculty not found');
            }

            $data = [
                'title' => 'Edit Faculty: ' . $faculty['faculty_name'],
                'faculty' => $faculty,
                'availableDeans' => $this->model->getAvailableDeans($id)
            ];
            
            return view('faculties/edit_faculty', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load faculty: ' . $e->getMessage());
        }
    }

    /**
     * Faculty showcase view (public)
     */
    public function facultyShowcaseView($id)
    {
        try {
            $showcaseData = $this->model->getFacultyShowcase($id);
            
            if (!$showcaseData || !$showcaseData['faculty']) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Faculty not found');
            }

            $data = [
                'title' => $showcaseData['faculty']['faculty_name'],
                'showcase' => $showcaseData
            ];
            
            return view('faculties/faculty_showcase', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load faculty showcase: ' . $e->getMessage());
        }
    }

    // Legacy methods for backward compatibility
    public function addFaculty()
    {
        return $this->createFacultyView();
    }

    public function listFaculty()
    {
        return $this->manageFacultiesView();
    }
}