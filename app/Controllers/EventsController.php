<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;
use App\Models\DepartmentsModel;

class EventsController extends BaseController
{
    public function index()
    {
        //
    }
    public function upcomingEvents()
    {

        $db = \Config\Database::connect();
        $builder = $db->table('events_upcoming');
        $data = $builder->get()->getResultArray();

        $perPage = 4; // Number of items per page
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();
        $upcomingevents = $builder
            ->select('events_upcoming.*, departments.department_name')
            ->join('departments', 'departments.id = events_upcoming.department_id', 'left')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();
        $departmentsModel = new DepartmentsModel();
        $departments = $departmentsModel->findAll();

        // Pass data to view
        $data = [
            'upcomingevents' => $upcomingevents,
            'departments'    => $departments,
            'pager'       => \Config\Services::pager(),
            'total'       => $total,
            'perPage'     => $perPage,
            'currentPage' => $page,
        ];

        return view('events/upcomingevents', $data);
    }

    public function addEvents()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('events_upcoming');

        // Get form data
        $title         = $this->request->getPost('title');
        $description   = $this->request->getPost('description');
        $starting_date = $this->request->getPost('starting_date');
        $ending_date   = $this->request->getPost('ending_date');
        $department    = $this->request->getPost('department');

        // Handle multiple file uploads
        $uploadedFiles = $this->request->getFiles();
        $savedFiles = [];

        if (isset($uploadedFiles['files']) && is_array($uploadedFiles['files'])) {
            foreach ($uploadedFiles['files'] as $file) {
                if ($file->isValid() && !$file->hasMoved()) {
                    $newName = $file->getRandomName();
                    $file->move(FCPATH . 'uploads/events/', $newName);
                    $savedFiles[] = $newName;
                }
            }
        }

        if (empty($savedFiles)) {
            return redirect()->back()->with('error', 'At least one valid banner file must be uploaded.');
        }

        // Store file names as JSON or comma-separated (you decide)
        $storedFileNames = json_encode($savedFiles); // or implode(',', $savedFiles);

        // Prepare data
        $data = [
            'title'         => $title,
            'description'   => $description,
            'starting_date' => date('Y-m-d H:i:s', strtotime($starting_date)),
            'ending_date'   => date('Y-m-d H:i:s', strtotime($ending_date)),
            'department_id' => $department,
            'banner'        => $storedFileNames, // adjust column type in DB to TEXT or JSON
            'created_at'    => date('Y-m-d H:i:s'),
        ];

        if ($builder->insert($data)) {
            return $this->response->setJSON([
            'status'  => 'success',
            'message' => 'Department added successfully',
        ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to add event.',
            ]);
        }
    }

    public function ajaxEditEvents()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('events_upcoming');

        // Get form data
        $id            = $this->request->getPost('id');
        $title         = $this->request->getPost('title');
        $description   = $this->request->getPost('description');
        $starting_date = $this->request->getPost('starting_date');
        $ending_date   = $this->request->getPost('ending_date');

        // Prepare data for update
        $data = [
            'title'         => $title,
            'description'   => $description,
            'starting_date' => date('Y-m-d H:i:s', strtotime($starting_date)),
            'ending_date'   => date('Y-m-d H:i:s', strtotime($ending_date)),
        ];

        if ($builder->where('id', $id)->update($data)) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Event updated successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to update event.',
            ]);
        }
    }


    public function ajaxDeleteEvents()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('events_upcoming');

        // Get event ID from POST request
        $id = $this->request->getPost('id');

        if ($builder->where('id', $id)->delete()) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Event deleted successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to delete event.',
            ]);
        }
    }
    public function newsEvents(){

        $db = \Config\Database::connect();
        $builder = $db->table('events_news-events');
        $data = $builder->get()->getResultArray();

        $perPage = 5; // Number of items per page
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();
        $news_events = $builder
            ->select('events_news-events.*, departments.department_name')
            ->join('departments', 'departments.id = events_news-events.department_id', 'left')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();
        $departmentsModel = new DepartmentsModel();
        $departments = $departmentsModel->findAll();

        // Pass data to view
        $data = [
            'news_events' => $news_events,
            'departments'    => $departments,
            'pager'       => \Config\Services::pager(),
            'total'       => $total,
            'perPage'     => $perPage,
            'currentPage' => $page,
        ];

        return view('events/news-events', $data);

    }

    public function newsEventsadd()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('events_news-events');

        // Get form data
        $title         = $this->request->getPost('title');
        $description   = $this->request->getPost('description');
        $department    = $this->request->getPost('department');

        // Handle multiple file uploads
        $uploadedFiles = $this->request->getFiles();
        $savedFiles = [];

        if (isset($uploadedFiles['files']) && is_array($uploadedFiles['files'])) {
            foreach ($uploadedFiles['files'] as $file) {
                if ($file->isValid() && !$file->hasMoved()) {
                    $newName = $file->getRandomName();
                    $file->move(FCPATH . 'uploads/events/', $newName);
                    $savedFiles[] = $newName;
                }
            }
        }

        if (empty($savedFiles)) {
            return redirect()->back()->with('error', 'At least one valid banner file must be uploaded.');
        }

        // Store file names as JSON or comma-separated (you decide)
        $storedFileNames = json_encode($savedFiles); // or implode(',', $savedFiles);

        // Prepare data
        $data = [
            'title'         => $title,
            'description'   => $description,
            'department_id' => $department,
            'banner'        => $storedFileNames, // adjust column type in DB to TEXT or JSON
            'created_at'    => date('Y-m-d H:i:s'),
        ];

        if ($builder->insert($data)) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Event added successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to add event.',
            ]);
        }
    }

    public function newsEventsedit()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('events_news-events');

        // Get form data
        $id            = $this->request->getPost('id');
        $title         = $this->request->getPost('title');
        $description   = $this->request->getPost('description');

        // Prepare data for update
        $data = [
            'title'         => $title,
            'description'   => $description,
        ];

        if ($builder->where('id', $id)->update($data)) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Event updated successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to update event.',
            ]);
        }
    }

    public function newsEventsdelete()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('events_news-events');

        // Get event ID from POST request
        $id = $this->request->getPost('id');

        if ($builder->where('id', $id)->delete()) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Event deleted successfully',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to delete event.',
            ]);
        }
    }
}