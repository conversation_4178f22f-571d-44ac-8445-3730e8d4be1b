<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class ShortcutsController extends BaseController
{
    public function index()
    {
        //
    }
    public function addShortcuts()
    {
        $data = [
            'title' => 'Add Shortcuts',
            'page_title' => 'Add Shortcuts',
            'page_heading' => 'Add Shortcuts',
        ];

        return view('shortcuts/add', $data);
    }

    public function ajaxAddShortcuts()
    {
        $title = $this->request->getPost('title');
        $src = $this->request->getPost('src');
        $icon = $this->request->getFile('icon');

        $db = \Config\Database::connect();
        $builder = $db->table('shortcuts');

        // Default value
        $iconFileName = null;

        // Handle icon upload if provided
        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $iconFileName = $icon->getRandomName(); // Generate a unique file name
            $icon->move(FCPATH . 'uploads/shortcuts', $iconFileName); // Move to target folder
        }

        $data = [
            'title' => $title,
            'src'   => $src,
            'icon'  => $iconFileName, // store new icon name
            'created_at' => date('Y-m-d H:i:s'),
        ];

        if ($builder->insert($data)) {
            return $this->response->setJSON(['status' => 'success', 'message' => 'Shortcut added successfully']);
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to add shortcut']);
        }
    }

    public function manageShortcuts()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('shortcuts');

        $perPage = 3; // Number of items per page
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();

        $shortcuts = $builder
            ->select('id, title, src, icon, created_at')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        $data = [
            'shortcuts' => $shortcuts,
            'pager'     => \Config\Services::pager(),
            'total'     => $total,
            'perPage'   => $perPage,
            'currentPage' => $page,
        ];

        return view('shortcuts/list', $data);
    }

    public function ajaxEditShortcuts()
    {
        $id = $this->request->getPost('id');
        $title = $this->request->getPost('title');
        $src = $this->request->getPost('src');
        $icon = $this->request->getFile('icon');

        $db = \Config\Database::connect();
        $builder = $db->table('shortcuts');

        // Default value
        $iconFileName = null;

        // Handle icon upload if provided
        if ($icon && $icon->isValid() && !$icon->hasMoved()) {
            $iconFileName = $icon->getRandomName(); // Generate a unique file name
            $icon->move(FCPATH . 'uploads/shortcuts', $iconFileName); // Move to target folder
        }

        $data = [
            'title' => $title,
            'src'   => $src,
            'icon'  => $iconFileName, // store new icon name
            'updated_at' => date('Y-m-d H:i:s'),
        ];

        if ($builder->where('id', $id)->update($data)) {
            return $this->response->setJSON(['status' => 'success', 'message' => 'Shortcut updated successfully']);
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to update shortcut']);
        }
    }

    public function ajaxDeleteShortcuts()
    {
        $id = $this->request->getPost('id');

        $db = \Config\Database::connect();
        $builder = $db->table('shortcuts');

        if ($builder->where('id', $id)->delete()) {
            return $this->response->setJSON(['status' => 'success', 'message' => 'Shortcut deleted successfully']);
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete shortcut']);
        }
    }
}
