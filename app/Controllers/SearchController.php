<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\SearchService;
use CodeIgniter\HTTP\ResponseInterface;

class SearchController extends BaseController
{
    protected $searchService;

    public function __construct()
    {
        $this->searchService = new SearchService();
    }

    /**
     * Quick search endpoint for AJAX requests
     * Returns limited results for dropdown display
     */
    public function quickSearch()
    {
        try {
            $query = $this->request->getGet('q');
            $limit = (int)($this->request->getGet('limit') ?? 5);

            if (empty($query) || strlen(trim($query)) < 2) {
                return $this->response->setJSON([
                    'success' => true,
                    'results' => [],
                    'message' => 'Please enter at least 2 characters'
                ]);
            }

            $results = $this->searchService->performQuickSearch($query, $limit);

            return $this->response->setJSON([
                'success' => true,
                'results' => $results,
                'query' => $query,
                'total_categories' => count($results)
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Quick search error: ' . $e->getMessage());
            
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Search temporarily unavailable. Please try again later.',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Full search endpoint for comprehensive search results
     */
    public function fullSearch()
    {
        try {
            $query = $this->request->getGet('q');
            $category = $this->request->getGet('category') ?? 'all';
            $page = (int)($this->request->getGet('page') ?? 1);
            $perPage = (int)($this->request->getGet('per_page') ?? 20);
            $sortBy = $this->request->getGet('sort_by') ?? 'relevance';

            if (empty($query) || strlen(trim($query)) < 2) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Please enter at least 2 characters',
                    'results' => []
                ]);
            }

            $filters = [
                'category' => $category,
                'page' => $page,
                'per_page' => $perPage,
                'sort_by' => $sortBy
            ];

            $results = $this->searchService->performFullSearch($query, $filters);

            return $this->response->setJSON([
                'success' => true,
                'data' => $results
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Full search error: ' . $e->getMessage());
            
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Search temporarily unavailable. Please try again later.',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Search by specific category
     */
    public function searchByCategory($category = null)
    {
        try {
            $query = $this->request->getGet('q');
            $limit = (int)($this->request->getGet('limit') ?? 20);

            if (empty($query) || strlen(trim($query)) < 2) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Please enter at least 2 characters'
                ]);
            }

            if (empty($category)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Category is required'
                ]);
            }

            $results = $this->searchService->searchByCategory($query, $category);

            return $this->response->setJSON([
                'success' => true,
                'results' => $results,
                'category' => $category,
                'query' => $query
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Category search error: ' . $e->getMessage());
            
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Search temporarily unavailable. Please try again later.'
            ]);
        }
    }

    /**
     * Get search suggestions for autocomplete
     */
    public function suggestions()
    {
        try {
            $query = $this->request->getGet('q');
            $limit = (int)($this->request->getGet('limit') ?? 10);

            if (empty($query) || strlen(trim($query)) < 2) {
                return $this->response->setJSON([
                    'success' => true,
                    'suggestions' => []
                ]);
            }

            $suggestions = $this->searchService->getSearchSuggestions($query, $limit);

            return $this->response->setJSON([
                'success' => true,
                'suggestions' => $suggestions,
                'query' => $query
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Search suggestions error: ' . $e->getMessage());
            
            return $this->response->setJSON([
                'success' => false,
                'suggestions' => []
            ]);
        }
    }

    /**
     * Search results page view
     */
    public function searchResults()
    {
        $query = $this->request->getGet('q') ?? '';
        $category = $this->request->getGet('category') ?? 'all';
        $page = (int)($this->request->getGet('page') ?? 1);
        $sortBy = $this->request->getGet('sort_by') ?? 'relevance';

        $data = [
            'query' => $query,
            'category' => $category,
            'page' => $page,
            'sort_by' => $sortBy,
            'categories' => [
                'all' => 'All Results',
                'faculty_members' => 'Faculty Members',
                'students' => 'Students',
                'departments' => 'Departments',
                'faculties' => 'Faculties',
                'pages' => 'Pages',
                'events' => 'Events',
                'notices' => 'Notices'
            ]
        ];

        // If there's a query, perform the search
        if (!empty($query) && strlen(trim($query)) >= 2) {
            try {
                $filters = [
                    'category' => $category,
                    'page' => $page,
                    'per_page' => 20,
                    'sort_by' => $sortBy
                ];

                $searchResults = $this->searchService->performFullSearch($query, $filters);
                $data['search_results'] = $searchResults;

            } catch (\Exception $e) {
                log_message('error', 'Search results page error: ' . $e->getMessage());
                $data['error'] = 'Search temporarily unavailable. Please try again later.';
            }
        }

        return view('search/results', $data);
    }

    /**
     * Validate search input
     */
    private function validateSearchInput(string $query): array
    {
        $errors = [];

        if (empty(trim($query))) {
            $errors[] = 'Search query cannot be empty';
        }

        if (strlen(trim($query)) < 2) {
            $errors[] = 'Search query must be at least 2 characters long';
        }

        if (strlen(trim($query)) > 100) {
            $errors[] = 'Search query cannot exceed 100 characters';
        }

        // Check for potentially malicious input
        if (preg_match('/[<>"\']/', $query)) {
            $errors[] = 'Search query contains invalid characters';
        }

        return $errors;
    }

    /**
     * Rate limiting check (basic implementation)
     */
    private function checkRateLimit(): bool
    {
        $session = session();
        $currentTime = time();
        $searchCount = $session->get('search_count') ?? 0;
        $lastSearchTime = $session->get('last_search_time') ?? 0;

        // Reset counter if more than 1 minute has passed
        if ($currentTime - $lastSearchTime > 60) {
            $searchCount = 0;
        }

        // Allow maximum 30 searches per minute
        if ($searchCount >= 30) {
            return false;
        }

        // Update session data
        $session->set([
            'search_count' => $searchCount + 1,
            'last_search_time' => $currentTime
        ]);

        return true;
    }
}