<?php

namespace App\Controllers;

use App\Models\StudentsModel;
use CodeIgniter\RESTful\ResourceController;

class StudentsController extends ResourceController
{
    protected $modelName = 'App\Models\StudentsModel';
    protected $format = 'json';

    public function __construct()
    {
        $this->model = new StudentsModel();
    }

    /**
     * Get all students with filtering options
     */
    public function index()
    {
        try {
            $filters = $this->request->getGet();
            
            if (isset($filters['status'])) {
                if ($filters['status'] === 'current') {
                    $students = $this->model->getCurrentStudents();
                } elseif ($filters['status'] === 'alumni') {
                    $students = $this->model->getAlumni();
                } else {
                    $students = $this->model->select('students.*, departments.department_name, faculties.faculty_name')
                                           ->join('departments', 'students.department_id = departments.id', 'left')
                                           ->join('faculties', 'departments.faculty_id = faculties.id', 'left')
                                           ->where('students.status', $filters['status'])
                                           ->findAll();
                }
            } elseif (isset($filters['department_id'])) {
                $students = $this->model->getByDepartment($filters['department_id']);
            } elseif (isset($filters['faculty_id'])) {
                $students = $this->model->getByFaculty($filters['faculty_id']);
            } elseif (isset($filters['program_type'])) {
                $students = $this->model->getByProgramType($filters['program_type']);
            } elseif (isset($filters['admission_year'])) {
                $students = $this->model->getByAdmissionYear($filters['admission_year']);
            } elseif (isset($filters['completion_year'])) {
                $students = $this->model->getByCompletionYear($filters['completion_year']);
            } elseif (isset($filters['search'])) {
                $students = $this->model->searchStudents($filters['search']);
            } else {
                $students = $this->model->getAllStudents();
            }

            return $this->respond([
                'status' => 'success',
                'data' => $students
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch students: ' . $e->getMessage());
        }
    }

    /**
     * Get specific student by ID
     */
    public function show($id = null)
    {
        try {
            $student = $this->model->getStudentWithDetails($id);
            
            if (!$student) {
                return $this->failNotFound('Student not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $student
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch student: ' . $e->getMessage());
        }
    }

    /**
     * Create new student
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            // Validate required fields
            $requiredFields = ['full_name', 'student_id', 'email', 'department_id', 'program_type', 'admission_year'];
            
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return $this->fail(ucfirst(str_replace('_', ' ', $field)) . ' is required', 400);
                }
            }

            $studentId = $this->model->createStudent($data);
            
            if (!$studentId) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid department selection or completion year must be after admission year', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $newStudent = $this->model->getStudentWithDetails($studentId);

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Student created successfully',
                'data' => $newStudent
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to create student: ' . $e->getMessage());
        }
    }

    /**
     * Update existing student
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            $existingStudent = $this->model->find($id);
            if (!$existingStudent) {
                return $this->failNotFound('Student not found');
            }

            $success = $this->model->updateStudent($id, $data);
            
            if (!$success) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid department selection or completion year must be after admission year', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $updatedStudent = $this->model->getStudentWithDetails($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Student updated successfully',
                'data' => $updatedStudent
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update student: ' . $e->getMessage());
        }
    }

    /**
     * Delete student
     */
    public function delete($id = null)
    {
        try {
            $existingStudent = $this->model->find($id);
            if (!$existingStudent) {
                return $this->failNotFound('Student not found');
            }

            $this->model->delete($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Student deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to delete student: ' . $e->getMessage());
        }
    }

    /**
     * Mark student as alumni
     */
    public function markAsAlumni($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            $existingStudent = $this->model->find($id);
            if (!$existingStudent) {
                return $this->failNotFound('Student not found');
            }

            $completionYear = isset($data['completion_year']) ? $data['completion_year'] : null;
            $success = $this->model->markAsAlumni($id, $completionYear);
            
            if (!$success) {
                return $this->fail('Failed to mark student as alumni', 500);
            }

            $updatedStudent = $this->model->getStudentWithDetails($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Student marked as alumni successfully',
                'data' => $updatedStudent
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to mark student as alumni: ' . $e->getMessage());
        }
    }

    /**
     * Mark student as current (remove alumni status)
     */
    public function markAsCurrent($id = null)
    {
        try {
            $existingStudent = $this->model->find($id);
            if (!$existingStudent) {
                return $this->failNotFound('Student not found');
            }

            $success = $this->model->markAsCurrent($id);
            
            if (!$success) {
                return $this->fail('Failed to mark student as current', 500);
            }

            $updatedStudent = $this->model->getStudentWithDetails($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Student marked as current successfully',
                'data' => $updatedStudent
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to mark student as current: ' . $e->getMessage());
        }
    }

    /**
     * Mark student as dropped
     */
    public function markAsDropped($id = null)
    {
        try {
            $existingStudent = $this->model->find($id);
            if (!$existingStudent) {
                return $this->failNotFound('Student not found');
            }

            $success = $this->model->markAsDropped($id);
            
            if (!$success) {
                return $this->fail('Failed to mark student as dropped', 500);
            }

            $updatedStudent = $this->model->getStudentWithDetails($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Student marked as dropped successfully',
                'data' => $updatedStudent
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to mark student as dropped: ' . $e->getMessage());
        }
    }

    /**
     * Get current students
     */
    public function getCurrentStudents($departmentId = null)
    {
        try {
            $currentStudents = $this->model->getCurrentStudents($departmentId);

            return $this->respond([
                'status' => 'success',
                'data' => $currentStudents
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch current students: ' . $e->getMessage());
        }
    }

    /**
     * Get alumni
     */
    public function getAlumni($departmentId = null)
    {
        try {
            $alumni = $this->model->getAlumni($departmentId);

            return $this->respond([
                'status' => 'success',
                'data' => $alumni
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch alumni: ' . $e->getMessage());
        }
    }

    /**
     * Get students by department
     */
    public function getByDepartment($departmentId = null)
    {
        try {
            if (!$departmentId) {
                return $this->fail('Department ID is required', 400);
            }

            $students = $this->model->getByDepartment($departmentId);

            return $this->respond([
                'status' => 'success',
                'data' => $students
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch students: ' . $e->getMessage());
        }
    }

    /**
     * Get students by faculty
     */
    public function getByFaculty($facultyId = null)
    {
        try {
            if (!$facultyId) {
                return $this->fail('Faculty ID is required', 400);
            }

            $students = $this->model->getByFaculty($facultyId);

            return $this->respond([
                'status' => 'success',
                'data' => $students
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch students: ' . $e->getMessage());
        }
    }

    /**
     * Get departments list for dropdown
     */
    public function getDepartmentsList()
    {
        try {
            $departments = $this->model->getDepartmentsList();

            return $this->respond([
                'status' => 'success',
                'data' => $departments
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch departments: ' . $e->getMessage());
        }
    }

    /**
     * Get student statistics
     */
    public function getStatistics()
    {
        try {
            $statistics = $this->model->getStatistics();

            return $this->respond([
                'status' => 'success',
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch statistics: ' . $e->getMessage());
        }
    }

    /**
     * Get academic performance statistics
     */
    public function getAcademicStats()
    {
        try {
            $academicStats = $this->model->getAcademicStats();

            return $this->respond([
                'status' => 'success',
                'data' => $academicStats
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch academic statistics: ' . $e->getMessage());
        }
    }

    /**
     * Bulk mark students as alumni
     */
    public function bulkMarkAsAlumni()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data || !isset($data['student_ids']) || !is_array($data['student_ids'])) {
                return $this->fail('Student IDs array is required', 400);
            }

            $completionYear = isset($data['completion_year']) ? $data['completion_year'] : null;
            $successCount = 0;
            $failedIds = [];

            foreach ($data['student_ids'] as $studentId) {
                $success = $this->model->markAsAlumni($studentId, $completionYear);
                if ($success) {
                    $successCount++;
                } else {
                    $failedIds[] = $studentId;
                }
            }

            return $this->respond([
                'status' => 'success',
                'message' => "Successfully marked {$successCount} students as alumni",
                'success_count' => $successCount,
                'failed_ids' => $failedIds
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to bulk mark students as alumni: ' . $e->getMessage());
        }
    }

    // View Methods for CMS Interface

    /**
     * Students management view
     */
    public function manageStudentsView()
    {
        try {
            $searchTerm = $this->request->getGet('search');
            $page = (int)($this->request->getGet('page') ?? 1);
            $perPage = 15;
            
            if ($searchTerm) {
                // Use search functionality
                $students = $this->model->searchStudents($searchTerm);
                $total = count($students);
                
                // Apply pagination to search results
                $offset = ($page - 1) * $perPage;
                $students = array_slice($students, $offset, $perPage);
            } else {
                // Get all students with pagination
                $students = $this->model->getAllStudents();
                $total = count($students);
                
                // Apply pagination
                $offset = ($page - 1) * $perPage;
                $students = array_slice($students, $offset, $perPage);
            }
            
            $data = [
                'title' => 'Manage Students',
                'students' => $students,
                'departments' => $this->model->getDepartmentsList(),
                'statistics' => $this->model->getStatistics(),
                'searchTerm' => $searchTerm,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage)
                ]
            ];
            
            return view('students/manage_students', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load students: ' . $e->getMessage());
        }
    }

    /**
     * Create student view
     */
    public function createStudentView()
    {
        try {
            $data = [
                'title' => 'Add New Student',
                'departments' => $this->model->getDepartmentsList()
            ];
            
            return view('students/create_student', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load create form: ' . $e->getMessage());
        }
    }

    /**
     * Edit student view
     */
    public function editStudentView($id)
    {
        try {
            $student = $this->model->getStudentWithDetails($id);
            
            if (!$student) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Student not found');
            }

            $data = [
                'title' => 'Edit Student: ' . $student['full_name'],
                'student' => $student,
                'departments' => $this->model->getDepartmentsList()
            ];
            
            return view('students/edit_student', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load student: ' . $e->getMessage());
        }
    }

    /**
     * Current students view
     */
    public function currentStudentsView()
    {
        try {
            $data = [
                'title' => 'Current Students',
                'students' => $this->model->getCurrentStudents(),
                'departments' => $this->model->getDepartmentsList()
            ];
            
            return view('students/current_students', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load current students: ' . $e->getMessage());
        }
    }

    /**
     * Alumni view
     */
    public function alumniView()
    {
        try {
            $data = [
                'title' => 'Alumni',
                'alumni' => $this->model->getAlumni(),
                'departments' => $this->model->getDepartmentsList()
            ];
            
            return view('students/alumni', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load alumni: ' . $e->getMessage());
        }
    }

    /**
     * Student profile view (public)
     */
    public function studentProfileView($id)
    {
        try {
            $student = $this->model->getStudentWithDetails($id);
            
            if (!$student) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Student not found');
            }

            $data = [
                'title' => $student['full_name'],
                'student' => $student
            ];
            
            return view('students/student_profile', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load student profile: ' . $e->getMessage());
        }
    }

    /**
     * Academic statistics view
     */
    public function academicStatsView()
    {
        try {
            $data = [
                'title' => 'Academic Statistics',
                'statistics' => $this->model->getStatistics(),
                'academic_stats' => $this->model->getAcademicStats()
            ];
            
            return view('students/academic_stats', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load academic statistics: ' . $e->getMessage());
        }
    }
}