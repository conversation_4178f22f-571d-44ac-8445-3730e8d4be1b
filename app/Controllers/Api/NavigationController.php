<?php

namespace App\Controllers\Api;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class NavigationController extends BaseController
{
    protected $navigationService;

    public function __construct()
    {
        parent::__construct();
        $this->navigationService = new \App\Services\NavigationService();
    }

    /**
     * Standard success response format
     */
    protected function successResponse($data, $meta = [])
    {
        $response = [
            'success' => true,
            'data' => $data,
            'meta' => array_merge([
                'timestamp' => date('c'),
                'cached' => false,
                'cache_expires' => null
            ], $meta)
        ];

        return $this->response->setJSON($response);
    }

    /**
     * Standard error response format
     */
    protected function errorResponse($message, $code = 'GENERAL_ERROR', $statusCode = 400, $details = [])
    {
        $response = [
            'success' => false,
            'error' => [
                'code' => $code,
                'message' => $message,
                'details' => $details
            ],
            'meta' => [
                'timestamp' => date('c'),
                'request_id' => uniqid('req_')
            ]
        ];

        return $this->response->setStatusCode($statusCode)->setJSON($response);
    }

    /**
     * Validate and sanitize input parameters
     */
    protected function validateInput($rules, $data = null)
    {
        if ($data === null) {
            $data = $this->request->getGet();
        }

        $validation = \Config\Services::validation();
        $validation->setRules($rules);

        if (!$validation->run($data)) {
            return [
                'valid' => false,
                'errors' => $validation->getErrors()
            ];
        }

        return [
            'valid' => true,
            'data' => $validation->getValidated()
        ];
    }

    /**
     * Check rate limiting
     */
    protected function checkRateLimit($limit = 100, $window = 60)
    {
        $session = session();
        $currentTime = time();
        $requestCount = $session->get('api_request_count') ?? 0;
        $lastRequestTime = $session->get('api_last_request_time') ?? 0;

        // Reset counter if window has passed
        if ($currentTime - $lastRequestTime > $window) {
            $requestCount = 0;
        }

        if ($requestCount >= $limit) {
            return false;
        }

        // Update session data
        $session->set([
            'api_request_count' => $requestCount + 1,
            'api_last_request_time' => $currentTime
        ]);

        return true;
    }

    /**
     * Apply rate limiting middleware
     */
    protected function applyRateLimit()
    {
        if (!$this->checkRateLimit()) {
            return $this->errorResponse(
                'Rate limit exceeded. Please try again later.',
                'RATE_LIMIT_EXCEEDED',
                429,
                ['retry_after' => 60]
            );
        }
        return null;
    }

    /**
     * Sanitize string input
     */
    protected function sanitizeString($input)
    {
        return trim(strip_tags($input));
    }

    /**
     * Generate slug from name
     */
    protected function generateSlug($name)
    {
        return strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $name), '-'));
    }

    /**
     * Get complete faculty hierarchy with departments
     * GET /api/v1/navigation/faculties/hierarchy
     */
    public function getFacultyHierarchy()
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Get hierarchy data from service
            $hierarchyData = $this->navigationService->buildFacultyHierarchy();

            // Update meta information
            $meta = $hierarchyData['meta'];
            $meta['cached'] = isset($hierarchyData['cached']) ? $hierarchyData['cached'] : false;

            return $this->successResponse($hierarchyData['faculties'], $meta);

        } catch (\Exception $e) {
            log_message('error', 'Faculty hierarchy error: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine());
            
            return $this->errorResponse(
                'Failed to retrieve faculty hierarchy: ' . $e->getMessage(),
                'HIERARCHY_ERROR',
                500,
                [
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine()
                ]
            );
        }
    }

    /**
     * Simple test endpoint for debugging
     * GET /api/v1/navigation/test
     */
    public function test()
    {
        try {
            // Simple test to check if basic functionality works
            $facultyModel = new \App\Models\FacultyModel();
            $faculties = $facultyModel->select('id, faculty_name')->findAll();

            return $this->successResponse([
                'message' => 'Navigation API is working',
                'faculty_count' => count($faculties),
                'faculties' => $faculties
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Test failed: ' . $e->getMessage(),
                'TEST_ERROR',
                500
            );
        }
    }

    /**
     * Get complete faculty data with all related entities
     * GET /api/v1/navigation/faculties/{id}/complete
     */
    public function getFacultyComplete($id)
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Validate ID
            if (!is_numeric($id) || $id <= 0) {
                return $this->errorResponse(
                    'Invalid faculty ID provided',
                    'INVALID_FACULTY_ID',
                    400,
                    ['provided_id' => $id]
                );
            }

            // Check cache first
            $cacheKey = "navigation_faculty_complete_{$id}";
            $cached = $this->navigationService->getCachedData($cacheKey);
            
            if ($cached !== null) {
                $meta = [
                    'cached' => true,
                    'cache_expires' => date('c', time() + 1800)
                ];
                return $this->successResponse($cached, $meta);
            }

            // Get complete faculty data
            $facultyData = $this->navigationService->aggregateFacultyData($id);

            if (!$facultyData) {
                return $this->errorResponse(
                    "Faculty with ID {$id} not found",
                    'FACULTY_NOT_FOUND',
                    404,
                    ['requested_id' => $id]
                );
            }

            // Cache the result for 30 minutes
            $this->navigationService->cacheData($cacheKey, $facultyData, 1800);

            $meta = [
                'cached' => false,
                'cache_expires' => date('c', time() + 1800),
                'last_updated' => date('c')
            ];

            return $this->successResponse($facultyData, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Faculty complete data error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve faculty data',
                'FACULTY_DATA_ERROR',
                500
            );
        }
    }

    /**
     * Get complete department data with all related entities
     * GET /api/v1/navigation/departments/{id}/complete
     */
    public function getDepartmentComplete($id)
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Validate ID
            if (!is_numeric($id) || $id <= 0) {
                return $this->errorResponse(
                    'Invalid department ID provided',
                    'INVALID_DEPARTMENT_ID',
                    400,
                    ['provided_id' => $id]
                );
            }

            // Check cache first
            $cacheKey = "navigation_department_complete_{$id}";
            $cached = $this->navigationService->getCachedData($cacheKey);
            
            if ($cached !== null) {
                $meta = [
                    'cached' => true,
                    'cache_expires' => date('c', time() + 1800)
                ];
                return $this->successResponse($cached, $meta);
            }

            // Get complete department data
            $departmentData = $this->navigationService->aggregateDepartmentData($id);

            if (!$departmentData) {
                return $this->errorResponse(
                    "Department with ID {$id} not found",
                    'DEPARTMENT_NOT_FOUND',
                    404,
                    ['requested_id' => $id]
                );
            }

            // Cache the result for 30 minutes
            $this->navigationService->cacheData($cacheKey, $departmentData, 1800);

            $meta = [
                'cached' => false,
                'cache_expires' => date('c', time() + 1800),
                'last_updated' => date('c')
            ];

            return $this->successResponse($departmentData, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Department complete data error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve department data',
                'DEPARTMENT_DATA_ERROR',
                500
            );
        }
    }

    /**
     * Get complete faculty data by name
     * GET /api/v1/navigation/faculties/name/{name}/complete
     */
    public function getFacultyByName($name)
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Sanitize name input
            $name = $this->sanitizeString($name);
            
            if (empty($name)) {
                return $this->errorResponse(
                    'Faculty name cannot be empty',
                    'EMPTY_FACULTY_NAME',
                    400
                );
            }

            // Find faculty by name
            $facultyModel = new \App\Models\FacultyModel();
            $faculty = $facultyModel->findByNameSlug($name);

            if (!$faculty) {
                return $this->errorResponse(
                    "Faculty with name '{$name}' not found",
                    'FACULTY_NOT_FOUND',
                    404,
                    ['requested_name' => $name]
                );
            }

            // Check cache first
            $cacheKey = "navigation_faculty_complete_{$faculty['id']}";
            $cached = $this->navigationService->getCachedData($cacheKey);
            
            if ($cached !== null) {
                $meta = [
                    'cached' => true,
                    'cache_expires' => date('c', time() + 1800),
                    'matched_by' => 'name'
                ];
                return $this->successResponse($cached, $meta);
            }

            // Get complete faculty data
            $facultyData = $this->navigationService->aggregateFacultyData($faculty['id']);

            if (!$facultyData) {
                return $this->errorResponse(
                    "Failed to retrieve data for faculty '{$name}'",
                    'FACULTY_DATA_ERROR',
                    500
                );
            }

            // Cache the result for 30 minutes
            $this->navigationService->cacheData($cacheKey, $facultyData, 1800);

            $meta = [
                'cached' => false,
                'cache_expires' => date('c', time() + 1800),
                'last_updated' => date('c'),
                'matched_by' => 'name'
            ];

            return $this->successResponse($facultyData, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Faculty by name error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve faculty data by name',
                'FACULTY_NAME_ERROR',
                500
            );
        }
    }

    /**
     * Get complete department data by name
     * GET /api/v1/navigation/departments/name/{name}/complete
     */
    public function getDepartmentByName($name)
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Sanitize name input
            $name = $this->sanitizeString($name);
            
            if (empty($name)) {
                return $this->errorResponse(
                    'Department name cannot be empty',
                    'EMPTY_DEPARTMENT_NAME',
                    400
                );
            }

            // Find department by name
            $departmentsModel = new \App\Models\DepartmentsModel();
            $department = $departmentsModel->findByNameSlug($name);

            if (!$department) {
                return $this->errorResponse(
                    "Department with name '{$name}' not found",
                    'DEPARTMENT_NOT_FOUND',
                    404,
                    ['requested_name' => $name]
                );
            }

            // Check cache first
            $cacheKey = "navigation_department_complete_{$department['id']}";
            $cached = $this->navigationService->getCachedData($cacheKey);
            
            if ($cached !== null) {
                $meta = [
                    'cached' => true,
                    'cache_expires' => date('c', time() + 1800),
                    'matched_by' => 'name'
                ];
                return $this->successResponse($cached, $meta);
            }

            // Get complete department data
            $departmentData = $this->navigationService->aggregateDepartmentData($department['id']);

            if (!$departmentData) {
                return $this->errorResponse(
                    "Failed to retrieve data for department '{$name}'",
                    'DEPARTMENT_DATA_ERROR',
                    500
                );
            }

            // Cache the result for 30 minutes
            $this->navigationService->cacheData($cacheKey, $departmentData, 1800);

            $meta = [
                'cached' => false,
                'cache_expires' => date('c', time() + 1800),
                'last_updated' => date('c'),
                'matched_by' => 'name'
            ];

            return $this->successResponse($departmentData, $meta);

        } catch (\Exception $e) {
            log_message('error', 'Department by name error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve department data by name',
                'DEPARTMENT_NAME_ERROR',
                500
            );
        }
    }

    /**
     * Get university statistics overview
     * GET /api/v1/navigation/statistics/overview
     */
    public function getUniversityStatistics()
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Check cache first
            $cacheKey = "navigation_university_stats";
            $cached = $this->navigationService->getCachedData($cacheKey);
            
            if ($cached !== null) {
                $meta = [
                    'cached' => true,
                    'cache_expires' => date('c', time() + 21600) // 6 hours
                ];
                return $this->successResponse($cached, $meta);
            }

            // Calculate university statistics
            $statistics = $this->navigationService->calculateUniversityStatistics();

            // Cache the result for 6 hours
            $this->navigationService->cacheData($cacheKey, $statistics, 21600);

            $meta = [
                'cached' => false,
                'cache_expires' => date('c', time() + 21600),
                'generated_at' => date('c')
            ];

            return $this->successResponse($statistics, $meta);

        } catch (\Exception $e) {
            log_message('error', 'University statistics error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve university statistics',
                'STATISTICS_ERROR',
                500
            );
        }
    }

    /**
     * Get filtered faculty members
     * GET /api/v1/navigation/filters/faculty-members
     */
    public function getFilteredFacultyMembers()
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Validate and get filters
            $validation = $this->validateInput([
                'faculty_id' => 'permit_empty|integer',
                'department_id' => 'permit_empty|integer',
                'designation' => 'permit_empty|in_list[Professor,Associate Professor,Assistant Professor,Lecturer]',
                'qualification' => 'permit_empty|in_list[PhD,MPhil,Master\'s,Bachelor\'s]',
                'page' => 'permit_empty|integer',
                'limit' => 'permit_empty|integer|max_length[100]'
            ]);

            if (!$validation['valid']) {
                return $this->errorResponse(
                    'Invalid filter parameters',
                    'INVALID_FILTERS',
                    400,
                    $validation['errors']
                );
            }

            $filters = $validation['data'];
            $page = $filters['page'] ?? 1;
            $limit = $filters['limit'] ?? 20;

            // Get filtered faculty members
            $result = $this->navigationService->getFilteredFacultyMembers($filters, $page, $limit);

            $meta = [
                'page' => $page,
                'limit' => $limit,
                'total' => $result['total'],
                'total_pages' => ceil($result['total'] / $limit),
                'filters_applied' => array_filter($filters)
            ];

            return $this->successResponse($result['data'], $meta);

        } catch (\Exception $e) {
            log_message('error', 'Filtered faculty members error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve filtered faculty members',
                'FILTER_ERROR',
                500
            );
        }
    }

    /**
     * Get filtered students
     * GET /api/v1/navigation/filters/students
     */
    public function getFilteredStudents()
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Validate and get filters
            $validation = $this->validateInput([
                'faculty_id' => 'permit_empty|integer',
                'department_id' => 'permit_empty|integer',
                'program_type' => 'permit_empty|in_list[Bachelor\'s,Master\'s,MPhil,PhD]',
                'status' => 'permit_empty|in_list[current,alumni,dropped]',
                'admission_year' => 'permit_empty|integer',
                'page' => 'permit_empty|integer',
                'limit' => 'permit_empty|integer|max_length[100]'
            ]);

            if (!$validation['valid']) {
                return $this->errorResponse(
                    'Invalid filter parameters',
                    'INVALID_FILTERS',
                    400,
                    $validation['errors']
                );
            }

            $filters = $validation['data'];
            $page = $filters['page'] ?? 1;
            $limit = $filters['limit'] ?? 20;

            // Get filtered students
            $result = $this->navigationService->getFilteredStudents($filters, $page, $limit);

            $meta = [
                'page' => $page,
                'limit' => $limit,
                'total' => $result['total'],
                'total_pages' => ceil($result['total'] / $limit),
                'filters_applied' => array_filter($filters)
            ];

            return $this->successResponse($result['data'], $meta);

        } catch (\Exception $e) {
            log_message('error', 'Filtered students error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve filtered students',
                'FILTER_ERROR',
                500
            );
        }
    }

    /**
     * Get filtered departments
     * GET /api/v1/navigation/filters/departments
     */
    public function getFilteredDepartments()
    {
        try {
            // Apply rate limiting
            $rateLimitResponse = $this->applyRateLimit();
            if ($rateLimitResponse) {
                return $rateLimitResponse;
            }

            // Validate and get filters
            $validation = $this->validateInput([
                'faculty_id' => 'permit_empty|integer',
                'page' => 'permit_empty|integer',
                'limit' => 'permit_empty|integer|max_length[100]'
            ]);

            if (!$validation['valid']) {
                return $this->errorResponse(
                    'Invalid filter parameters',
                    'INVALID_FILTERS',
                    400,
                    $validation['errors']
                );
            }

            $filters = $validation['data'];
            $page = $filters['page'] ?? 1;
            $limit = $filters['limit'] ?? 20;

            // Get filtered departments
            $result = $this->navigationService->getFilteredDepartments($filters, $page, $limit);

            $meta = [
                'page' => $page,
                'limit' => $limit,
                'total' => $result['total'],
                'total_pages' => ceil($result['total'] / $limit),
                'filters_applied' => array_filter($filters)
            ];

            return $this->successResponse($result['data'], $meta);

        } catch (\Exception $e) {
            log_message('error', 'Filtered departments error: ' . $e->getMessage());
            
            return $this->errorResponse(
                'Failed to retrieve filtered departments',
                'FILTER_ERROR',
                500
            );
        }
    }
}