<?php

namespace App\Controllers\Api\v1;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;

class HomepageController extends ResourceController
{
    use ResponseTrait;

    public function headerTopbar()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_top_mini');
        $row = $builder->get()->getRow();
    
        if ($row && isset($row->data)) {
            $decoded = json_decode($row->data, true);
            // If you want just the fields array:
            $fields = $decoded['fields'] ?? [];
            return $this->respond([
                'status' => 'success',
                'headertopdata' => $fields
            ]);
        } else {
            return $this->respond([
                'status' => 'success',
                'headertopdata' => []
            ]);
        }
    }

    public function midbar()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_mid_bar');
        $row = $builder->get()->getRow();
    
        if ($row && isset($row->data)) {
            $decoded = json_decode($row->data, true);
            // If you want just the fields array:
            $fields = $decoded['fields'] ?? [];
            return $this->respond([
                'status' => 'success',
                'headermiddata' => $fields
            ]);
        } else {
            return $this->respond([
                'status' => 'success',
                'headermiddata' => []
            ]);
        }
    }

    public function heroSection()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('header_hero_section');
            $heroBanners = $builder->orderBy('id', 'DESC')->get()->getResultArray();

            // Add full image URL to each banner
            $baseUrl = base_url('uploads/hero/');
            foreach ($heroBanners as &$banner) {
                if (!empty($banner['banner'])) {
                    $banner['image_url'] = $baseUrl . $banner['banner'];
                } else {
                    $banner['image_url'] = null;
                }
            }

            return $this->respond([
                'status' => 'success',
                'hero_banners' => $heroBanners
            ]);
        }
        public function footerSection()
        {
            $categoryModel = new \App\Models\FooterSettingsCategoryModel();
            $settingsModel = new \App\Models\FooterSettingsModel();

            // Get all categories
            $categories = $categoryModel->findAll();
            $footerData = [];

            foreach ($categories as $category) {
                // Get all links for this category
                $links = $settingsModel
                    ->where('footer_category', $category['id'])
                    ->select('meta_title, meta_link')
                    ->findAll();
                $footerData[] = [
                    'category_id' => $category['id'],
                    'category_name' => $category['category_name'],
                    'links' => $links
                ];
            }

            return $this->respond([
                'status' => 'success',
                'footer' => $footerData
            ]);
        }
    
        public function marqueeNotifications()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('notices');
            $notices = $builder
                ->select('title, description, priority')
                ->where('isLatest', 1)
                ->get()
                ->getResultArray();

            // Map to required structure
            $result = array_map(function($notice) {
                return [
                    'Title' => $notice['title'],
                    'Heading' => $notice['description'],
                    'priority' => $notice['priority']
                ];
            }, $notices);

            return $this->respond([
                'status' => 'success',
                'notices' => $result
            ]);
        }    

        public function notifications()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('notices');
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function generalNotifications()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('notices');
            $notices = $builder
                ->select('title, description, files, created_at, url_link')
                ->where('notice_type', 'general')
                ->get()
                ->getResultArray();

            // Map to required structure
            $result = array_map(function($notice) {
                return [
                    'title' => $notice['title'],
                    'description' => $notice['description'],
                    'files' => $notice['files'],
                    'url_link' => $notice['url_link'], // Set to null as url_link does not exist in the table
                    'created_at' => $notice['created_at']
                ];
            }, $notices);

            return $this->respond([
                'status' => 'success',
                'notices' => $result
            ]);
        }

        public function resultNotifications()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('notices');
            $notices = $builder
                ->select('title, description, files, created_at, url_link')
                ->where('notice_type', 'result')
                ->get()
                ->getResultArray();

            // Map to required structure
            $result = array_map(function($notice) {
                return [
                    'title' => $notice['title'],
                    'description' => $notice['description'],
                    'files' => $notice['files'],
                    'url_link' => $notice['url_link'], // Set to null as url_link does not exist in the table
                    'created_at' => $notice['created_at']
                ];
            }, $notices);

            return $this->respond([
                'status' => 'success',
                'notices' => $result
            ]);
        }

        public function impLinksNotifications()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('notices');
            $notices = $builder
                ->select('title, description, created_at, url_link')
                ->where('notice_type', 'important')
                ->get()
                ->getResultArray();

            // Map to required structure
            $result = array_map(function($notice) {
                return [
                    'title' => $notice['title'],
                    'description' => $notice['description'],
                    'url_link' => $notice['url_link'], // Set to null as url_link does not exist in the table
                    'created_at' => $notice['created_at']
                ];
            }, $notices);

            return $this->respond([
                'status' => 'success',
                'notices' => $result
            ]);
        }
        
        public function shortcutSection()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('shortcuts');
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function upcomingEvents()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('events_upcoming');
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function newsEvents()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('events_news-events');
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function administrations()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('administrations');
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }
        
        public function administrationShortcuts()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('administrations_shortcuts'); //curently Not Implemented
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function studentsCorner()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('imp_lin_students_corner'); 
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }
        public function importantInformation()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('imp_lin_impoinformation'); 
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }
        public function onlineServices()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('imp_lin_onlineservices'); 
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }
        public function facilitiesSection()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('facilities_amenities'); 
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function gallerySection()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('gallery'); 
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }

        public function academicsSection()
        {
            $db = \Config\Database::connect();
            $builder = $db->table('academicsSection'); 
            $notices = $builder->get()->getResultArray();

            return $this->respond([
                'status' => 'success',
                'notices' => $notices
            ]);
        }


    }

