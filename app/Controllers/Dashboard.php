<?php

namespace App\Controllers;


class Dashboard extends BaseController {

    /**
     * File Manager: List, upload, delete, and rename files in all upload directories.
     */
    public function fileManager()
    {
        $baseUploadPath = FCPATH . 'uploads/';
        
        // Ensure uploads directory exists
        if (!is_dir($baseUploadPath)) {
            mkdir($baseUploadPath, 0777, true);
        }

        // Handle file upload (AJAX)
        if ($this->request->getMethod() === 'post') {
            try {
                // Set JSON response header
                $this->response->setContentType('application/json');
                
                // Handle multiple file uploads
                $files = $this->request->getFiles();
                $targetDir = $this->request->getPost('path') ?? $this->request->getPost('target_dir') ?? 'general';
                
                // Clean up the target directory path
                $targetDir = trim($targetDir, '/');
                if (!empty($targetDir)) {
                    $uploadPath = $baseUploadPath . $targetDir . '/';
                } else {
                    $uploadPath = $baseUploadPath;
                }
                
                // Ensure target directory exists
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0777, true);
                }
                
                $uploadedFiles = [];
                $errors = [];
                
                // Debug information
                log_message('debug', 'Upload attempt - Target dir: ' . $targetDir . ', Upload path: ' . $uploadPath);
                log_message('debug', 'Files received: ' . json_encode(array_keys($files)));
                
                if (isset($files['files'])) {
                    foreach ($files['files'] as $file) {
                        log_message('debug', 'Processing file: ' . $file->getClientName() . ', Valid: ' . ($file->isValid() ? 'yes' : 'no') . ', Moved: ' . ($file->hasMoved() ? 'yes' : 'no'));
                        
                        if ($file->isValid() && !$file->hasMoved()) {
                            try {
                                // Generate unique filename to prevent conflicts
                                $originalName = $file->getClientName();
                                $extension = $file->getClientExtension();
                                $newName = pathinfo($originalName, PATHINFO_FILENAME) . '_' . time() . '.' . $extension;
                                
                                log_message('debug', 'Attempting to move file to: ' . $uploadPath . $newName);
                                
                                $file->move($uploadPath, $newName);
                                $uploadedFiles[] = $newName;
                                
                                log_message('debug', 'File uploaded successfully: ' . $newName);
                            } catch (\Exception $e) {
                                $error = 'Failed to upload ' . $file->getClientName() . ': ' . $e->getMessage();
                                $errors[] = $error;
                                log_message('error', $error);
                            }
                        } else {
                            $error = 'Invalid file: ' . $file->getClientName() . ' - ' . $file->getErrorString();
                            $errors[] = $error;
                            log_message('error', $error);
                        }
                    }
                } else {
                    $errors[] = 'No files received in the request';
                    log_message('error', 'No files array found in request');
                }
                
                if (!empty($uploadedFiles)) {
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => count($uploadedFiles) . ' file(s) uploaded successfully to ' . $targetDir . ' directory',
                        'uploaded_files' => $uploadedFiles,
                        'errors' => $errors
                    ]);
                } else {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'No files were uploaded',
                        'errors' => $errors
                    ]);
                }
            } catch (\Exception $e) {
                log_message('error', 'File upload exception: ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Upload failed: ' . $e->getMessage(),
                    'errors' => [$e->getMessage()]
                ]);
            }
        }

        // Get current path from query parameter
        $currentPath = $this->request->getGet('path') ?? '';
        $currentPath = trim($currentPath, '/');
        
        // Get view and sort parameters
        $view = $this->request->getGet('view') ?? 'grid';
        $sort = $this->request->getGet('sort') ?? 'name';
        $order = $this->request->getGet('order') ?? 'asc';
        
        // Build breadcrumbs
        $breadcrumbs = [['name' => 'Home', 'path' => '']];
        if (!empty($currentPath)) {
            $pathParts = explode('/', $currentPath);
            $buildPath = '';
            foreach ($pathParts as $part) {
                $buildPath .= ($buildPath ? '/' : '') . $part;
                $breadcrumbs[] = ['name' => $part, 'path' => $buildPath];
            }
        }
        
        // Get storage info
        $totalSpace = disk_total_space(FCPATH);
        $freeSpace = disk_free_space(FCPATH);
        $usedSpace = $totalSpace - $freeSpace;
        $storageInfo = [
            'total' => $totalSpace,
            'used' => $usedSpace,
            'free' => $freeSpace,
            'usage_percent' => round(($usedSpace / $totalSpace) * 100, 2)
        ];
        
        // Get files in current directory
        $currentDir = $baseUploadPath . $currentPath;
        $files = [];
        
        if (is_dir($currentDir)) {
            foreach (scandir($currentDir) as $item) {
                if ($item === '.' || $item === '..') continue;
                $fullPath = $currentDir . '/' . $item;
                
                if (is_dir($fullPath)) {
                    $files[] = [
                        'name' => $item,
                        'type' => 'folder',
                        'icon' => 'zmdi-folder',
                        'size' => 0,
                        'modified' => filemtime($fullPath)
                    ];
                } else {
                    $extension = strtolower(pathinfo($item, PATHINFO_EXTENSION));
                    $icon = $this->getFileIcon($extension);
                    
                    $files[] = [
                        'name' => $item,
                        'type' => 'file',
                        'extension' => $extension,
                        'icon' => $icon,
                        'size' => filesize($fullPath),
                        'modified' => filemtime($fullPath)
                    ];
                }
            }
            
            // Sort files
            usort($files, function($a, $b) use ($sort, $order) {
                $result = 0;
                switch ($sort) {
                    case 'name':
                        $result = strcasecmp($a['name'], $b['name']);
                        break;
                    case 'size':
                        $result = $a['size'] - $b['size'];
                        break;
                    case 'modified':
                        $result = $a['modified'] - $b['modified'];
                        break;
                }
                return $order === 'desc' ? -$result : $result;
            });
        }

        return view('file_manager/index', [
            'currentPath' => $currentPath,
            'breadcrumbs' => $breadcrumbs,
            'storageInfo' => $storageInfo,
            'files' => $files,
            'view' => $view,
            'sort' => $sort,
            'order' => $order
        ]);
    }

    /**
     * Create folder in file manager
     */
    public function createFolder()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }

        if ($this->request->getMethod() === 'post') {
            try {
                // Set JSON response header
                $this->response->setContentType('application/json');
            $folderName = $this->request->getPost('name');
            $path = $this->request->getPost('path') ?? '';
            
            if (empty($folderName)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Folder name is required'
                ]);
            }

            // Sanitize folder name
            $folderName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $folderName);
            
            // Make folder name unique if it already exists
            $originalFolderName = $folderName;
            $counter = 1;
            
            $baseUploadPath = FCPATH . 'uploads/';
            $path = trim($path, '/');
            
            // Generate unique folder name
            do {
                if (!empty($path)) {
                    $fullPath = $baseUploadPath . $path . '/' . $folderName;
                } else {
                    $fullPath = $baseUploadPath . $folderName;
                }
                
                if (is_dir($fullPath)) {
                    $folderName = $originalFolderName . '_' . $counter;
                    $counter++;
                } else {
                    break;
                }
            } while ($counter <= 100); // Prevent infinite loop
            
            log_message('debug', 'Creating folder: ' . $fullPath);

                if (mkdir($fullPath, 0777, true)) {
                    log_message('debug', 'Folder created successfully: ' . $fullPath);
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'Folder "' . $folderName . '" created successfully'
                    ]);
                } else {
                    log_message('error', 'Failed to create folder: ' . $fullPath);
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Failed to create folder'
                    ]);
                }
            } catch (\Exception $e) {
                log_message('error', 'Create folder exception: ' . $e->getMessage());
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Create folder failed: ' . $e->getMessage()
                ]);
            }
        }

        return $this->response->setStatusCode(405)->setJSON([
            'success' => false,
            'message' => 'Method not allowed'
        ]);
    }

    /**
     * Delete files/folders in file manager - DISABLED FOR SECURITY
     */
    public function deleteFiles()
    {
        return $this->response->setStatusCode(403)->setJSON([
            'success' => false,
            'message' => 'Delete operation is disabled for security reasons'
        ]);
    }

    /**
     * Organize uploaded file from MediaController to target directory
     */
    public function organizeFile()
    {
        try {
            $this->response->setContentType('application/json');
            
            $data = $this->request->getJSON(true);
            
            if (!isset($data['sourcePath']) || !isset($data['targetPath']) || !isset($data['fileName'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Missing required parameters'
                ]);
            }
            
            $sourcePath = FCPATH . $data['sourcePath'];
            
            // Handle empty target path (root uploads directory)
            $targetPathParam = trim($data['targetPath'], '/');
            if (empty($targetPathParam)) {
                $targetDir = 'uploads/';
            } else {
                $targetDir = 'uploads/' . $targetPathParam . '/';
            }
            
            $targetPath = FCPATH . $targetDir;
            $fileName = $data['fileName'];
            
            // Create target directory if it doesn't exist
            if (!is_dir($targetPath)) {
                mkdir($targetPath, 0777, true);
            }
            
            // Move file from source to target
            $targetFile = $targetPath . $fileName;
            
            if (file_exists($sourcePath)) {
                if (rename($sourcePath, $targetFile)) {
                    return $this->response->setJSON([
                        'success' => true,
                        'message' => 'File organized successfully',
                        'targetPath' => $targetDir . $fileName
                    ]);
                } else {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Failed to move file'
                    ]);
                }
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Source file not found'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'File organization error: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Organization failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get file information
     */
    public function getFileInfo()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }

        $fileName = $this->request->getGet('file');
        $path = $this->request->getGet('path') ?? '';
        
        if (empty($fileName)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File name is required'
            ]);
        }

        $baseUploadPath = FCPATH . 'uploads/';
        $fullPath = $baseUploadPath . $path . '/' . $fileName;
        
        if (!is_file($fullPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found'
            ]);
        }

        $fileInfo = [
            'name' => $fileName,
            'size' => filesize($fullPath),
            'type' => mime_content_type($fullPath),
            'modified' => filemtime($fullPath),
            'url' => base_url('uploads/' . $path . '/' . $fileName)
        ];

        // Add image dimensions if it's an image
        if (strpos($fileInfo['type'], 'image/') === 0) {
            $imageInfo = getimagesize($fullPath);
            if ($imageInfo) {
                $fileInfo['dimensions'] = $imageInfo[0] . ' x ' . $imageInfo[1];
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'info' => $fileInfo
        ]);
    }



    /**
     * Get file icon based on extension
     */
    private function getFileIcon($extension)
    {
        $iconMap = [
            // Images
            'jpg' => 'zmdi-image',
            'jpeg' => 'zmdi-image',
            'png' => 'zmdi-image',
            'gif' => 'zmdi-image',
            'webp' => 'zmdi-image',
            'svg' => 'zmdi-image',
            
            // Documents
            'pdf' => 'zmdi-file-text',
            'doc' => 'zmdi-file-text',
            'docx' => 'zmdi-file-text',
            'txt' => 'zmdi-file-text',
            'rtf' => 'zmdi-file-text',
            
            // Spreadsheets
            'xls' => 'zmdi-grid',
            'xlsx' => 'zmdi-grid',
            'csv' => 'zmdi-grid',
            
            // Presentations
            'ppt' => 'zmdi-slideshow',
            'pptx' => 'zmdi-slideshow',
            
            // Archives
            'zip' => 'zmdi-archive',
            'rar' => 'zmdi-archive',
            '7z' => 'zmdi-archive',
            'tar' => 'zmdi-archive',
            'gz' => 'zmdi-archive',
            
            // Audio
            'mp3' => 'zmdi-music',
            'wav' => 'zmdi-music',
            'flac' => 'zmdi-music',
            'aac' => 'zmdi-music',
            
            // Video
            'mp4' => 'zmdi-videocam',
            'avi' => 'zmdi-videocam',
            'mkv' => 'zmdi-videocam',
            'mov' => 'zmdi-videocam',
            'wmv' => 'zmdi-videocam',
            
            // Code
            'php' => 'zmdi-code',
            'js' => 'zmdi-code',
            'css' => 'zmdi-code',
            'html' => 'zmdi-code',
            'json' => 'zmdi-code',
            'xml' => 'zmdi-code'
        ];
        
        return $iconMap[$extension] ?? 'zmdi-file';
    }

    /**
     * Format file size for display
     */
    private function formatFileSize($bytes)
    {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }

    /**
     * Rename file/folder in file manager - DISABLED FOR SECURITY
     */
    public function renameFile()
    {
        return $this->response->setStatusCode(403)->setJSON([
            'success' => false,
            'message' => 'Rename operation is disabled for security reasons'
        ]);
    }

    /**
     * Recursively scan files in a directory and return info array
     */
    private function scanFilesRecursive($dir, $base = '')
    {
        $result = [];
        $rii = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($dir, \FilesystemIterator::SKIP_DOTS));
        foreach ($rii as $file) {
            if ($file->isFile()) {
                $relativePath = ltrim(str_replace(FCPATH . 'uploads/', '', $file->getPathname()), '/');
                $result[] = $this->fileInfoArray($file->getPathname(), $relativePath);
            }
        }
        return $result;
    }

    /**
     * Get file info as array
     */
    private function fileInfoArray($fullPath, $relativePath)
    {
        return [
            'name' => basename($fullPath),
            'src' => 'uploads/' . $relativePath,
            'date' => date('Y-m-d H:i:s', filemtime($fullPath)),
        ];
    }
    // public function index()
    // {
    //     return view('dashboard', ['title' => 'Admin Dashboard']);
    // }
    public function index()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        // Disk Usage
        $totalSpace = disk_total_space("/");
        $freeSpace = disk_free_space("/");
        $diskUsedPercent = round((($totalSpace - $freeSpace) / $totalSpace) * 100, 2);

        // Memory Usage
        $memoryUsedPercent = null;
        if (function_exists('memory_get_usage')) {
            $totalMemory = @memory_get_usage(true);
            $peakMemory = @memory_get_peak_usage(true);

            if ($peakMemory > 0) {
                $memoryUsedPercent = round(($totalMemory / $peakMemory) * 100, 2);
            }
        }

        // Server Uptime
        $uptimeFormatted = 'Unavailable';
        if (is_readable('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptimeSeconds = (int) explode(' ', $uptime)[0];

            $days = floor($uptimeSeconds / 86400);
            $hours = floor(($uptimeSeconds % 86400) / 3600);
            $minutes = floor(($uptimeSeconds % 3600) / 60);

            $uptimeFormatted = "{$days}d {$hours}h {$minutes}m";
        }

        // CPU Load
        $cpuLoadPercent = 0;
        $cpuLoadText = 'Unavailable';
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            if ($load && count($load) > 0) {
                $cpuLoadPercent = round(($load[0] / 4) * 100, 2); // Assuming 4 cores
                $cpuLoadText = $cpuLoadPercent . '%';
            }
        }

        return view('dashboard', [
            'diskUsedPercent' => $diskUsedPercent,
            'memoryUsedPercent' => $memoryUsedPercent,
            'uptimeFormatted' => $uptimeFormatted,
            'cpuLoadPercent' => $cpuLoadPercent,
            'cpuLoadText' => $cpuLoadText,
        ]);
    }
    public function forms()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('forms', ['title' => 'Admin Dashboard']);
    }
    public function tenders()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll(); // Fetch all departments
        return view('tenders', ['title' => 'Admin Tenders', 'departments' => $data['departments']]);
    }
    public function events()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('events', ['title' => 'Admin Events']);
    }
    public function notices()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll(); // Fetch all departments
        return view('notices', ['title' => 'Admin Notices', 'departments' => $data['departments']]);
    }
    public function departments()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll();
        $data['title'] = 'Admin Departments';

        return view('departments', $data);
    }
    public function peoples()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll(); // Fetch all departments    
        return view('peoples', ['title' => 'Admin Peoples', 'departments' => $data['departments']]);
    }
    public function abouttac()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->aboutModel = new \App\Models\AboutModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->aboutModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view
        return view('abouttac', [
            'title' => 'Admin About TAC',
            'aboutData' => $aboutData // Pass the data to the view
        ]);
    }
    public function abouttiwa()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->abouttiwaModel = new \App\Models\AbouttiwaModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->abouttiwaModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view
        return view('abouttiwa', [
            'title' => 'Admin About Tiwa Community',
            'aboutData' => $aboutData // Pass the data to the view
        ]);
    }
    public function addtiwacomunitydress()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->abouttiwaModel = new \App\Models\AbouttiwaModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->abouttiwaModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view

    }
    public function abouttacact()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->abouttacactModel = new \App\Models\AbouttacactModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->abouttacactModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view
        return view('abouttacact', [
            'title' => 'Admin About TAC ACT & RULES',
            'aboutData' => $aboutData // Pass the data to the view
        ]);
    }

    public function contacts()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->contactModel = new \App\Models\ContactModel();

        // Fetch all the contact data
        $contactData = $this->contactModel->findAll(); // Fetch all contact data

        // Pass the data to the view
        return view('contacts', [
            'title' => 'Admin Contacts',
            'contactData' => $contactData // Pass the data to the view
        ]);
    }
    public function archives()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        // Load the ArchiveModel and ArchiveCategoryModel
        $this->archiveModel = new \App\Models\ArchiveModel();
        $this->archiveCategoryModel = new \App\Models\ArchiveCategoryModel();

        // Fetch all the archive data
        $archiveData = $this->archiveModel->findAll(); // Fetch all archive data
        // Fetch all the archive categories
        $archiveCategories = $this->archiveCategoryModel->findAll(); // Fetch all archive categories

        // Pass the data to the view
        return view('archives', [
            'title' => 'Admin Archives',
            'archiveData' => $archiveData, // Pass archive data to the view
            'archiveCategories' => $archiveCategories // Pass archive categories to the view
        ]);
    }
    public function galleries()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }

        // Fetch all the archive data
    $galleryModel = new \App\Models\GalleryModel();
        $eventModel = new \App\Models\EventModel();
    // Build the query with join
    $galleryData = $galleryModel
        ->select('tac_galleries.*, tac_events.event_name')
        ->join('tac_events', 'tac_events.id = tac_galleries.event_id')
        ->orderBy('tac_galleries.id', 'DESC')
        ->findAll();

        $eventData = $eventModel->findAll();

        // Pass the data to the view
        return view('galleries', [
            'title' => 'Admin Galleries',
            'eventData' => $eventData, // Pass gallery data to the view
          
        ]);
    }

    public function logoUpdate()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        // Fetch the logo data using the method from the model
        $coreModel = new \App\Models\CoreModel();
        $logoData = $coreModel->getLogoSettings(); // Assuming this method returns the logo settings data

        $logoUrl = '';
        $logoWidth = 'auto';
        $logoHeight = 'auto';

        if (!empty($logoData['settings_data'])) {
            $settings = json_decode($logoData['settings_data'], true);

            $logoUrl = !empty($settings['logo_url']) ? $settings['logo_url'] : '';
            $logoWidth = !empty($settings['logo_width']) ? $settings['logo_width'] : 'auto';
            $logoHeight = !empty($settings['logo_height']) ? $settings['logo_height'] : 'auto';
        }

        return view('logoUpdate', [
            'title' => 'Admin Logo Update',
            'logoUrl' => $logoUrl,
            'logoWidth' => $logoWidth,
            'logoHeight' => $logoHeight
        ]);
    }

    public function bgUpdate()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $coreModel = new \App\Models\CoreModel();
        $logoData = $coreModel->getBackgroundSettings(); // Fetch settings

        $fileUrl = '';
        $fileType = '';
        $logoWidth = 'auto';
        $logoHeight = 'auto';

        if (!empty($logoData['settings_data'])) {
            $settings = json_decode($logoData['settings_data'], true);

            $fileUrl = !empty($settings['file_url']) ? $settings['file_url'] : '';
            $fileType = !empty($settings['file_type']) ? $settings['file_type'] : '';

            // Set default dimensions based on file type
            if (strpos($fileType, 'image/') === 0) {
                $logoWidth = 200;
                $logoHeight = 200;
            } elseif (strpos($fileType, 'video/') === 0) {
                $logoWidth = 320;
                $logoHeight = 180;
            }
        }

        return view('bgUpdate', [
            'title'      => 'Admin website Background Update',
            'fileUrl'    => $fileUrl,
            'fileType'   => $fileType,
            'logoWidth'  => $logoWidth,
            'logoHeight' => $logoHeight,
        ]);
    }

    public function socialLink()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('socialLink', ['title' => 'Admin Social Links']);
    }
    public function aboutStaff()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('aboutStaff', ['title' => 'Admin About Staff']);
    }







    // all page ajax fetch functions starts here

    public function fetchDepartments()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\DepartmentModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $departments = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $departments,
            'total' => $total
        ]);
    }
    public function fetchTenders()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\TenderModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        // Build the query with join
        $tenders = $model
            ->select('tac_tenders.*, tac_departments.department_name')
            ->join('tac_departments', 'tac_departments.id = tac_tenders.tender_department')
            ->orderBy('tac_tenders.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $tenders,
            'total' => $total
        ]);
    }

    public function fetchNotices()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\NoticeModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        // Build the query with join
        $notices = $model
            ->select('tac_notices.*, tac_departments.department_name')
            ->join('tac_departments', 'tac_departments.id = tac_notices.notice_department')
            ->orderBy('tac_notices.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $notices,
            'total' => $total
        ]);
    }
    public function fetchEvents()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\EventModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $departments = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $departments,
            'total' => $total
        ]);
    }

    public function fetchPeoples()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\PeopleModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $peoples = $model
            ->select('tac_peoples.*, tac_departments.department_name')
            ->join('tac_departments', 'tac_departments.id = tac_peoples.person_department')
            ->orderBy('tac_peoples.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $peoples,
            'total' => $total
        ]);
    }

    public function fetchArchives()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\ArchiveModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $archives = $model
            ->select('tac_archives.*, tac_archive_category.category_name')
            ->join('tac_archive_category', 'tac_archive_category.id = tac_archives.archive_category')
            ->orderBy('tac_archives.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $archives,
            'total' => $total
        ]);
    }

    function fetchAboutStaff()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\AboutStaffModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $staffs = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $staffs,
            'total' => $total
        ]);
    }


    public function fetchGallery()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\GalleryModel();
        $model->select('tac_galleries.*, tac_events.event_name')
            ->join('tac_events', 'tac_events.id = tac_galleries.event_id');
        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $galleryData = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $galleryData,
            'total' => $total
        ]);
    }



    // all page ajax fetch functions ends here





    public function createDepartment()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }

        $departmentName = $this->request->getPost('department_name');

        if (empty($departmentName)) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Department name is required']);
        }

        $data = [
            'department_name'     => $departmentName,
            'department_created'  => date('Y-m-d'),
            'created_by'          => 'admin' // or dynamic value from session
        ];

        $db = \Config\Database::connect();
        $builder = $db->table('tac_departments');
        $builder->insert($data);

        return $this->response->setJSON(['status' => 'success']);
    }
}
