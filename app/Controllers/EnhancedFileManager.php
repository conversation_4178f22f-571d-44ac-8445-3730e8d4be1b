<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class EnhancedFileManager extends BaseController
{
    protected $uploadPath;
    protected $allowedTypes;
    protected $maxSize;

    public function __construct()
    {
        $this->uploadPath = FCPATH . 'uploads/';
        $this->allowedTypes = [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
            'documents' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
            'media' => ['mp4', 'avi', 'mov', 'wmv', 'mp3', 'wav', 'ogg'],
            'archives' => ['zip', 'rar', '7z', 'tar', 'gz']
        ];
        $this->maxSize = 50 * 1024 * 1024; // 50MB
    }

    /**
     * File Manager Dashboard
     */
    public function index()
    {
        $currentPath = $this->request->getGet('path') ?? '';
        $view = $this->request->getGet('view') ?? 'grid';
        $sort = $this->request->getGet('sort') ?? 'name';
        $order = $this->request->getGet('order') ?? 'asc';
        
        $fullPath = $this->uploadPath . $currentPath;
        
        // Security check - prevent directory traversal
        if (!$this->isPathSafe($fullPath)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Invalid path');
        }
        
        $files = $this->getDirectoryContents($fullPath, $sort, $order);
        $breadcrumbs = $this->generateBreadcrumbs($currentPath);
        $storageInfo = $this->getStorageInfo();
        
        $data = [
            'title' => 'File Manager',
            'files' => $files,
            'currentPath' => $currentPath,
            'breadcrumbs' => $breadcrumbs,
            'view' => $view,
            'sort' => $sort,
            'order' => $order,
            'storageInfo' => $storageInfo,
            'allowedTypes' => $this->allowedTypes
        ];
        
        return view('file_manager/index', $data);
    }

    /**
     * Upload files
     */
    public function upload()
    {
        $targetPath = $this->request->getPost('path') ?? '';
        $fullPath = $this->uploadPath . $targetPath;
        
        if (!$this->isPathSafe($fullPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid upload path'
            ]);
        }
        
        $files = $this->request->getFiles();
        $uploadedFiles = [];
        $errors = [];
        
        foreach ($files['files'] as $file) {
            if ($file->isValid() && !$file->hasMoved()) {
                $result = $this->processFileUpload($file, $fullPath);
                
                if ($result['success']) {
                    $uploadedFiles[] = $result['filename'];
                } else {
                    $errors[] = $result['message'];
                }
            }
        }
        
        return $this->response->setJSON([
            'success' => empty($errors),
            'uploaded' => $uploadedFiles,
            'errors' => $errors,
            'message' => empty($errors) ? 
                count($uploadedFiles) . ' file(s) uploaded successfully' : 
                'Some files failed to upload'
        ]);
    }

    /**
     * Create new folder
     */
    public function createFolder()
    {
        $currentPath = $this->request->getPost('path') ?? '';
        $folderName = $this->request->getPost('name');
        
        if (empty($folderName)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Folder name is required'
            ]);
        }
        
        // Sanitize folder name
        $folderName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $folderName);
        $fullPath = $this->uploadPath . $currentPath . '/' . $folderName;
        
        if (!$this->isPathSafe($fullPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid folder path'
            ]);
        }
        
        if (is_dir($fullPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Folder already exists'
            ]);
        }
        
        if (mkdir($fullPath, 0755, true)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Folder created successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create folder'
            ]);
        }
    }

    /**
     * Delete file or folder
     */
    public function delete()
    {
        $items = $this->request->getPost('items');
        $currentPath = $this->request->getPost('path') ?? '';
        
        if (empty($items)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No items selected'
            ]);
        }
        
        $deleted = [];
        $errors = [];
        
        foreach ($items as $item) {
            $fullPath = $this->uploadPath . $currentPath . '/' . $item;
            
            if (!$this->isPathSafe($fullPath)) {
                $errors[] = "Invalid path: $item";
                continue;
            }
            
            if (is_file($fullPath)) {
                if (unlink($fullPath)) {
                    $deleted[] = $item;
                } else {
                    $errors[] = "Failed to delete file: $item";
                }
            } elseif (is_dir($fullPath)) {
                if ($this->deleteDirectory($fullPath)) {
                    $deleted[] = $item;
                } else {
                    $errors[] = "Failed to delete folder: $item";
                }
            }
        }
        
        return $this->response->setJSON([
            'success' => empty($errors),
            'deleted' => $deleted,
            'errors' => $errors,
            'message' => count($deleted) . ' item(s) deleted successfully'
        ]);
    }

    /**
     * Rename file or folder
     */
    public function rename()
    {
        $oldName = $this->request->getPost('oldName');
        $newName = $this->request->getPost('newName');
        $currentPath = $this->request->getPost('path') ?? '';
        
        if (empty($oldName) || empty($newName)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Both old and new names are required'
            ]);
        }
        
        $oldPath = $this->uploadPath . $currentPath . '/' . $oldName;
        $newPath = $this->uploadPath . $currentPath . '/' . $newName;
        
        if (!$this->isPathSafe($oldPath) || !$this->isPathSafe($newPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid file path'
            ]);
        }
        
        if (!file_exists($oldPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found'
            ]);
        }
        
        if (file_exists($newPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'A file with that name already exists'
            ]);
        }
        
        if (rename($oldPath, $newPath)) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'File renamed successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to rename file'
            ]);
        }
    }

    /**
     * Get file information
     */
    public function getFileInfo()
    {
        $filename = $this->request->getGet('file');
        $currentPath = $this->request->getGet('path') ?? '';
        
        $fullPath = $this->uploadPath . $currentPath . '/' . $filename;
        
        if (!$this->isPathSafe($fullPath) || !file_exists($fullPath)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found'
            ]);
        }
        
        $info = [
            'name' => $filename,
            'size' => filesize($fullPath),
            'type' => mime_content_type($fullPath),
            'modified' => filemtime($fullPath),
            'permissions' => substr(sprintf('%o', fileperms($fullPath)), -4),
            'url' => base_url('uploads/' . $currentPath . '/' . $filename)
        ];
        
        // Add image dimensions if it's an image
        if (strpos($info['type'], 'image/') === 0) {
            $imageInfo = getimagesize($fullPath);
            if ($imageInfo) {
                $info['dimensions'] = $imageInfo[0] . ' x ' . $imageInfo[1];
            }
        }
        
        return $this->response->setJSON([
            'success' => true,
            'info' => $info
        ]);
    }

    /**
     * Process file upload
     */
    private function processFileUpload($file, $targetPath)
    {
        // Check file size
        if ($file->getSize() > $this->maxSize) {
            return [
                'success' => false,
                'message' => 'File size exceeds maximum allowed size (50MB)'
            ];
        }
        
        // Check file type
        $extension = strtolower($file->getClientExtension());
        $isAllowed = false;
        
        foreach ($this->allowedTypes as $category => $types) {
            if (in_array($extension, $types)) {
                $isAllowed = true;
                break;
            }
        }
        
        if (!$isAllowed) {
            return [
                'success' => false,
                'message' => 'File type not allowed: ' . $extension
            ];
        }
        
        // Generate unique filename
        $originalName = pathinfo($file->getClientName(), PATHINFO_FILENAME);
        $filename = $this->generateUniqueFilename($originalName, $extension, $targetPath);
        
        // Move file
        if ($file->move($targetPath, $filename)) {
            return [
                'success' => true,
                'filename' => $filename
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to move uploaded file'
            ];
        }
    }

    /**
     * Generate unique filename
     */
    private function generateUniqueFilename($name, $extension, $path)
    {
        $filename = $name . '.' . $extension;
        $counter = 1;
        
        while (file_exists($path . '/' . $filename)) {
            $filename = $name . '_' . $counter . '.' . $extension;
            $counter++;
        }
        
        return $filename;
    }

    /**
     * Check if path is safe (prevent directory traversal)
     */
    private function isPathSafe($path)
    {
        $realPath = realpath($path);
        $basePath = realpath($this->uploadPath);
        
        return $realPath !== false && strpos($realPath, $basePath) === 0;
    }

    /**
     * Get directory contents
     */
    private function getDirectoryContents($path, $sort = 'name', $order = 'asc')
    {
        if (!is_dir($path)) {
            return [];
        }
        
        $files = [];
        $items = scandir($path);
        
        foreach ($items as $item) {
            if ($item === '.' || $item === '..') continue;
            
            $fullPath = $path . '/' . $item;
            $isDir = is_dir($fullPath);
            
            $fileInfo = [
                'name' => $item,
                'type' => $isDir ? 'folder' : 'file',
                'size' => $isDir ? 0 : filesize($fullPath),
                'modified' => filemtime($fullPath),
                'extension' => $isDir ? '' : strtolower(pathinfo($item, PATHINFO_EXTENSION)),
                'icon' => $this->getFileIcon($item, $isDir)
            ];
            
            $files[] = $fileInfo;
        }
        
        // Sort files
        usort($files, function($a, $b) use ($sort, $order) {
            $result = 0;
            
            switch ($sort) {
                case 'name':
                    $result = strcasecmp($a['name'], $b['name']);
                    break;
                case 'size':
                    $result = $a['size'] - $b['size'];
                    break;
                case 'modified':
                    $result = $a['modified'] - $b['modified'];
                    break;
                case 'type':
                    $result = strcasecmp($a['extension'], $b['extension']);
                    break;
            }
            
            return $order === 'desc' ? -$result : $result;
        });
        
        // Folders first
        usort($files, function($a, $b) {
            if ($a['type'] === 'folder' && $b['type'] === 'file') return -1;
            if ($a['type'] === 'file' && $b['type'] === 'folder') return 1;
            return 0;
        });
        
        return $files;
    }

    /**
     * Generate breadcrumbs
     */
    private function generateBreadcrumbs($currentPath)
    {
        $breadcrumbs = [['name' => 'Home', 'path' => '']];
        
        if (!empty($currentPath)) {
            $parts = explode('/', trim($currentPath, '/'));
            $path = '';
            
            foreach ($parts as $part) {
                $path .= '/' . $part;
                $breadcrumbs[] = [
                    'name' => $part,
                    'path' => trim($path, '/')
                ];
            }
        }
        
        return $breadcrumbs;
    }

    /**
     * Get storage information
     */
    private function getStorageInfo()
    {
        $totalSpace = disk_total_space($this->uploadPath);
        $freeSpace = disk_free_space($this->uploadPath);
        $usedSpace = $totalSpace - $freeSpace;
        
        return [
            'total' => $totalSpace,
            'used' => $usedSpace,
            'free' => $freeSpace,
            'usage_percent' => round(($usedSpace / $totalSpace) * 100, 2)
        ];
    }

    /**
     * Get file icon based on type
     */
    private function getFileIcon($filename, $isDir)
    {
        if ($isDir) {
            return 'zmdi-folder';
        }
        
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        $iconMap = [
            // Images
            'jpg' => 'zmdi-image', 'jpeg' => 'zmdi-image', 'png' => 'zmdi-image', 
            'gif' => 'zmdi-image', 'webp' => 'zmdi-image', 'svg' => 'zmdi-image',
            
            // Documents
            'pdf' => 'zmdi-file-text', 'doc' => 'zmdi-file-text', 'docx' => 'zmdi-file-text',
            'xls' => 'zmdi-grid', 'xlsx' => 'zmdi-grid', 'ppt' => 'zmdi-slideshow', 'pptx' => 'zmdi-slideshow',
            'txt' => 'zmdi-file-text',
            
            // Media
            'mp4' => 'zmdi-videocam', 'avi' => 'zmdi-videocam', 'mov' => 'zmdi-videocam',
            'mp3' => 'zmdi-music-note', 'wav' => 'zmdi-music-note', 'ogg' => 'zmdi-music-note',
            
            // Archives
            'zip' => 'zmdi-archive', 'rar' => 'zmdi-archive', '7z' => 'zmdi-archive'
        ];
        
        return $iconMap[$extension] ?? 'zmdi-file';
    }

    /**
     * Delete directory recursively
     */
    private function deleteDirectory($dir)
    {
        if (!is_dir($dir)) {
            return false;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            is_dir($path) ? $this->deleteDirectory($path) : unlink($path);
        }
        
        return rmdir($dir);
    }

    /**
     * Format file size helper
     */
    public function formatFileSize($bytes)
    {
        if ($bytes === 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
}