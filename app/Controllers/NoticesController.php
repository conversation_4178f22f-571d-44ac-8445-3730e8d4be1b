<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class NoticesController extends BaseController
{
    public function index()
    {
        //
    }
    public function addNotice()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('departments');
        // Load the view for adding a notice
        return view('notices/add', [
            'departments' => $builder->get()->getResultArray()
        ]);
    }

    public function ajaxAddNotice()
    {
        $request = service('request');
        $session = session();

        // Prepare data for insertion
        $data = [
            'title'          => $request->getPost('notice_title'),
            'description'    => $request->getPost('notice_description'),
            'notice_type'    => $request->getPost('notice_type'),
            'department_id'  => $request->getPost('department') ? $request->getPost('department') : null,
            'created_at'     => date('Y-m-d H:i:s'),
            'files'          => null, // Default to null, will be updated if file is uploaded
            'status'         => 'active', // Default status
            'created_by'     => $session->get('id'),
            'url_link'       => null, // Default to null, will be updated if link is provided
        ];

        // Handle file upload for notice image
        if ($file = $this->request->getFile('notice_image')) {
            if ($file->isValid() && !$file->hasMoved()) {
                $newName = $file->getRandomName();
                $file->move(FCPATH . 'uploads/notices', $newName);
                $data['files'] = $newName;
            }
        }

        // Handle link data based on source type
        $linkSourceType = $request->getPost('link_source_type');

        if ($linkSourceType === 'upload') {
            // Handle link file upload
            if ($linkFile = $this->request->getFile('notice_link_file')) {
                if ($linkFile->isValid() && !$linkFile->hasMoved()) {
                    $linkFileName = $linkFile->getRandomName();
                    $linkFile->move(FCPATH . 'uploads/notices/links', $linkFileName);
                    $data['url_link'] = base_url('uploads/notices/links/' . $linkFileName);
                }
            }
        } elseif ($linkSourceType === 'url') {
            // Handle direct URL
            $linkUrl = $request->getPost('notice_link_url');
            if (!empty($linkUrl)) {
                $data['url_link'] = $linkUrl;
            }
        } elseif ($linkSourceType === 'filemanager') {
            // Handle file manager URL
            $linkUrl = $request->getPost('notice_link_url');
            if (!empty($linkUrl)) {
                $data['url_link'] = $linkUrl;
            }
        } elseif ($linkSourceType === 'page') {
            // Handle page URL
            $pageUrl = $request->getPost('notice_page_url');
            if (!empty($pageUrl)) {
                $data['url_link'] = $pageUrl;
            }
        }

        // Create uploads directory if it doesn't exist
        if (!is_dir(FCPATH . 'uploads/notices/links')) {
            mkdir(FCPATH . 'uploads/notices/links', 0755, true);
        }

        // Insert into database
        $db = \Config\Database::connect();
        $builder = $db->table('notices');
        if ($builder->insert($data)) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Notice added successfully.',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to add notice.',
            ]);
        }
    }

    public function getNoticeData($id)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('notices');
        $builder->where('id', $id);
        $notice = $builder->get()->getRow();

        if ($notice) {
            return $this->response->setJSON([
                'status' => 'success',
                'data' => $notice
            ]);
        } else {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Notice not found'
            ]);
        }
    }


    public function manageNotices()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('notices');

        // Count total for pagination
        $total = $builder->countAll();

        // Pagination setup
        $perPage = 5;
        $page = (int) ($this->request->getGet('page') ?? 1);

        // Reset builder to avoid conflict
        $builder = $db->table('notices');

        // Apply JOIN and pagination
        $builder->select('notices.*, departments.department_name');
        $builder->join('departments', 'departments.id = notices.department_id', 'left');
        $builder->orderBy('notices.id', 'DESC');
        $builder->limit($perPage, ($page - 1) * $perPage);
        $notices = $builder->get()->getResult();

        // Prepare data for view
        $data = [
            'notices'     => $notices,
            'pager'       => \Config\Services::pager(),
            'total'       => $total,
            'perPage'     => $perPage,
            'currentPage' => $page,
        ];

        return view('notices/manage', $data);
    }

    public function ajaxEditNotices()
    {
        $request = service('request');
        $session = session();

        // Prepare data for update
        $data = [
            'title'          => $request->getPost('title'),
            'description'    => $request->getPost('description'),
            'isApproved'     => $request->getPost('approval_status')
        ];

        // Handle file upload
        if ($file = $this->request->getFile('file')) {
            if ($file->isValid() && !$file->hasMoved()) {
                $newName = $file->getRandomName();
                $file->move(FCPATH . 'uploads/notices', $newName);
                $data['files'] = $newName;
            }
        }

        // Update in database
        $db = \Config\Database::connect();
        $builder = $db->table('notices');
        if ($builder->update($data, ['id' => $request->getPost('id')])) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Notice updated successfully.',
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to update notice.',
            ]);
        }
    }

    public function deleteNotice($id)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('notices');

        // Check if notice exists
        $builder->where('id', $id);
        if ($builder->countAllResults() == 0) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Notice not found'
            ]);
        }

        // Delete the notice
        if ($builder->delete(['id' => $id])) {
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Notice deleted successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Failed to delete notice'
            ]);
        }
    }
}
