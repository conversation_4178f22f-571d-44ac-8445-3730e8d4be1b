<?php

namespace App\Controllers;

class demoHeaderController extends BaseController
{
    // public function index()
    // {
    //     return view('dashboard', ['title' => 'Admin Dashboard']);
    // }
    public function index()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_top_mini');
        
        return json_encode([
            'status' => 'success',
            'message' => 'Welcome to the Header API',
            'data' => [
                'title' => 'Admin Dashboard',
                'description' => 'This is the admin dashboard API endpoint.'
            ]
        ]);
    }

    public function getHeaderData()
    {

        $db = \Config\Database::connect();
        $builder = $db->table('header_top_mini');
        $builder->select('*');
        $query = $builder->get();
        $headerData = $query->getResultArray();
        // Simulate fetching header data
        // $headerData = [
        //     'logo' => 'assets/images/logo.png',
        //     'title' => 'Admin Dashboard',
        //     'navigation' => [
        //         ['label' => 'Home', 'url' => '/'],
        //         ['label' => 'Users', 'url' => '/users'],
        //         ['label' => 'Settings', 'url' => '/settings']
        //     ]
        // ];

        return json_encode([
            'status' => 'success',
            'data' => $headerData
        ]);
    }


}