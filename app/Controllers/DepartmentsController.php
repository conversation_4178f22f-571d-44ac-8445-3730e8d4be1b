<?php

namespace App\Controllers;

use App\Models\DepartmentsModel;
use CodeIgniter\RESTful\ResourceController;

class DepartmentsController extends ResourceController
{
    protected $modelName = 'App\Models\DepartmentsModel';
    protected $format = 'json';
    protected $cacheInvalidationService;

    public function __construct()
    {
        $this->model = new DepartmentsModel();
        $this->cacheInvalidationService = new \App\Services\CacheInvalidationService();
    }

    /**
     * Get all departments with faculty information
     */
    public function index()
    {
        try {
            $departments = $this->model->getAllDepartments();
            return $this->respond([
                'status' => 'success',
                'data' => $departments
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch departments: ' . $e->getMessage());
        }
    }

    /**
     * Get specific department by ID
     */
    public function show($id = null)
    {
        try {
            $department = $this->model->getDepartmentWithHead($id);
            
            if (!$department) {
                return $this->failNotFound('Department not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $department
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch department: ' . $e->getMessage());
        }
    }

    /**
     * Create new department
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            // Validate required fields
            if (empty($data['department_name'])) {
                return $this->fail('Department name is required', 400);
            }

            if (empty($data['faculty_id'])) {
                return $this->fail('Faculty selection is required', 400);
            }

            $departmentId = $this->model->createDepartment($data);
            
            if (!$departmentId) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid faculty selection', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $newDepartment = $this->model->getDepartmentWithHead($departmentId);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateDepartmentCache($departmentId, $data['faculty_id']);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in DepartmentsController::create: ' . $e->getMessage());
            }

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Department created successfully',
                'data' => $newDepartment
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to create department: ' . $e->getMessage());
        }
    }

    /**
     * Update existing department
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            $existingDepartment = $this->model->find($id);
            if (!$existingDepartment) {
                return $this->failNotFound('Department not found');
            }

            $success = $this->model->updateDepartment($id, $data);
            
            if (!$success) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid head selection or faculty selection', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $updatedDepartment = $this->model->getDepartmentWithHead($id);

            // Clear navigation caches (get faculty_id from updated department)
            $facultyId = $updatedDepartment['faculty_id'] ?? null;
            $this->cacheInvalidationService->invalidateDepartmentCache($id, $facultyId);

            return $this->respond([
                'status' => 'success',
                'message' => 'Department updated successfully',
                'data' => $updatedDepartment
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update department: ' . $e->getMessage());
        }
    }

    /**
     * Delete department
     */
    public function delete($id = null)
    {
        try {
            $existingDepartment = $this->model->find($id);
            if (!$existingDepartment) {
                return $this->failNotFound('Department not found');
            }

            // Check if department can be deleted
            if (!$this->model->canDelete($id)) {
                return $this->respond([
                    'status' => 'error',
                    'message' => 'Cannot delete department: Department has faculty members or students. Please remove them first.'
                ], 403);
            }

            // Get faculty_id before deletion for cache invalidation
            $facultyId = $existingDepartment['faculty_id'] ?? null;
            
            $this->model->delete($id);

            // Clear navigation caches
            $this->cacheInvalidationService->invalidateDepartmentCache($id, $facultyId);

            return $this->respond([
                'status' => 'success',
                'message' => 'Department deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to delete department: ' . $e->getMessage());
        }
    }

    /**
     * Get available heads for a department
     */
    public function getAvailableHeads($id = null)
    {
        try {
            $existingDepartment = $this->model->find($id);
            if (!$existingDepartment) {
                return $this->failNotFound('Department not found');
            }

            $availableHeads = $this->model->getAvailableHeads($id);

            return $this->respond([
                'status' => 'success',
                'data' => $availableHeads
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch available heads: ' . $e->getMessage());
        }
    }

    /**
     * Update head of department
     */
    public function updateHead($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data || !isset($data['head_id'])) {
                return $this->fail('Head ID is required', 400);
            }

            $existingDepartment = $this->model->find($id);
            if (!$existingDepartment) {
                return $this->failNotFound('Department not found');
            }

            $success = $this->model->updateHead($id, $data['head_id']);
            
            if (!$success) {
                return $this->fail('Invalid head selection. Head must belong to this department.', 400);
            }

            $updatedDepartment = $this->model->getDepartmentWithHead($id);

            // Clear navigation caches
            $facultyId = $updatedDepartment['faculty_id'] ?? null;
            $this->cacheInvalidationService->invalidateDepartmentCache($id, $facultyId);

            return $this->respond([
                'status' => 'success',
                'message' => 'Head of department assigned successfully',
                'data' => $updatedDepartment
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update head: ' . $e->getMessage());
        }
    }

    /**
     * Remove head from department
     */
    public function removeHead($id = null)
    {
        try {
            $existingDepartment = $this->model->find($id);
            if (!$existingDepartment) {
                return $this->failNotFound('Department not found');
            }

            $success = $this->model->removeHead($id);
            
            if (!$success) {
                return $this->fail('Failed to remove head', 500);
            }

            $updatedDepartment = $this->model->getDepartmentWithHead($id);

            // Clear navigation caches
            $facultyId = $updatedDepartment['faculty_id'] ?? null;
            $this->cacheInvalidationService->invalidateDepartmentCache($id, $facultyId);

            return $this->respond([
                'status' => 'success',
                'message' => 'Head of department removed successfully',
                'data' => $updatedDepartment
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to remove head: ' . $e->getMessage());
        }
    }

    /**
     * Get department showcase data
     */
    public function getShowcase($id = null)
    {
        try {
            $existingDepartment = $this->model->find($id);
            if (!$existingDepartment) {
                return $this->failNotFound('Department not found');
            }

            $showcaseData = $this->model->getDepartmentShowcase($id);

            return $this->respond([
                'status' => 'success',
                'data' => $showcaseData
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch department showcase: ' . $e->getMessage());
        }
    }

    /**
     * Get faculties list for dropdown
     */
    public function getFacultiesList()
    {
        try {
            $faculties = $this->model->getFacultiesList();

            return $this->respond([
                'status' => 'success',
                'data' => $faculties
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculties: ' . $e->getMessage());
        }
    }

    // View Methods for CMS Interface

    /**
     * Department management view
     */
    public function manageDepartmentsView()
    {
        try {
            $searchTerm = $this->request->getGet('search');
            $page = (int)($this->request->getGet('page') ?? 1);
            $perPage = 10;
            
            if ($searchTerm) {
                // Use search functionality
                $departments = $this->model->searchForGlobalSearch($searchTerm);
                $total = count($departments);
                
                // Apply pagination to search results
                $offset = ($page - 1) * $perPage;
                $departments = array_slice($departments, $offset, $perPage);
            } else {
                // Get all departments with pagination
                $departments = $this->model->getAllDepartments();
                $total = count($departments);
                
                // Apply pagination
                $offset = ($page - 1) * $perPage;
                $departments = array_slice($departments, $offset, $perPage);
            }
            
            $data = [
                'title' => 'Manage Departments',
                'departments' => $departments,
                'faculties' => $this->model->getFacultiesList(),
                'searchTerm' => $searchTerm,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage)
                ]
            ];
            
            return view('departments/list', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load departments: ' . $e->getMessage());
        }
    }

    /**
     * Create department view
     */
    public function createDepartmentView()
    {
        try {
            $data = [
                'title' => 'Create New Department',
                'faculties' => $this->model->getFacultiesList()
            ];
            
            return view('departments/add', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load create form: ' . $e->getMessage());
        }
    }

    /**
     * Edit department view
     */
    public function editDepartmentView($id)
    {
        try {
            $department = $this->model->getDepartmentWithHead($id);
            
            if (!$department) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Department not found');
            }

            $data = [
                'title' => 'Edit Department: ' . $department['department_name'],
                'department' => $department,
                'faculties' => $this->model->getFacultiesList(),
                'availableHeads' => $this->model->getAvailableHeads($id)
            ];
            
            return view('departments/edit_department', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load department: ' . $e->getMessage());
        }
    }

    /**
     * Department showcase view (public)
     */
    public function departmentShowcaseView($id)
    {
        try {
            $showcaseData = $this->model->getDepartmentShowcase($id);
            
            if (!$showcaseData || !$showcaseData['department']) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Department not found');
            }

            $data = [
                'title' => $showcaseData['department']['department_name'],
                'showcase' => $showcaseData
            ];
            
            return view('departments/department_showcase', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load department showcase: ' . $e->getMessage());
        }
    }

    // Legacy methods for backward compatibility
    public function listDepartments()
    {
        return $this->manageDepartmentsView();
    }

    public function addDepartments()
    {
        return $this->createDepartmentView();
    }

    public function ajaxaddDepartments()
    {
        $request = service('request');
        $session = session();

        $data = [
            'department_name'        => $request->getPost('department_name'),
            'department_description' => $request->getPost('department_description'),
            'faculty_id'             => $request->getPost('faculty_id'),
            'created_by'             => $session->get('id'),
        ];

        try {
            $departmentId = $this->model->createDepartment($data);
            
            if (!$departmentId) {
                $errors = $this->model->errors();
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'Validation failed: ' . json_encode($errors),
                ]);
            }

            // Clear navigation caches
            $this->cacheInvalidationService->invalidateDepartmentCache($departmentId, $data['faculty_id']);

            // Log the action
            $details = "Added department: " . $data['department_name'];
            \logUserAction('insert', $details);

            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Department added successfully',
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to add department: ' . $e->getMessage(),
            ]);
        }
    }

    public function ajaxEditDepartments()
    {
        $request = service('request');

        $id = $request->getPost('id');
        $data = [
            'department_name'        => $request->getPost('department_name'),
            'department_description' => $request->getPost('department_description'),
            'faculty_id'             => $request->getPost('faculty_id'),
        ];

        try {
            $success = $this->model->updateDepartment($id, $data);
            
            if (!$success) {
                $errors = $this->model->errors();
                return $this->response->setJSON([
                    'status'  => 'error',
                    'message' => 'Validation failed: ' . json_encode($errors),
                ]);
            }

            // Clear navigation caches
            $this->cacheInvalidationService->invalidateDepartmentCache($id, $data['faculty_id']);

            // Log the action
            $details = "Edited department (ID: $id): " . $data['department_name'];
            \logUserAction('update', $details);

            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Department updated successfully',
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to update department: ' . $e->getMessage(),
            ]);
        }
    }
}
