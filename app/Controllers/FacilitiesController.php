<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class FacilitiesController extends BaseController
{
    public function index()
    {
        //
    }
    public function facility_amenity()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('facilities_amenities');
        $perPage = 4;
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();

        $data['facilities'] = $builder
            ->select('facilities_amenities.*')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        $data['pager'] = \Config\Services::pager();
        $data['total'] = $total;
        $data['perPage'] = $perPage;
        $data['currentPage'] = $page;
        return view('facilities_amenities/facility_amenity', $data);
    }

    public function ajaxAddfacility_amenity()
    {
        $categoryName        = $this->request->getPost('categoryName');
        $categoryTitle       = $this->request->getPost('categoryTitle');
        $categoryDescription = $this->request->getPost('categoryDescription');

        $iconFile   = $this->request->getFile('categoryIcon');    // File: icon
        $bannerFile = $this->request->getFile('categoryBanner');  // File: banner

        $iconFileName   = null;
        $bannerFileName = null;

        // ✅ Handle icon upload
        if ($iconFile && $iconFile->isValid() && !$iconFile->hasMoved()) {
            $iconFileName = $iconFile->getRandomName();
            $iconFile->move(FCPATH . 'uploads/facilities', $iconFileName);
        }

        // ✅ Handle banner upload
        if ($bannerFile && $bannerFile->isValid() && !$bannerFile->hasMoved()) {
            $bannerFileName = $bannerFile->getRandomName();
            $bannerFile->move(FCPATH . 'uploads/facilities', $bannerFileName);
        }

        // ✅ Pack banner and text into JSON
        $categoryData = [
            'title'       => $categoryTitle,
            'description' => $categoryDescription,
            'banner'      => $bannerFileName,
        ];

        // ✅ Final data array
        $data = [
            'category_name' => $categoryName,
            'category_icon' => $iconFileName,
            'category_data' => json_encode($categoryData),
        ];

        $db      = \Config\Database::connect();
        $builder = $db->table('facilities_amenities');

        if ($builder->insert($data)) {
            return $this->response->setJSON([
                'status'  => 'success',
                'message' => 'Facility added successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'status'  => 'error',
                'message' => 'Failed to add facility'
            ]);
        }
    }

    public function ajaxEditfacility_amenity()
    {
        // Load necessary helpers & libraries
        helper(['form', 'url']);

        $db      = \Config\Database::connect();
        $builder = $db->table('facilities_amenities');

        // Get POST data
        $id = $this->request->getPost('id');
        $title = $this->request->getPost('title');
        $description = $this->request->getPost('description');

        // Fetch existing record
        $query = $builder->getWhere(['id' => $id]);
        $facility = $query->getRow();

        if (!$facility) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Facility not found']);
        }

        $categoryData = json_decode($facility->category_data);

        // Update category title and description
        $categoryData->title = $title;
        $categoryData->description = $description;

        // Handle Banner Upload
        $bannerPath = $categoryData->banner; // Default to existing banner

        $bannerFile = $this->request->getFile('banner');

        if ($bannerFile && $bannerFile->isValid() && !$bannerFile->hasMoved()) {
            // Delete old image if exists
            if (!empty($categoryData->banner) && file_exists(ROOTPATH . 'public/uploads/facilities/' . $categoryData->banner)) {
                unlink(ROOTPATH . 'public/uploads/facilities/' . $categoryData->banner);
            }

            // Generate unique name to avoid conflicts
            $newName = $bannerFile->getRandomName();
            $bannerFile->move(ROOTPATH . 'public/uploads/facilities/', $newName);

            $bannerPath = $newName;
        }

        // Update category data with new banner
        $categoryData->banner = $bannerPath;

        // Prepare update data
        $updateData = [
            'category_data' => json_encode($categoryData)
        ];

        // Perform the update
        $builder->where('id', $id);
        $result = $builder->update($updateData);

        if ($result) {
            return $this->response->setJSON(['status' => 'success', 'message' => 'Facility updated successfully']);
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to update facility']);
        }
    }

    public function ajaxDeletefacility_amenity()
    {
        $db      = \Config\Database::connect();
        $builder = $db->table('facilities_amenities');

        $id = $this->request->getPost('id');
        // Fetch the facility to get the banner path
        $query = $builder->getWhere(['id' => $id]);
        $facility = $query->getRow();

        if (!$facility) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Facility not found']);
        }

        // Delete the facility record
        if ($builder->delete(['id' => $id])) {
            // Delete the banner file if it exists
            if (!empty($facility->category_data)) {
                $categoryData = json_decode($facility->category_data);
                if (isset($categoryData->banner) && file_exists(ROOTPATH . 'public/uploads/facilities/' . $categoryData->banner)) {
                    unlink(ROOTPATH . 'public/uploads/facilities/' . $categoryData->banner);
                }
            }
            return $this->response->setJSON(['status' => 'success', 'message' => 'Facility deleted successfully']);
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Failed to delete facility']);
        }
    }
}
