<?php

namespace App\Controllers;

use App\Models\FacultyMembersModel;
use CodeIgniter\RESTful\ResourceController;

class FacultyMembersController extends ResourceController
{
    protected $modelName = 'App\Models\FacultyMembersModel';
    protected $format = 'json';
    protected $cacheInvalidationService;

    public function __construct()
    {
        $this->model = new FacultyMembersModel();
        $this->cacheInvalidationService = new \App\Services\CacheInvalidationService();
    }

    /**
     * Get all faculty members with filtering options
     */
    public function index()
    {
        try {
            $filters = $this->request->getGet();
            
            if (isset($filters['department_id'])) {
                $facultyMembers = $this->model->getByDepartment($filters['department_id']);
            } elseif (isset($filters['faculty_id'])) {
                $facultyMembers = $this->model->getByFaculty($filters['faculty_id']);
            } elseif (isset($filters['designation'])) {
                $facultyMembers = $this->model->getByDesignation($filters['designation']);
            } elseif (isset($filters['qualification'])) {
                $facultyMembers = $this->model->getByQualification($filters['qualification']);
            } elseif (isset($filters['search'])) {
                $facultyMembers = $this->model->searchFacultyMembers($filters['search']);
            } else {
                $facultyMembers = $this->model->getAllFacultyMembers();
            }

            return $this->respond([
                'status' => 'success',
                'data' => $facultyMembers
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculty members: ' . $e->getMessage());
        }
    }

    /**
     * Get specific faculty member by ID
     */
    public function show($id = null)
    {
        try {
            $facultyMember = $this->model->getFacultyMemberWithDetails($id);
            
            if (!$facultyMember) {
                return $this->failNotFound('Faculty member not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $facultyMember
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Create new faculty member
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            // Validate required fields
            $requiredFields = ['full_name', 'designation', 'contact_number', 'email', 'department_id', 'employee_id', 'highest_qualification', 'discipline', 'year_completion', 'university_name'];
            
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    return $this->fail(ucfirst(str_replace('_', ' ', $field)) . ' is required', 400);
                }
            }

            $facultyMemberId = $this->model->createFacultyMember($data);
            
            if (!$facultyMemberId) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid department selection', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $newFacultyMember = $this->model->getFacultyMemberWithDetails($facultyMemberId);

            // Clear navigation caches
            $departmentId = $data['department_id'] ?? null;
            $facultyId = $newFacultyMember['faculty_id'] ?? null;
            $this->cacheInvalidationService->invalidateFacultyMemberCache($departmentId, $facultyId);

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Faculty member created successfully',
                'data' => $newFacultyMember
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to create faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Update existing faculty member
     */
    public function update($id = null)
    {
        try {
            $existingFacultyMember = $this->model->find($id);
            if (!$existingFacultyMember) {
                return $this->failNotFound('Faculty member not found');
            }

            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            // If a new photo is being uploaded, delete the old one
            if (isset($data['photo']) && !empty($existingFacultyMember['photo']) && $data['photo'] !== $existingFacultyMember['photo']) {
                \App\Helpers\MediaHelper::deleteOldPhoto($existingFacultyMember['photo']);
            }

            $success = $this->model->updateFacultyMember($id, $data);
            
            if (!$success) {
                $errors = $this->model->errors();
                if (empty($errors)) {
                    return $this->fail('Invalid department selection', 400);
                }
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $updatedFacultyMember = $this->model->getFacultyMemberWithDetails($id);

            // Clear navigation caches
            $departmentId = $updatedFacultyMember['department_id'] ?? null;
            $facultyId = $updatedFacultyMember['faculty_id'] ?? null;
            $this->cacheInvalidationService->invalidateFacultyMemberCache($departmentId, $facultyId);

            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty member updated successfully',
                'data' => $updatedFacultyMember
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Delete faculty member
     */
    public function delete($id = null)
    {
        try {
            $existingFacultyMember = $this->model->find($id);
            if (!$existingFacultyMember) {
                return $this->failNotFound('Faculty member not found');
            }

            // Check if faculty member can be deleted
            if (!$this->model->canDelete($id)) {
                $assignments = $this->model->getAssignments($id);
                $assignmentList = array_map(function($assignment) {
                    return $assignment['type'] . ' of ' . $assignment['entity_name'];
                }, $assignments);

                return $this->respond([
                    'status' => 'error',
                    'message' => 'Cannot delete faculty member: Currently assigned as ' . implode(', ', $assignmentList) . '. Please remove assignments first.',
                    'assignments' => $assignments
                ], 403);
            }

            // Get department and faculty info before deletion for cache invalidation
            $departmentId = $existingFacultyMember['department_id'] ?? null;
            
            // Get faculty_id from department if needed
            $facultyId = null;
            if ($departmentId) {
                $departmentsModel = new \App\Models\DepartmentsModel();
                $department = $departmentsModel->find($departmentId);
                $facultyId = $department['faculty_id'] ?? null;
            }
            
            $this->model->delete($id);

            // Clear navigation caches (don't fail if cache clearing fails)
            try {
                $this->cacheInvalidationService->invalidateFacultyMemberCache($departmentId, $facultyId);
            } catch (\Exception $e) {
                log_message('error', 'Cache invalidation failed in FacultyMembersController::create: ' . $e->getMessage());
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty member deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to delete faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Deactivate faculty member
     */
    public function deactivate($id = null)
    {
        try {
            $existingFacultyMember = $this->model->find($id);
            if (!$existingFacultyMember) {
                return $this->failNotFound('Faculty member not found');
            }

            $success = $this->model->deactivateFacultyMember($id);
            
            if (!$success) {
                return $this->fail('Failed to deactivate faculty member', 500);
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty member deactivated successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to deactivate faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Activate faculty member
     */
    public function activate($id = null)
    {
        try {
            $existingFacultyMember = $this->model->find($id);
            if (!$existingFacultyMember) {
                return $this->failNotFound('Faculty member not found');
            }

            $success = $this->model->activateFacultyMember($id);
            
            if (!$success) {
                return $this->fail('Failed to activate faculty member', 500);
            }

            return $this->respond([
                'status' => 'success',
                'message' => 'Faculty member activated successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to activate faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Get faculty members by department
     */
    public function getByDepartment($departmentId = null)
    {
        try {
            if (!$departmentId) {
                return $this->fail('Department ID is required', 400);
            }

            $facultyMembers = $this->model->getByDepartment($departmentId);

            return $this->respond([
                'status' => 'success',
                'data' => $facultyMembers
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculty members: ' . $e->getMessage());
        }
    }

    /**
     * Get faculty members by faculty
     */
    public function getByFaculty($facultyId = null)
    {
        try {
            if (!$facultyId) {
                return $this->fail('Faculty ID is required', 400);
            }

            $facultyMembers = $this->model->getByFaculty($facultyId);

            return $this->respond([
                'status' => 'success',
                'data' => $facultyMembers
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch faculty members: ' . $e->getMessage());
        }
    }

    /**
     * Get PhD holders
     */
    public function getPhdHolders()
    {
        try {
            $phdHolders = $this->model->getPhdHolders();

            return $this->respond([
                'status' => 'success',
                'data' => $phdHolders
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch PhD holders: ' . $e->getMessage());
        }
    }

    /**
     * Get departments list for dropdown
     */
    public function getDepartmentsList()
    {
        try {
            $departments = $this->model->getDepartmentsList();

            return $this->respond([
                'status' => 'success',
                'data' => $departments
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch departments: ' . $e->getMessage());
        }
    }

    /**
     * Get faculty member assignments
     */
    public function getAssignments($id = null)
    {
        try {
            $existingFacultyMember = $this->model->find($id);
            if (!$existingFacultyMember) {
                return $this->failNotFound('Faculty member not found');
            }

            $assignments = $this->model->getAssignments($id);

            return $this->respond([
                'status' => 'success',
                'data' => $assignments
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch assignments: ' . $e->getMessage());
        }
    }

    /**
     * Get faculty member statistics
     */
    public function getStatistics()
    {
        try {
            $statistics = $this->model->getStatistics();

            return $this->respond([
                'status' => 'success',
                'data' => $statistics
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch statistics: ' . $e->getMessage());
        }
    }

    /**
     * Organize uploaded photo from MediaController to faculty members directory
     */
    public function organizePhoto()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!isset($data['mediaUrl'])) {
                return $this->fail('Media URL is required', 400);
            }

            // Use helper to organize the photo
            $newPath = \App\Helpers\MediaHelper::organizeFacultyPhoto($data['mediaUrl']);
            
            if ($newPath) {
                return $this->respond([
                    'status' => 'success',
                    'data' => ['path' => $newPath]
                ]);
            } else {
                return $this->fail('Failed to organize photo', 500);
            }
        } catch (\Exception $e) {
            return $this->fail('Failed to organize photo: ' . $e->getMessage());
        }
    }

    // View Methods for CMS Interface

    /**
     * Faculty members management view
     */
    public function manageFacultyMembersView()
    {
        try {
            $searchTerm = $this->request->getGet('search');
            $page = (int)($this->request->getGet('page') ?? 1);
            $perPage = 15;
            
            if ($searchTerm) {
                // Use search functionality
                $facultyMembers = $this->model->searchFacultyMembers($searchTerm);
                $total = count($facultyMembers);
                
                // Apply pagination to search results
                $offset = ($page - 1) * $perPage;
                $facultyMembers = array_slice($facultyMembers, $offset, $perPage);
            } else {
                // Get all faculty members with pagination
                $facultyMembers = $this->model->getAllFacultyMembers();
                $total = count($facultyMembers);
                
                // Apply pagination
                $offset = ($page - 1) * $perPage;
                $facultyMembers = array_slice($facultyMembers, $offset, $perPage);
            }
            
            $data = [
                'title' => 'Manage Faculty Members',
                'facultyMembers' => $facultyMembers,
                'departments' => $this->model->getDepartmentsList(),
                'statistics' => $this->model->getStatistics(),
                'searchTerm' => $searchTerm,
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'total_pages' => ceil($total / $perPage)
                ]
            ];
            
            return view('faculty_members/manage_members', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load faculty members: ' . $e->getMessage());
        }
    }

    /**
     * Create faculty member view
     */
    public function createFacultyMemberView()
    {
        try {
            $data = [
                'title' => 'Add New Faculty Member',
                'departments' => $this->model->getDepartmentsList()
            ];
            
            return view('faculty_members/create_member', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load create form: ' . $e->getMessage());
        }
    }

    /**
     * Edit faculty member view
     */
    public function editFacultyMemberView($id)
    {
        try {
            $facultyMember = $this->model->getFacultyMemberWithDetails($id);
            
            if (!$facultyMember) {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Faculty member not found');
            }

            $data = [
                'title' => 'Edit Faculty Member: ' . $facultyMember['full_name'],
                'facultyMember' => $facultyMember,
                'departments' => $this->model->getDepartmentsList(),
                'assignments' => $this->model->getAssignments($id)
            ];
            
            return view('faculty_members/edit_member', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load faculty member: ' . $e->getMessage());
        }
    }

    /**
     * Faculty member profile view (public)
     */
    public function facultyMemberProfileView($id)
    {
        try {
            $facultyMember = $this->model->getFacultyMemberWithDetails($id);
            
            if (!$facultyMember || $facultyMember['status'] !== 'active') {
                throw new \CodeIgniter\Exceptions\PageNotFoundException('Faculty member not found');
            }

            $data = [
                'title' => $facultyMember['full_name'],
                'facultyMember' => $facultyMember,
                'assignments' => $this->model->getAssignments($id)
            ];
            
            return view('faculty_members/member_profile', $data);
        } catch (\Exception $e) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Failed to load faculty member profile: ' . $e->getMessage());
        }
    }
}