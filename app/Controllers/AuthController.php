<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Models\UserModel;

class AuthController extends Controller
{

    public function login()
    {
        helper(['form', 'cookie']);
        $session = session();

        if ($this->request->getMethod() === 'POST') {
            $email = $this->request->getPost('email');
            $password = $this->request->getPost('password');
            $rememberMe = $this->request->getPost('remember_me');

            $userModel = new \App\Models\UserModel();
            $user = $userModel->where('email', $email)->first();

            if ($user) {
                if (password_verify($password, $user['password'])) {
                    if ($user['status'] === 'active' || $user['status'] == 1) {

                        // 1. Generate a secure, complex OTP code
                        $otpCode = bin2hex(random_bytes(4)); // Creates an 8-character hex string
                        $otpExpiry = date('Y-m-d H:i:s', time() + 300); // 5-minute expiry

                        // 2. Save OTP to the database for this user
                        $userModel->update($user['id'], [
                            'otp_code' => $otpCode,
                            'otp_expiry' => $otpExpiry
                        ]);
                        // // 2. Store the OTP and its expiry in the current user's session
                        // $session->set([
                        //     'otp_code_to_verify' => $otpCode,
                        //     'otp_expiry_time'    => $otpExpiry
                        // ]);

                        // 3. Send the OTP email
                        $this->_sendOtpEmail($user['email'], $otpCode);

                        // 4. Tell the frontend to show the OTP form
                        return $this->response->setJSON([
                            'status' => 'otp_required',
                            'message' => 'An OTP has been sent to your email.',
                            'user_id' => $user['id'], // Send user_id to identify who is verifying
                            // No need to send user_id anymore, as the OTP is in the current session
                        ]);
                    } else {
                        return $this->response->setJSON([
                            'status' => 'error',
                            'message' => 'Your account is not active.'
                        ]);
                    }
                } else {
                    return $this->response->setJSON([
                        'status' => 'error',
                        'message' => 'Invalid password.'
                    ]);
                }
            } else {
                return $this->response->setJSON([
                    'status' => 'error',
                    'message' => 'Email not found.'
                ]);
            }
        }

        // ✅ Pass cookie values to view
        $data = [
            'email' => get_cookie('email'),
            'password' => get_cookie('password'),
        ];

        return view('login', $data);
    }
    // public function login()
    // {
    //     helper(['form', 'cookie']);
    //     $session = session();

    //     if ($this->request->getMethod() === 'POST') {
    //         $email = $this->request->getPost('email');
    //         $password = $this->request->getPost('password');

    //         $userModel = new UserModel();
    //         $user = $userModel->where('email', $email)->first();

    //         if ($user && password_verify($password, $user['password'])) {
    //             if ($user['status'] !== 'active' && $user['status'] != 1) {
    //                 return $this->response->setJSON(['status' => 'error', 'message' => 'Your account is not active.']);
    //             }

    //             // --- OTP Logic Starts Here ---

    //             // 1. Generate a secure, complex OTP code
    //             $otpCode = bin2hex(random_bytes(4)); // Creates a 32-character hex string
    //             $otpExpiry = date('Y-m-d H:i:s', time() + 300); // 5-minute expiry

    //             // 2. Save OTP to the database for this user
    //             $userModel->update($user['id'], [
    //                 'otp_code' => $otpCode,
    //                 'otp_expiry' => $otpExpiry
    //             ]);

    //             // 3. Send the OTP email
    //             // $this->_sendOtpEmail($user['email'], $otpCode);
    //             $this->_sendOtpEmail('<EMAIL>', $otpCode);

    //             // 4. Tell the frontend to show the OTP form
    //             return $this->response->setJSON([
    //                 'status' => 'otp_required',
    //                 'message' => 'Verification required. An OTP has been sent to your email.',
    //                 'user_id' => $user['id'] // Send user_id to identify who is verifying
    //             ]);
    //         } else {
    //             return $this->response->setJSON([
    //                 'status' => 'error',
    //                 'message' => 'Invalid email or password.'
    //             ]);
    //         }
    //     }

    //     // Pass cookie values to view if needed
    //     $data = ['email' => get_cookie('email'), 'password' => get_cookie('password')];
    //     return view('login', $data); // Assuming your view is 'login'
    // }

    // NEW FUNCTION: To verify the submitted OTP
    public function verifyOtp()
    {
        $session = session();

        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(405)->setJSON(['status' => 'error', 'message' => 'Method not allowed']);
        }

        $userId = $this->request->getPost('user_id');
        $otpCode = $this->request->getPost('otp');

        if (!$userId || !$otpCode) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid request.']);
        }

        $userModel = new UserModel();
        $user = $userModel->find($userId);

        // Check if OTP is correct and not expired
        if (!$user || $user['otp_code'] !== $otpCode || strtotime($user['otp_expiry']) < time()) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid or expired OTP. Please try logging in again. for super admin id']);
        }

        // --- OTP is valid, now log the user in ---

        // 1. Clear the OTP from the database to prevent reuse
        $userModel->update($userId, ['otp_code' => null, 'otp_expiry' => null]);



        if ($user['role'] === 'superadmin') {
            // 2. Create the real session
            $session->set([
                'id'        => $user['id'],
                'username'  => $user['username'],
                'email'     => $user['email'],
                'user_role'     => $user['role'],
                'isAdminLoggedIn' => true,
                'login_time' => time()
            ]);
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Login successful!',
                'redirect' => base_url('/dashboard') // The real redirect
            ]);
        } else {
            // For department admin, set additional session data
            $session->set([
                'department_id' => $user['department_id'],
                'isDepartmentAdmin' => true,
                'isSuperAdmin' => false,
                'profile_picture' => $user['profile_picture'] ?? null,
                'last_login' => $user['last_login'] ?? null,
                'status' => $user['status'] ?? 'inactive',
                'created_at' => $user['created_at'] ?? null,
                'role' => $user['role'] ?? 'department_admin',
                'user_id' => $user['id'],
                'username' => $user['username'],

            ]);
            return $this->response->setJSON([
                'status' => 'success',
                'message' => 'Login successful!',
                'redirect' => base_url('/dashboard') // The real redirect
            ]);
        }
        // Handle "Remember Me" if you need it here as well


    }

    // PRIVATE HELPER FUNCTION: To send the email
    private function _sendOtpEmail($to, $otp)
    {
        $email = \Config\Services::email();

        $email->setTo($to);
        $email->setFrom('<EMAIL>'); // Change this
        $email->setSubject('Your One-Time Password');
        $email->setMessage("Hello,<br><br>Your one-time password for JBU CMS is: <b>{$otp}</b><br>This code will expire in 5 minutes.<br>If you did not request this, please ignore this email.");

        // For debugging, you can check if the email was sent
        if (!$email->send()) {
            log_message('error', $email->printDebugger(['headers']));
        }
    }





    public function logout()
    {
        session()->destroy();
        return redirect()->to('/login');
    }

    public function forgotPassword()
    {
        return view('forgot_password');
    }
    public function register()
    {
        return view('register');
    }
}
