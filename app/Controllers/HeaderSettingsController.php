<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class HeaderSettingsController extends BaseController
{
    public function index() {}

    public function topMiniHeader()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_top_mini');

        // Assume there's only one row or you filter by ID/section_name
        $row = $builder->get()->getRow();

        // Decode JSON stored in `data` column
        $formData = json_decode($row->data ?? '{}', true);

        return view('header_settings/top_mini_header', ['formData' => $formData]);
    }

    public function updateTopMiniHeader()
    {
        $request = service('request');
        $db = \Config\Database::connect();
        $builder = $db->table('header_top_mini');

        // Get the raw JSON from the request
        $jsonData = $request->getJSON(true); // associative array

        // Validate JSON
        if (!$jsonData || !isset($jsonData['fields'])) {
            return $this->response->setStatusCode(400)->setJSON([
                'status' => 'error',
                'message' => 'Invalid input data',
            ]);
        }

        // Prepare data
        $data = [
            'data' => json_encode($jsonData, JSON_UNESCAPED_SLASHES),
        ];

        // Always update the row with id = 1
        $builder->where('id', 1)->update($data);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Top Mini Header updated successfully',
            'data' => $jsonData
        ]);
    }
    public function midBarHeader()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_mid_bar');

        // Assume there's only one row or you filter by ID/section_name
        $row = $builder->get()->getRow();

        // Decode JSON stored in `data` column
        $formData = json_decode($row->data ?? '{}', true);

        return view('header_settings/mid_bar_header', ['formData' => $formData]);
    }

    public function updateMidBarHeader()
    {
        $request = service('request');
        $jsonData = $request->getJSON(true); // Decode JSON payload into PHP array

        if (!$jsonData || !isset($jsonData['fields'])) {
            return $this->response->setStatusCode(400)->setJSON([
                'status' => 'error',
                'message' => 'Invalid input data.'
            ]);
        }

        $fields = $jsonData['fields'];

        foreach ($fields as &$field) {
            // Check if it's a file input (has "src" key with base64 data)
            if (
                isset($field['type']) &&
                $field['type'] === 'file' &&
                isset($field['src']) &&
                str_starts_with($field['src'], 'data:image/')
            ) {
                // Extract the base64 encoded string
                if (preg_match('/^data:image\/(\w+);base64,/', $field['src'], $type)) {
                    $extension = strtolower($type[1]); // jpg, png, etc
                    $data = substr($field['src'], strpos($field['src'], ',') + 1);
                    $data = base64_decode($data);

                    if ($data === false) {
                        return $this->response->setStatusCode(400)->setJSON([
                            'status' => 'error',
                            'message' => 'Base64 decoding failed.'
                        ]);
                    }

                    // Save file
                    $filename = uniqid('img_') . '.' . $extension;
                    $uploadPath = FCPATH . 'uploads/' . $filename;

                    if (!is_dir(FCPATH . 'uploads')) {
                        mkdir(FCPATH . 'uploads', 0755, true);
                    }

                    file_put_contents($uploadPath, $data);

                    // Store the filename path instead of base64
                    $field['src'] = 'uploads/' . $filename;
                }
            }
        }

        // Save updated fields to DB
        $db = \Config\Database::connect();
        $builder = $db->table('header_mid_bar');

        $saveData = [
            'data' => json_encode(['fields' => $fields], JSON_UNESCAPED_SLASHES),
        ];

        // Update row with ID 1 (as you mentioned there's only one)
        $builder->where('id', 1)->update($saveData);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Mid Bar Header updated successfully!'
        ]);
    }


    public function heroHeading()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_heading');

        // Assume there's only one row or you filter by ID/section_name
        $row = $builder->get()->getRow();

        // Decode JSON stored in `data` column
        $formData = json_decode($row->data ?? '{}', true);

        return view('header_settings/header_heading', ['formData' => $formData]);
    }
    public function updateHeroHeading()
    {
        $request = service('request');
        $jsonData = $request->getJSON(true); // Decode JSON payload into PHP array

        if (!$jsonData || !isset($jsonData['fields'])) {
            return $this->response->setStatusCode(400)->setJSON([
                'status' => 'error',
                'message' => 'Invalid input data.'
            ]);
        }

        $fields = $jsonData['fields'];

        // Save updated fields to DB
        $db = \Config\Database::connect();
        $builder = $db->table('header_heading');

        $saveData = [
            'data' => json_encode(['fields' => $fields], JSON_UNESCAPED_SLASHES),
        ];

        // Update row with ID 1 (as you mentioned there's only one)
        $builder->where('id', 1)->update($saveData);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Hero Heading updated successfully!'
        ]);
    }

    public function navigationOptions()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_navigation_options');

        // Assume there's only one row or you filter by ID/section_name
        $row = $builder->get()->getRow();

        // Decode JSON stored in `data` column
        $formData = json_decode($row->data ?? '{}', true);

        return view('header_settings/navigation_options', ['formData' => $formData]);
    }

    public function heroBanner()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('header_hero_section');

        // Pagination setup
        $perPage = 3; // Number of items per page
        $page = (int) ($this->request->getGet('page') ?? 1);
        $total = $builder->countAll();
        $banner = $builder->orderBy('id', 'DESC')
            ->limit($perPage, ($page - 1) * $perPage)
            ->get()
            ->getResult();

        // Pass data to view
        $data = [
            'banner' => $banner,
            'pager'       => \Config\Services::pager(),
            'total'       => $total,
            'perPage'     => $perPage,
            'currentPage' => $page,
        ];

        return view('header_settings/hero_banner', $data);
    }

    public function updateHeroBanner()
    {
        $request = service('request');
        $db = \Config\Database::connect();
        $bannerId = $request->getPost('bannerId');

        if (!$bannerId) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Banner ID missing']);
        }

        $title = trim($request->getPost('title'));
        $subtitle = trim($request->getPost('subtitle'));
        $imageSourceType = $request->getPost('imageSourceType');

        if (!$title || !$subtitle) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Title and subtitle are required']);
        }

        // Prepare update data
        $updateData = [
            'title' => $title,
            'subtitle' => $subtitle
        ];

        // Handle image update
        if ($imageSourceType === 'upload') {
            // Check for image upload
            $file = $this->request->getFile('image');
            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Validate file type
                $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                if (!in_array(strtolower($file->getExtension()), $allowedTypes)) {
                    return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.']);
                }

                // Validate file size (max 5MB)
                if ($file->getSize() > 5 * 1024 * 1024) {
                    return $this->response->setJSON(['status' => 'error', 'message' => 'File size too large. Maximum 5MB allowed.']);
                }

                $newName = 'hero_' . time() . '.' . $file->getExtension();
                
                // Ensure directory exists
                if (!is_dir(FCPATH . 'uploads/hero/')) {
                    mkdir(FCPATH . 'uploads/hero/', 0755, true);
                }
                
                $file->move(FCPATH . 'uploads/hero/', $newName);

                // Optional: delete old image file (not URLs)
                $old = $db->table('header_hero_section')->getWhere(['id' => $bannerId])->getRow();
                if ($old && $old->banner && !filter_var($old->banner, FILTER_VALIDATE_URL)) {
                    $oldPath = FCPATH . 'uploads/hero/' . $old->banner;
                    if (file_exists($oldPath)) {
                        unlink($oldPath);
                    }
                }

                $updateData['banner'] = $newName;
            }
        } elseif ($imageSourceType === 'url' || $imageSourceType === 'filemanager') {
            // Handle URL source or File Manager selection
            $imageUrl = trim($request->getPost('imageUrl'));
            if ($imageUrl) {
                // Basic URL validation
                if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid URL format']);
                }
                
                // Delete old image file if it exists locally (not URLs)
                $old = $db->table('header_hero_section')->getWhere(['id' => $bannerId])->getRow();
                if ($old && $old->banner && !filter_var($old->banner, FILTER_VALIDATE_URL)) {
                    $oldPath = FCPATH . 'uploads/hero/' . $old->banner;
                    if (file_exists($oldPath)) {
                        unlink($oldPath);
                    }
                }
                
                $updateData['banner'] = $imageUrl;
            }
        }

        // Update in DB
        $db->table('header_hero_section')->where('id', $bannerId)->update($updateData);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Hero banner updated successfully',
            'data' => $updateData
        ]);
    }

    public function addHeroBanner()
    {
        $request = service('request');
        $db = \Config\Database::connect();

        $title = trim($request->getPost('title'));
        $subtitle = trim($request->getPost('subtitle'));
        $imageSourceType = $request->getPost('imageSourceType');

        if (!$title || !$subtitle) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Title and subtitle are required']);
        }

        // Prepare insert data
        $insertData = [
            'title' => $title,
            'subtitle' => $subtitle,
            'banner' => '' // Default empty
        ];

        // Handle image
        if ($imageSourceType === 'upload') {
            $file = $this->request->getFile('image');
            if ($file && $file->isValid() && !$file->hasMoved()) {
                // Validate file type
                $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                if (!in_array(strtolower($file->getExtension()), $allowedTypes)) {
                    return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid file type. Only JPG, PNG, GIF, and WebP are allowed.']);
                }

                // Validate file size (max 5MB)
                if ($file->getSize() > 5 * 1024 * 1024) {
                    return $this->response->setJSON(['status' => 'error', 'message' => 'File size too large. Maximum 5MB allowed.']);
                }

                $newName = 'hero_' . time() . '.' . $file->getExtension();
                
                // Ensure directory exists
                if (!is_dir(FCPATH . 'uploads/hero/')) {
                    mkdir(FCPATH . 'uploads/hero/', 0755, true);
                }
                
                $file->move(FCPATH . 'uploads/hero/', $newName);
                $insertData['banner'] = $newName;
            } else {
                return $this->response->setJSON(['status' => 'error', 'message' => 'Please select a valid image file']);
            }
        } elseif ($imageSourceType === 'url' || $imageSourceType === 'filemanager') {
            $imageUrl = trim($request->getPost('imageUrl'));
            if ($imageUrl) {
                // Basic URL validation
                if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid URL format']);
                }
                $insertData['banner'] = $imageUrl;
            } else {
                return $this->response->setJSON(['status' => 'error', 'message' => 'Image URL is required']);
            }
        } else {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Please provide an image (upload file, URL, or select from file manager)']);
        }

        // Insert in DB
        $db->table('header_hero_section')->insert($insertData);

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'New hero banner added successfully',
            'data' => $insertData
        ]);
    }

    public function deleteHeroBanner()
    {
        $request = service('request');
        $db = \Config\Database::connect();
        $bannerId = $request->getPost('bannerId');

        if (!$bannerId) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Banner ID missing']);
        }

        // Get banner info before deletion to clean up files
        $banner = $db->table('header_hero_section')->getWhere(['id' => $bannerId])->getRow();
        
        if (!$banner) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Banner not found']);
        }

        // Delete the banner from database
        $db->table('header_hero_section')->where('id', $bannerId)->delete();

        // Delete image file if it exists locally (not URLs)
        if ($banner->banner && !filter_var($banner->banner, FILTER_VALIDATE_URL)) {
            $imagePath = FCPATH . 'uploads/hero/' . $banner->banner;
            if (file_exists($imagePath)) {
                unlink($imagePath);
            }
        }

        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Hero banner deleted successfully'
        ]);
    }

    public function getFileManagerImages()
    {
        $path = $this->request->getGet('path') ?? '';
        $uploadsPath = FCPATH . 'uploads/' . $path;
        
        if (!is_dir($uploadsPath)) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Directory not found']);
        }
        
        $files = [];
        $iterator = new \RecursiveIteratorIterator(new \RecursiveDirectoryIterator($uploadsPath));
        
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filename = $file->getFilename();
                $extension = strtolower($file->getExtension());
                
                // Only include image files
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg'])) {
                    $relativePath = str_replace(FCPATH . 'uploads/', '', $file->getPathname());
                    $files[] = [
                        'name' => $filename,
                        'path' => $relativePath,
                        'url' => base_url('uploads/' . $relativePath),
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime()
                    ];
                }
            }
        }
        
        // Sort by name
        usort($files, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        return $this->response->setJSON(['status' => 'success', 'files' => $files]);
    }
}
