<?php

namespace App\Controllers;

class Dashboard extends BaseController
{
    // public function index()
    // {
    //     return view('dashboard', ['title' => 'Admin Dashboard']);
    // }
    public function index()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        // Disk Usage
        $totalSpace = disk_total_space("/");
        $freeSpace = disk_free_space("/");
        $diskUsedPercent = round((($totalSpace - $freeSpace) / $totalSpace) * 100, 2);

        // Memory Usage
        $memoryUsedPercent = null;
        if (function_exists('memory_get_usage')) {
            $totalMemory = @memory_get_usage(true);
            $peakMemory = @memory_get_peak_usage(true);

            if ($peakMemory > 0) {
                $memoryUsedPercent = round(($totalMemory / $peakMemory) * 100, 2);
            }
        }

        // Server Uptime
        $uptimeFormatted = 'Unavailable';
        if (is_readable('/proc/uptime')) {
            $uptime = file_get_contents('/proc/uptime');
            $uptimeSeconds = (int) explode(' ', $uptime)[0];

            $days = floor($uptimeSeconds / 86400);
            $hours = floor(($uptimeSeconds % 86400) / 3600);
            $minutes = floor(($uptimeSeconds % 3600) / 60);

            $uptimeFormatted = "{$days}d {$hours}h {$minutes}m";
        }

        // CPU Load
        $cpuLoadPercent = 0;
        $cpuLoadText = 'Unavailable';
        if (function_exists('sys_getloadavg')) {
            $load = sys_getloadavg();
            if ($load && count($load) > 0) {
                $cpuLoadPercent = round(($load[0] / 4) * 100, 2); // Assuming 4 cores
                $cpuLoadText = $cpuLoadPercent . '%';
            }
        }

        return view('dashboard', [
            'diskUsedPercent' => $diskUsedPercent,
            'memoryUsedPercent' => $memoryUsedPercent,
            'uptimeFormatted' => $uptimeFormatted,
            'cpuLoadPercent' => $cpuLoadPercent,
            'cpuLoadText' => $cpuLoadText,
        ]);
    }
    public function forms()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('forms', ['title' => 'Admin Dashboard']);
    }
    public function tenders()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll(); // Fetch all departments
        return view('tenders', ['title' => 'Admin Tenders', 'departments' => $data['departments']]);
    }
    public function events()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('events', ['title' => 'Admin Events']);
    }
    public function notices()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll(); // Fetch all departments
        return view('notices', ['title' => 'Admin Notices', 'departments' => $data['departments']]);
    }
    public function departments()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll();
        $data['title'] = 'Admin Departments';

        return view('departments', $data);
    }
    public function peoples()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $departmentModel = new \App\Models\DepartmentModel();
        $data['departments'] = $departmentModel->findAll(); // Fetch all departments    
        return view('peoples', ['title' => 'Admin Peoples', 'departments' => $data['departments']]);
    }
    public function abouttac()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->aboutModel = new \App\Models\AboutModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->aboutModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view
        return view('abouttac', [
            'title' => 'Admin About TAC',
            'aboutData' => $aboutData // Pass the data to the view
        ]);
    }
    public function abouttiwa()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->abouttiwaModel = new \App\Models\AbouttiwaModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->abouttiwaModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view
        return view('abouttiwa', [
            'title' => 'Admin About Tiwa Community',
            'aboutData' => $aboutData // Pass the data to the view
        ]);
    }
    public function addtiwacomunitydress()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->abouttiwaModel = new \App\Models\AbouttiwaModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->abouttiwaModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view

    }
    public function abouttacact()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->abouttacactModel = new \App\Models\AbouttacactModel();

        // Fetch the about data (assuming ID=1, modify as needed)
        $aboutData = $this->abouttacactModel->find(1); // This fetches the data with ID=1

        // Pass the fetched data to the view
        return view('abouttacact', [
            'title' => 'Admin About TAC ACT & RULES',
            'aboutData' => $aboutData // Pass the data to the view
        ]);
    }

    public function contacts()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $this->contactModel = new \App\Models\ContactModel();

        // Fetch all the contact data
        $contactData = $this->contactModel->findAll(); // Fetch all contact data

        // Pass the data to the view
        return view('contacts', [
            'title' => 'Admin Contacts',
            'contactData' => $contactData // Pass the data to the view
        ]);
    }
    public function archives()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        // Load the ArchiveModel and ArchiveCategoryModel
        $this->archiveModel = new \App\Models\ArchiveModel();
        $this->archiveCategoryModel = new \App\Models\ArchiveCategoryModel();

        // Fetch all the archive data
        $archiveData = $this->archiveModel->findAll(); // Fetch all archive data
        // Fetch all the archive categories
        $archiveCategories = $this->archiveCategoryModel->findAll(); // Fetch all archive categories

        // Pass the data to the view
        return view('archives', [
            'title' => 'Admin Archives',
            'archiveData' => $archiveData, // Pass archive data to the view
            'archiveCategories' => $archiveCategories // Pass archive categories to the view
        ]);
    }
    public function galleries()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }

        // Fetch all the archive data
    $galleryModel = new \App\Models\GalleryModel();
        $eventModel = new \App\Models\EventModel();
    // Build the query with join
    $galleryData = $galleryModel
        ->select('tac_galleries.*, tac_events.event_name')
        ->join('tac_events', 'tac_events.id = tac_galleries.event_id')
        ->orderBy('tac_galleries.id', 'DESC')
        ->findAll();

        $eventData = $eventModel->findAll();

        // Pass the data to the view
        return view('galleries', [
            'title' => 'Admin Galleries',
            'eventData' => $eventData, // Pass gallery data to the view
          
        ]);
    }

    public function logoUpdate()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        // Fetch the logo data using the method from the model
        $coreModel = new \App\Models\CoreModel();
        $logoData = $coreModel->getLogoSettings(); // Assuming this method returns the logo settings data

        $logoUrl = '';
        $logoWidth = 'auto';
        $logoHeight = 'auto';

        if (!empty($logoData['settings_data'])) {
            $settings = json_decode($logoData['settings_data'], true);

            $logoUrl = !empty($settings['logo_url']) ? $settings['logo_url'] : '';
            $logoWidth = !empty($settings['logo_width']) ? $settings['logo_width'] : 'auto';
            $logoHeight = !empty($settings['logo_height']) ? $settings['logo_height'] : 'auto';
        }

        return view('logoUpdate', [
            'title' => 'Admin Logo Update',
            'logoUrl' => $logoUrl,
            'logoWidth' => $logoWidth,
            'logoHeight' => $logoHeight
        ]);
    }

    public function bgUpdate()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $coreModel = new \App\Models\CoreModel();
        $logoData = $coreModel->getBackgroundSettings(); // Fetch settings

        $fileUrl = '';
        $fileType = '';
        $logoWidth = 'auto';
        $logoHeight = 'auto';

        if (!empty($logoData['settings_data'])) {
            $settings = json_decode($logoData['settings_data'], true);

            $fileUrl = !empty($settings['file_url']) ? $settings['file_url'] : '';
            $fileType = !empty($settings['file_type']) ? $settings['file_type'] : '';

            // Set default dimensions based on file type
            if (strpos($fileType, 'image/') === 0) {
                $logoWidth = 200;
                $logoHeight = 200;
            } elseif (strpos($fileType, 'video/') === 0) {
                $logoWidth = 320;
                $logoHeight = 180;
            }
        }

        return view('bgUpdate', [
            'title'      => 'Admin website Background Update',
            'fileUrl'    => $fileUrl,
            'fileType'   => $fileType,
            'logoWidth'  => $logoWidth,
            'logoHeight' => $logoHeight,
        ]);
    }

    public function socialLink()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('socialLink', ['title' => 'Admin Social Links']);
    }
    public function aboutStaff()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        return view('aboutStaff', ['title' => 'Admin About Staff']);
    }







    // all page ajax fetch functions starts here

    public function fetchDepartments()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\DepartmentModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $departments = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $departments,
            'total' => $total
        ]);
    }
    public function fetchTenders()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\TenderModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        // Build the query with join
        $tenders = $model
            ->select('tac_tenders.*, tac_departments.department_name')
            ->join('tac_departments', 'tac_departments.id = tac_tenders.tender_department')
            ->orderBy('tac_tenders.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $tenders,
            'total' => $total
        ]);
    }

    public function fetchNotices()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\NoticeModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        // Build the query with join
        $notices = $model
            ->select('tac_notices.*, tac_departments.department_name')
            ->join('tac_departments', 'tac_departments.id = tac_notices.notice_department')
            ->orderBy('tac_notices.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $notices,
            'total' => $total
        ]);
    }
    public function fetchEvents()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\EventModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $departments = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $departments,
            'total' => $total
        ]);
    }

    public function fetchPeoples()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\PeopleModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $peoples = $model
            ->select('tac_peoples.*, tac_departments.department_name')
            ->join('tac_departments', 'tac_departments.id = tac_peoples.person_department')
            ->orderBy('tac_peoples.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $peoples,
            'total' => $total
        ]);
    }

    public function fetchArchives()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\ArchiveModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $archives = $model
            ->select('tac_archives.*, tac_archive_category.category_name')
            ->join('tac_archive_category', 'tac_archive_category.id = tac_archives.archive_category')
            ->orderBy('tac_archives.id', 'DESC')
            ->findAll($limit, $offset);

        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $archives,
            'total' => $total
        ]);
    }

    function fetchAboutStaff()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\AboutStaffModel();

        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $staffs = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $staffs,
            'total' => $total
        ]);
    }


    public function fetchGallery()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }
        $model = new \App\Models\GalleryModel();
        $model->select('tac_galleries.*, tac_events.event_name')
            ->join('tac_events', 'tac_events.id = tac_galleries.event_id');
        $limit = $this->request->getGet('limit') ?? 10;
        $offset = $this->request->getGet('offset') ?? 0;

        $galleryData = $model->orderBy('id', 'DESC')->findAll($limit, $offset);
        $total = $model->countAll();

        return $this->response->setJSON([
            'status' => 'success',
            'data' => $galleryData,
            'total' => $total
        ]);
    }



    // all page ajax fetch functions ends here





    public function createDepartment()
    {
        if (!session()->get('isAdminLoggedIn')) {
            return redirect()->to('/login');
        }

        $departmentName = $this->request->getPost('department_name');

        if (empty($departmentName)) {
            return $this->response->setJSON(['status' => 'error', 'message' => 'Department name is required']);
        }

        $data = [
            'department_name'     => $departmentName,
            'department_created'  => date('Y-m-d'),
            'created_by'          => 'admin' // or dynamic value from session
        ];

        $db = \Config\Database::connect();
        $builder = $db->table('tac_departments');
        $builder->insert($data);

        return $this->response->setJSON(['status' => 'success']);
    }
}
