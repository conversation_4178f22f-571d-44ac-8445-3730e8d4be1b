<?php

namespace App\Controllers;

use App\Models\PagesModel;
use CodeIgniter\RESTful\ResourceController;

class PagesController extends ResourceController
{
    protected $modelName = 'App\Models\PagesModel';
    protected $format = 'json';

    public function __construct()
    {
        $this->model = new PagesModel();
    }

    /**
     * Get all pages (for CMS listing)
     */
    public function index()
    {
        try {
            $pages = $this->model->getAllPages();
            return $this->respond([
                'status' => 'success',
                'data' => $pages
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch pages: ' . $e->getMessage());
        }
    }

    /**
     * Get specific page by ID (for editing)
     */
    public function show($id = null)
    {
        try {
            $page = $this->model->getPageWithData($id);
            
            if (!$page) {
                return $this->failNotFound('Page not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $page
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch page: ' . $e->getMessage());
        }
    }

    /**
     * Get specific page by ID
     */
    public function getById($id = null)
    {
        try {
            $page = $this->model->getPageWithData($id);
            
            if (!$page) {
                return $this->failNotFound('Page not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $page
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch page: ' . $e->getMessage());
        }
    }

    /**
     * Get page by name (for frontend rendering)
     */
    public function getByName($pageName = null)
    {
        try {
            if (!$pageName) {
                return $this->fail('Page name is required', 400);
            }

            $page = $this->model->getPageByNameWithData($pageName);
            
            if (!$page) {
                return $this->failNotFound('Page not found');
            }

            return $this->respond([
                'status' => 'success',
                'data' => $page
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to fetch page: ' . $e->getMessage());
        }
    }

    /**
     * Create new page
     */
    public function create()
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            // Validate required fields
            if (empty($data['pageName'])) {
                return $this->fail('Page name is required', 400);
            }

            if (empty($data['pageData'])) {
                return $this->fail('Page data is required', 400);
            }

            $pageId = $this->model->createPage($data);
            
            if (!$pageId) {
                $errors = $this->model->errors();
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $newPage = $this->model->getPageWithData($pageId);

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Page created successfully',
                'data' => $newPage
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to create page: ' . $e->getMessage());
        }
    }

    /**
     * Update existing page
     */
    public function update($id = null)
    {
        try {
            $data = $this->request->getJSON(true);
            
            if (!$data) {
                return $this->fail('Invalid JSON data', 400);
            }

            $existingPage = $this->model->find($id);
            if (!$existingPage) {
                return $this->failNotFound('Page not found');
            }

            $success = $this->model->updatePage($id, $data);
            
            if (!$success) {
                $errors = $this->model->errors();
                return $this->fail('Validation failed: ' . json_encode($errors), 400);
            }

            $updatedPage = $this->model->getPageWithData($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Page updated successfully',
                'data' => $updatedPage
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to update page: ' . $e->getMessage());
        }
    }

    /**
     * Delete page
     */
    public function delete($id = null)
    {
        try {
            $existingPage = $this->model->find($id);
            if (!$existingPage) {
                return $this->failNotFound('Page not found');
            }

            // Check if page is marked as permanent
            if ($this->model->isPagePermanent($id)) {
                return $this->respond([
                    'status' => 'error',
                    'message' => 'Cannot delete permanent page: ' . $existingPage['pageName']
                ], 403);
            }

            $this->model->delete($id);

            return $this->respond([
                'status' => 'success',
                'message' => 'Page deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to delete page: ' . $e->getMessage());
        }
    }

    /**
     * Duplicate page
     */
    public function duplicate($id = null)
    {
        try {
            $existingPage = $this->model->getPageWithData($id);
            if (!$existingPage) {
                return $this->failNotFound('Page not found');
            }

            // Create new page name
            $newPageName = $existingPage['pageName'] . ' (Copy)';
            $counter = 1;
            
            while ($this->model->getPageByName($newPageName)) {
                $newPageName = $existingPage['pageName'] . ' (Copy ' . $counter . ')';
                $counter++;
            }

            $newPageData = [
                'pageName' => $newPageName,
                'pageData' => $existingPage['pageData']
            ];

            $newPageId = $this->model->createPage($newPageData);
            $newPage = $this->model->getPageWithData($newPageId);

            return $this->respondCreated([
                'status' => 'success',
                'message' => 'Page duplicated successfully',
                'data' => $newPage
            ]);
        } catch (\Exception $e) {
            return $this->fail('Failed to duplicate page: ' . $e->getMessage());
        }
    }

    // CMS View Methods
    
    /**
     * Pages management view
     */
    public function managePagesView()
    {
        $searchTerm = $this->request->getGet('search');
        $page = (int)($this->request->getGet('page') ?? 1);
        $perPage = 10;
        
        if ($searchTerm) {
            // Use search functionality
            $pages = $this->model->searchForGlobalSearch($searchTerm);
            $total = count($pages);
            
            // Apply pagination to search results
            $offset = ($page - 1) * $perPage;
            $pages = array_slice($pages, $offset, $perPage);
        } else {
            // Get all pages with pagination
            $total = $this->model->countAllResults();
            $pages = $this->model->findAll($perPage, ($page - 1) * $perPage);
        }
        
        $data = [
            'title' => 'Manage Pages',
            'pages' => $pages,
            'searchTerm' => $searchTerm,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => ceil($total / $perPage)
            ]
        ];
        
        return view('pages/manage_pages', $data);
    }

    /**
     * Create page view
     */
    public function createPageView()
    {
        $data = [
            'title' => 'Create New Page'
        ];
        
        return view('pages/create_page', $data);
    }

    /**
     * Visual editor view
     */
    public function visualEditorView()
    {
        $data = [
            'title' => 'Visual Page Builder'
        ];
        
        return view('pages/visual_editor', $data);
    }

    /**
     * Edit page view
     */
    public function editPageView($id)
    {
        $page = $this->model->getPageWithData($id);
        
        if (!$page) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException('Page not found');
        }

        $data = [
            'title' => 'Edit Page: ' . $page['pageName'],
            'page' => $page
        ];
        
        return view('pages/edit_page', $data);
    }
}