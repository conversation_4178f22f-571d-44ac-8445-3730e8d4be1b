<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

class FooterSettingsController extends BaseController
{
    public function index()
    {
        //
    }

    public function managefooter()
    {
        $db = \Config\Database::connect();
        $builder = $db->table('footer_settings');
        $perPage = 20;
        $page = (int) ($this->request->getGet('page') ?? 1);

        // Get total count
        $total = $builder->countAll();

        // Fetch categories (for dropdown or other use)
        $category = $db->table('footer_settings_category')
            ->select('id, category_name')
            ->get()
            ->getResultArray();

        // Join with footer_settings_category
        $builder->select('footer_settings.id, footer_settings.footer_category, footer_settings_category.category_name, footer_settings.meta_title, footer_settings.meta_link');
        $builder->join('footer_settings_category', 'footer_settings.footer_category = footer_settings_category.id', 'left');
        $builder->orderBy('footer_settings_category.category_name');

        // Pagination
        $builder->limit($perPage, ($page - 1) * $perPage);

        // Get raw results
        $results = $builder->get()->getResult();

        // Group data by footer_category
        $groupedData = [];

        foreach ($results as $row) {
            $key = $row->footer_category;

            if (!isset($groupedData[$key])) {
                $groupedData[$key] = [
                    'footer_category' => $row->footer_category,
                    'category_name'   => $row->category_name,
                    'footer_data'     => []
                ];
            }

            // Include ID for each link
            $groupedData[$key]['footer_data'][] = [
                'id'    => $row->id,
                'title' => $row->meta_title,
                'link'  => $row->meta_link
            ];
        }

        // Convert footer_data arrays into JSON strings
        foreach ($groupedData as &$item) {
            $item['footer_data'] = json_encode($item['footer_data']);
        }

        // Final output
        $data['footer_settings'] = array_values($groupedData);
        $data['pager'] = \Config\Services::pager();
        $data['total'] = $total;
        $data['perPage'] = $perPage;
        $data['currentPage'] = $page;
        $data['categories'] = $category;

        return view('footer_settings/footer_settings', $data);
    }

    public function ajaxAddfooter_settingsForm()
    {
        // Load helper(s)
        helper('form');

        // Get POST data
        $category = $this->request->getPost('footercategory');
        $titles   = $this->request->getPost('metatitle'); // Array
        $links    = $this->request->getPost('metalink');  // Array

        // Validate required fields
        if (empty($category) || empty($titles) || empty($links)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'All fields are required'
            ]);
        }

        // Connect to DB using Query Builder
        $db      = \Config\Database::connect();
        $builder = $db->table('footer_settings');

        // Loop through titles and links, and insert each pair as a new row
        foreach ($titles as $index => $title) {
            $link = $links[$index] ?? null;

            if (!empty($title) && !empty($link)) {
                $data = [
                    'footer_category' => $category,
                    'meta_title'      => $title,
                    'meta_link'       => $link
                ];

                // Insert each record
                $builder->insert($data);
            }
        }

        // If we reach here without error, assume success
        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Footer links saved successfully'
        ]);
    }

    public function ajaxEditfooter_settingsForm()
    {
        // Load helper(s)
        helper('form');

        // Get POST data
        $id       = $this->request->getPost('id');
        $title    = $this->request->getPost('metatitle');
        $link     = $this->request->getPost('metalink');

        // Validate required fields
        if (empty($id) || empty($title) || empty($link)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'All fields are required'
            ]);
        }

        // Connect to DB using Query Builder
        $db      = \Config\Database::connect();
        $builder = $db->table('footer_settings');

        // Update the record
        $data = [
            'meta_title' => $title,
            'meta_link'  => $link
        ];

        $builder->where('id', $id);
        $builder->update($data);

        // If we reach here without error, assume success
        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Footer link updated successfully'
        ]);
    }

    public function ajaxDeletefooter_settingsForm()
    {
        // Get POST data
        $id = $this->request->getPost('id');

        // Validate required fields
        if (empty($id)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'ID is required'
            ]);
        }

        // Connect to DB using Query Builder
        $db      = \Config\Database::connect();
        $builder = $db->table('footer_settings');

        // Delete the record
        $builder->where('id', $id);
        $builder->delete();

        // If we reach here without error, assume success
        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Footer link deleted successfully'
        ]);
    }

    public function ajaxAddfooter_category()
    {
        // Load helper(s)
        helper('form');

        // Get POST data
        $categoryName = $this->request->getPost('categoryname');

        // Validate required fields
        if (empty($categoryName)) {
            return $this->response->setJSON([
                'status' => 'error',
                'message' => 'Category name is required'
            ]);
        }

        // Connect to DB using Query Builder
        $db      = \Config\Database::connect();
        $builder = $db->table('footer_settings_category');

        // Insert the new category
        $data = [
            'category_name' => $categoryName
        ];

        $builder->insert($data);

        // If we reach here without error, assume success
        return $this->response->setJSON([
            'status' => 'success',
            'message' => 'Footer category added successfully'
        ]);
    }
}
