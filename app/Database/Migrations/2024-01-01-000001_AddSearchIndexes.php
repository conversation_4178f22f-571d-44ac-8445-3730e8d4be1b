<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddSearchIndexes extends Migration
{
    public function up()
    {
        // Add indexes for faculty_members table
        if ($this->db->tableExists('faculty_members')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_faculty_search ON faculty_members (full_name, email)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_faculty_employee_id ON faculty_members (employee_id)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_faculty_designation ON faculty_members (designation)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_faculty_status ON faculty_members (status)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }

        // Add indexes for students table
        if ($this->db->tableExists('students')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_student_search ON students (full_name, email)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_student_id ON students (student_id)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_student_program ON students (program_type)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_student_status ON students (status)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }

        // Add indexes for departments table
        if ($this->db->tableExists('departments')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_department_name ON departments (department_name)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_department_faculty ON departments (faculty_id)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }

        // Add indexes for faculties table
        if ($this->db->tableExists('faculties')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_faculty_name ON faculties (faculty_name)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }

        // Add indexes for pages table
        if ($this->db->tableExists('pages')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_page_name ON pages (pageName)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_page_dates ON pages (created_at, updated_at)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }

        // Add indexes for events_upcoming table
        if ($this->db->tableExists('events_upcoming')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_event_title ON events_upcoming (title)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_event_date ON events_upcoming (event_date)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_event_active ON events_upcoming (is_active)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }

        // Add indexes for notices table
        if ($this->db->tableExists('notices')) {
            try {
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_notice_title ON notices (title)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_notice_active ON notices (is_active)');
                $this->db->query('CREATE INDEX IF NOT EXISTS idx_notice_created ON notices (created_at)');
            } catch (\Exception $e) {
                // Indexes might already exist, continue
            }
        }
    }

    public function down()
    {
        // Remove indexes for faculty_members table
        if ($this->db->tableExists('faculty_members')) {
            try {
                $this->db->query('DROP INDEX idx_faculty_search ON faculty_members');
                $this->db->query('DROP INDEX idx_faculty_employee_id ON faculty_members');
                $this->db->query('DROP INDEX idx_faculty_designation ON faculty_members');
                $this->db->query('DROP INDEX idx_faculty_status ON faculty_members');
            } catch (\Exception $e) {
                // Indexes might not exist, continue
            }
        }

        // Remove indexes for other tables
        try {
            $this->db->query('DROP INDEX idx_student_search ON students');
            $this->db->query('DROP INDEX idx_student_id ON students');
            $this->db->query('DROP INDEX idx_student_program ON students');
            $this->db->query('DROP INDEX idx_student_status ON students');
            
            $this->db->query('DROP INDEX idx_department_name ON departments');
            $this->db->query('DROP INDEX idx_department_faculty ON departments');
            
            $this->db->query('DROP INDEX idx_faculty_name ON faculties');
            
            $this->db->query('DROP INDEX idx_page_name ON pages');
            $this->db->query('DROP INDEX idx_page_dates ON pages');
            
            $this->db->query('DROP INDEX idx_event_title ON events_upcoming');
            $this->db->query('DROP INDEX idx_event_date ON events_upcoming');
            $this->db->query('DROP INDEX idx_event_active ON events_upcoming');
            
            $this->db->query('DROP INDEX idx_notice_title ON notices');
            $this->db->query('DROP INDEX idx_notice_active ON notices');
            $this->db->query('DROP INDEX idx_notice_created ON notices');
        } catch (\Exception $e) {
            // Indexes might not exist, continue
        }
    }
}