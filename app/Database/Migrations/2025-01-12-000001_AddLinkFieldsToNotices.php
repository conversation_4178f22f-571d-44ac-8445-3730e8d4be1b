<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddLinkFieldsToNotices extends Migration
{
    public function up()
    {
        // Add new columns for link functionality
        $fields = [
            'link_url' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'URL for notice link (from URL input, file manager, or generated page URL)'
            ],
            'link_file' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Filename for uploaded link file'
            ],
            'link_source_type' => [
                'type' => 'ENUM',
                'constraint' => ['upload', 'url', 'filemanager', 'page'],
                'null' => true,
                'comment' => 'Source type of the link: upload, url, filemanager, or page'
            ],
            'selected_page_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'ID of the selected page when link_source_type is page'
            ]
        ];

        $this->forge->addColumn('notices', $fields);
    }

    public function down()
    {
        // Remove the added columns
        $this->forge->dropColumn('notices', ['link_url', 'link_file', 'link_source_type', 'selected_page_id']);
    }
}
