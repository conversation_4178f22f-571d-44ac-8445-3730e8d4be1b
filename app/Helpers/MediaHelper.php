<?php

namespace App\Helpers;

class MediaHelper
{
    /**
     * Move uploaded file from MediaController location to faculty members directory
     * This is called directly, not via routes
     */
    public static function organizeFacultyPhoto($mediaUrl)
    {
        try {
            // Extract the file path from the media URL
            $baseUrl = base_url();
            $relativePath = str_replace($baseUrl, '', $mediaUrl);
            $relativePath = ltrim($relativePath, '/');
            
            $sourceFile = FCPATH . $relativePath;
            
            if (!file_exists($sourceFile)) {
                return null;
            }
            
            // Create faculty members directory if it doesn't exist
            $facultyDir = FCPATH . 'uploads/faculty_members/';
            if (!is_dir($facultyDir)) {
                mkdir($facultyDir, 0755, true);
            }
            
            // Generate new filename for faculty member
            $pathInfo = pathinfo($sourceFile);
            $newFilename = 'faculty_' . uniqid() . '.' . $pathInfo['extension'];
            $destinationFile = $facultyDir . $newFilename;
            
            // Move file to faculty members directory
            if (rename($sourceFile, $destinationFile)) {
                return 'uploads/faculty_members/' . $newFilename;
            }
            
            return null;
        } catch (\Exception $e) {
            log_message('error', 'Failed to organize faculty photo: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Delete old faculty photo when updating
     */
    public static function deleteOldPhoto($photoPath)
    {
        try {
            if (empty($photoPath)) {
                return true;
            }
            
            $fullPath = FCPATH . $photoPath;
            if (file_exists($fullPath)) {
                return unlink($fullPath);
            }
            
            return true;
        } catch (\Exception $e) {
            log_message('error', 'Failed to delete old photo: ' . $e->getMessage());
            return false;
        }
    }
}