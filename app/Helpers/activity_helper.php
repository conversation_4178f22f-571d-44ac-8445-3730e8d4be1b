<?php

use CodeIgniter\Database\BaseConnection;

if (!function_exists('logUserAction')) {
    function logUserAction($actionType, $details = '')
    {
        $session = session();
        $db = \Config\Database::connect();
        $request = service('request');

        $logData = [
            'user_id'        => $session->get('id') ?? 0,
            'action_type'    => $actionType,
            'ip_address'     => $request->getIPAddress(),
            'user_agent'     => $request->getUserAgent(),
            'action_details' => $details,
            'action_time'    => date('Y-m-d H:i:s'),
        ];

        $db->table('user_logs')->insert($logData);
    }
}
