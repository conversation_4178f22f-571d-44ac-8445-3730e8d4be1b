<?php

return [
    [
        'label' => 'Dashboard',
        'icon' => 'zmdi zmdi-view-dashboard',
        'url'  => '/dashboard',
        'roles' => ['superadmin', 'department_admin', 'admission_admin', 'research_admin'],
    ],
    [
        'label' => 'Departments',
        'icon'  => 'zmdi zmdi-city',
        'roles' => ['superadmin', 'department_admin'],
        'children' => [
            [
                'label' => 'List Departments',
                'icon'  => 'zmdi zmdi-format-list-bulleted',
                'url'   => '/dashboard/listDepartments',
                'roles' => ['superadmin', 'department_admin'],
            ],
            [
                'label' => 'Add Department',
                'icon'  => 'zmdi zmdi-plus-circle',
                'url'   => '/dashboard/addDepartments',
                'roles' => ['superadmin'],
            ],
        ],
    ],
    [
        'label' => 'Users',
        'icon'  => 'zmdi zmdi-accounts',
        'roles' => ['superadmin'],
        'children' => [
            [
                'label' => 'List Users',
                'icon'  => 'zmdi zmdi-account-box',
                'url'   => '/dashboard/listUsers',
                'roles' => ['superadmin'],
            ],
            [
                'label' => 'Add User',
                'icon'  => 'zmdi zmdi-account-add',
                'url'   => '/dashboard/addUsers',
                'roles' => ['superadmin'],
            ],
            [
                'label' => 'Roles & Permissions',
                'icon'  => 'zmdi zmdi-shield-security',
                'url'   => '/dashboard/rolePermissionUsers',
                'roles' => ['superadmin'],
            ],
        ],
    ],
    [
        'label' => 'Content Management',
        'icon'  => 'zmdi zmdi-collection-folder-image',
        'roles' => ['superadmin', 'department_admin'],
        'children' => [
            [
                'label' => 'Manage Sections',
                'icon'  => 'zmdi zmdi-view-list',
                'url'   => '/dashboard/manageSections',
                'roles' => ['superadmin', 'department_admin'],
            ],
            [
                'label' => 'Add New Section',
                'icon'  => 'zmdi zmdi-plus-square',
                'url'   => '/dashboard/addSections',
                'roles' => ['superadmin'],
            ],
        ],
    ],
    [
        'label' => 'Academics',
        'icon'  => 'zmdi zmdi-graduation-cap',
        'roles' => ['superadmin'],
        'children' => [
            ['label' => 'Academic Programmes', 'icon' => 'zmdi zmdi-library', 'url' => '/dashboard/academicProgrammes', 'roles' => ['superadmin']],
            ['label' => 'Syllabus Upload', 'icon' => 'zmdi zmdi-file-text', 'url' => '/dashboard/syllabusUpload', 'roles' => ['superadmin']],
            ['label' => 'Fee Structure', 'icon' => 'zmdi zmdi-money', 'url' => '/dashboard/feeStructure', 'roles' => ['superadmin']],
            ['label' => 'Academic Calendar', 'icon' => 'zmdi zmdi-calendar', 'url' => '/dashboard/academicCalendar', 'roles' => ['superadmin']],
            ['label' => 'Regulations & Guidelines', 'icon' => 'zmdi zmdi-assignment', 'url' => '/dashboard/regulationsGuidelines', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Faculty',
        'icon'  => 'zmdi zmdi-accounts-list',
        'roles' => ['superadmin', 'department_admin'],
        'children' => [
            ['label' => 'Add Faculty', 'icon' => 'zmdi zmdi-account-add', 'url' => '/dashboard/addFaculty', 'roles' => ['superadmin', 'department_admin']],
            ['label' => 'List Faculty', 'icon' => 'zmdi zmdi-accounts-list-alt', 'url' => '/dashboard/listFaculty', 'roles' => ['superadmin', 'department_admin']],
        ],
    ],
    [
        'label' => 'Admissions',
        'icon'  => 'zmdi zmdi-assignment-check',
        'roles' => ['superadmin', 'admission_admin'],
        'children' => [
            ['label' => 'Prospectus Upload', 'icon' => 'zmdi zmdi-file', 'url' => '/dashboard/prospectusUpload', 'roles' => ['superadmin', 'admission_admin']],
            ['label' => 'Admission Guidelines', 'icon' => 'zmdi zmdi-assignment', 'url' => '/dashboard/admissionGuidelines', 'roles' => ['superadmin']],
            ['label' => 'Fee Refund Policy', 'icon' => 'zmdi zmdi-money-off', 'url' => '/dashboard/feeRefundPolicy', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Research',
        'icon'  => 'zmdi zmdi-lamp',
        'roles' => ['superadmin', 'research_admin'],
        'children' => [
            ['label' => 'R&D Cell', 'icon' => 'zmdi zmdi-flask', 'url' => '/dashboard/research', 'roles' => ['superadmin', 'research_admin']],
            ['label' => 'Ongoing Projects', 'icon' => 'zmdi zmdi-refresh', 'url' => '/dashboard/ongoingProjects', 'roles' => ['superadmin', 'research_admin']],
        ],
    ],
    [
        'label' => 'Facilities',
        'icon'  => 'zmdi zmdi-home',
        'roles' => ['superadmin'],
        'children' => [
            ['label' => 'Hostel Info', 'icon' => 'zmdi zmdi-hotel', 'url' => '/dashboard/hostelInfo', 'roles' => ['superadmin']],
            ['label' => 'Scholarship Info', 'icon' => 'zmdi zmdi-card-giftcard', 'url' => '/dashboard/scholarshipInfo', 'roles' => ['superadmin']],
            ['label' => 'Central Library', 'icon' => 'zmdi zmdi-library', 'url' => '/dashboard/centralLibrary', 'roles' => ['superadmin']],
            ['label' => 'Sports, Labs, AV Centre', 'icon' => 'zmdi zmdi-run', 'url' => '/dashboard/sportsLabsAVCentre', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Notices & Updates',
        'icon'  => 'zmdi zmdi-notifications',
        'roles' => ['superadmin', 'department_admin'],
        'children' => [
            ['label' => 'Add Notice', 'icon' => 'zmdi zmdi-plus-square', 'url' => '/dashboard/addNotice', 'roles' => ['superadmin']],
            ['label' => 'Manage Notices', 'icon' => 'zmdi zmdi-format-list-bulleted', 'url' => '/dashboard/manageNotices', 'roles' => ['superadmin']],
            ['label' => 'Results Upload', 'icon' => 'zmdi zmdi-upload', 'url' => '/dashboard/resultsUpload', 'roles' => ['superadmin']],
            ['label' => 'Event Archive', 'icon' => 'zmdi zmdi-archive', 'url' => '/dashboard/eventArchive', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Media & Gallery',
        'icon'  => 'zmdi zmdi-collection-image',
        'roles' => ['superadmin'],
        'children' => [
            ['label' => 'Upload Images', 'icon' => 'zmdi zmdi-upload', 'url' => '/dashboard/uploadImages', 'roles' => ['superadmin']],
            ['label' => 'Manage Gallery', 'icon' => 'zmdi zmdi-collection-image-o', 'url' => '/dashboard/manageGallery', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Feedback & Forms',
        'icon'  => 'zmdi zmdi-comment-text',
        'roles' => ['superadmin'],
        'children' => [
            ['label' => 'View Feedback', 'icon' => 'zmdi zmdi-eye', 'url' => '/dashboard/viewFeedback', 'roles' => ['superadmin']],
            ['label' => 'Download Forms', 'icon' => 'zmdi zmdi-download', 'url' => '/dashboard/downloadForms', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Security & Logs',
        'icon'  => 'zmdi zmdi-lock',
        'roles' => ['superadmin'],
        'children' => [
            ['label' => 'Login History', 'icon' => 'zmdi zmdi-time-restore', 'url' => '/dashboard/loginHistory', 'roles' => ['superadmin']],
            ['label' => 'User Activity Logs', 'icon' => 'zmdi zmdi-assignment-check', 'url' => '/dashboard/userActivityLogs', 'roles' => ['superadmin']],
            ['label' => 'Enable 2FA/OTP', 'icon' => 'zmdi zmdi-shield-security', 'url' => '/dashboard/enable2FA', 'roles' => ['superadmin']],
            ['label' => 'Audit Logs', 'icon' => 'zmdi zmdi-assignment', 'url' => '/dashboard/auditLogs', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Settings',
        'icon'  => 'zmdi zmdi-settings',
        'roles' => ['superadmin'],
        'children' => [
            ['label' => 'University Info', 'icon' => 'zmdi zmdi-info-outline', 'url' => '/dashboard/universityInfo', 'roles' => ['superadmin']],
            ['label' => 'Logo & Favicon', 'icon' => 'zmdi zmdi-brush', 'url' => '/dashboard/logoFavicon', 'roles' => ['superadmin']],
            ['label' => 'SMTP Settings', 'icon' => 'zmdi zmdi-email', 'url' => '/dashboard/smtpSettings', 'roles' => ['superadmin']],
            ['label' => 'Social Media Links', 'icon' => 'zmdi zmdi-globe', 'url' => '/dashboard/socialMediaLinks', 'roles' => ['superadmin']],
            ['label' => 'Backup & Exports', 'icon' => 'zmdi zmdi-cloud-upload', 'url' => '/dashboard/backupExports', 'roles' => ['superadmin']],
        ],
    ],
    [
        'label' => 'Help & Manual',
        'icon'  => 'zmdi zmdi-help',
        'roles' => ['superadmin', 'department_admin'],
        'children' => [
            ['label' => 'Admin Manual (PDF)', 'icon' => 'zmdi zmdi-coffee text-danger', 'url' => '#', 'roles' => ['superadmin']],
            ['label' => 'How to Add Pages', 'icon' => 'zmdi zmdi-chart-donut text-success', 'url' => '#', 'roles' => ['superadmin']],
            ['label' => 'System Usage Guide', 'icon' => 'zmdi zmdi-share text-info', 'url' => '#', 'roles' => ['superadmin']],
        ],
    ],
];
