# Design Document

## Overview

The faculty management system implements a hierarchical organizational structure for the JBU CMS with dynamic data aggregation capabilities. The system follows a three-tier hierarchy: Faculties → Departments → Faculty Members/Students, with intelligent data showcase features that automatically aggregate information across the hierarchy.

## Architecture

### System Flow
```
Faculties (Parent)
    ↓ (has many)
Departments (Child of Faculty)
    ↓ (has many)
Faculty Members & Students (Child of Department)
```

### Key Relationships
- **Faculty ← Dean**: Faculty can have a dean selected from faculty members within its departments
- **Department ← Head**: Department can have a head selected from its faculty members
- **Dynamic Aggregation**: Faculty showcase data is dynamically generated from its departments and their members

## Components and Interfaces

### 1. Faculty Management Component

**Controller:** `FacultiesController`
**Model:** `FacultiesModel`
**Views:** 
- `faculties/manage_faculties.php` - List all faculties
- `faculties/create_faculty.php` - Create new faculty
- `faculties/edit_faculty.php` - Edit faculty details
- `faculties/faculty_showcase.php` - Public faculty display page

**Key Methods:**
- `index()` - List all faculties
- `create()` - Create new faculty
- `update($id)` - Update faculty details
- `delete($id)` - Delete faculty (with cascade handling)
- `getFacultyShowcase($id)` - Get aggregated data for public display
- `getAvailableDeans($facultyId)` - Get faculty members eligible to be dean

### 2. Enhanced Department Management Component

**Controller:** `DepartmentsController` (Enhanced existing)
**Model:** `DepartmentsModel` (Enhanced existing)
**Views:** 
- Enhanced existing department forms to include faculty selection
- Enhanced department showcase with new fields

**New/Enhanced Methods:**
- `getFacultiesList()` - Get all faculties for dropdown
- `getAvailableHeads($departmentId)` - Get faculty members eligible to be head
- `getDepartmentShowcase($id)` - Get aggregated data for department display

### 3. Faculty Members Management Component

**Controller:** `FacultyMembersController`
**Model:** `FacultyMembersModel`
**Views:**
- `faculty_members/manage_members.php` - List all faculty members
- `faculty_members/create_member.php` - Add new faculty member
- `faculty_members/edit_member.php` - Edit faculty member details

**Key Methods:**
- `index()` - List all faculty members with department/faculty info
- `create()` - Add new faculty member
- `update($id)` - Update faculty member details
- `delete($id)` - Delete faculty member (with relationship checks)
- `getByDepartment($departmentId)` - Get members by department
- `getByFaculty($facultyId)` - Get members by faculty (via departments)

### 4. Students Management Component

**Controller:** `StudentsController`
**Model:** `StudentsModel`
**Views:**
- `students/manage_students.php` - List all students
- `students/create_student.php` - Add new student
- `students/edit_student.php` - Edit student details

**Key Methods:**
- `index()` - List all students with filters (current/alumni)
- `create()` - Add new student
- `update($id)` - Update student details
- `markAsAlumni($id)` - Update completion year and status
- `getCurrentStudents($departmentId)` - Get current students by department
- `getAlumni($departmentId)` - Get alumni by department

## Data Models

### Database Schema (Already Implemented)

```sql
faculties:
- id (PK)
- faculty_name
- faculty_description
- dean_id (FK → faculty_members.record_id)
- faculty_image
- profile, programmes_curriculum, research_groups, contact_info
- created_at, updated_at, created_by

departments:
- id (PK)
- department_name
- department_description
- faculty_id (FK → faculties.id)
- head_of_department_id (FK → faculty_members.record_id)
- department_image
- profile, programmes_curriculum, research_groups, contact_info
- created_at, updated_at, created_by

faculty_members:
- record_id (PK)
- full_name, designation, contact_number, email
- department_id (FK → departments.id)
- employee_id (UNIQUE)
- highest_qualification, discipline, year_completion, university_name
- photo, bio, status
- created_at, updated_at

students:
- record_id (PK)
- full_name, student_id (UNIQUE), email, contact_number
- department_id (FK → departments.id)
- program_type, admission_year, expected_completion_year
- actual_completion_year (NULL = current student)
- current_semester, cgpa, status
- photo, created_at, updated_at
```

### Model Relationships

**FacultiesModel:**
```php
public function getFacultyWithDean($id)
public function getDepartments($facultyId)
public function getFacultyMembers($facultyId) // via departments
public function getPhdAwardees($facultyId)
public function getCurrentStudents($facultyId)
public function getAlumni($facultyId)
public function getFacultyNews($facultyId) // via departments
```

**DepartmentsModel (Enhanced):**
```php
public function getDepartmentWithHead($id)
public function getFacultyMembers($departmentId)
public function getCurrentStudents($departmentId)
public function getAlumni($departmentId)
public function getDepartmentNews($departmentId)
```

## Error Handling

### Validation Rules

**Faculty Creation:**
- Faculty name: required, max 100 chars, unique
- Dean selection: must be from faculty members within faculty's departments
- Image upload: validate file type and size

**Department Updates:**
- Faculty selection: required, must exist
- Head selection: must be from department's faculty members
- Maintain existing validation for department name uniqueness

**Faculty Member Creation:**
- Employee ID: required, unique across system
- Email: required, valid format, unique
- Department: required, must exist
- All enum fields: validate against allowed values

**Student Creation:**
- Student ID: required, unique across system
- Email: required, valid format
- Completion year logic: actual_completion_year must be >= admission_year

### Error Scenarios

1. **Circular Reference Prevention:**
   - Cannot delete faculty if it has departments
   - Cannot delete department if it has faculty members/students
   - Cannot delete faculty member if they are dean/head

2. **Relationship Integrity:**
   - Dean must belong to faculty's departments
   - Head must belong to specific department
   - Students/faculty members must belong to existing departments

3. **Data Consistency:**
   - Alumni status automatically determined by actual_completion_year
   - PhD awardees filtered by highest_qualification = 'PhD'

## Testing Strategy

### Unit Tests

1. **Model Tests:**
   - Test all CRUD operations for each model
   - Test relationship methods (getFacultyMembers, getPhdAwardees, etc.)
   - Test validation rules and constraints

2. **Controller Tests:**
   - Test all API endpoints
   - Test form submissions and validations
   - Test error handling scenarios

3. **Integration Tests:**
   - Test complete workflows (create faculty → add departments → add members → assign dean)
   - Test data aggregation accuracy
   - Test cascade delete behaviors

### Manual Testing Scenarios

1. **Hierarchy Creation:**
   - Create faculty → create departments → add faculty members → assign dean
   - Verify dean dropdown only shows eligible members

2. **Data Aggregation:**
   - Add faculty members with different qualifications
   - Add students with different completion statuses
   - Verify faculty showcase displays correct aggregated data

3. **Relationship Management:**
   - Test changing department's faculty
   - Test reassigning dean/head positions
   - Test deletion restrictions

## Implementation Notes

### Frontend Considerations

1. **Dynamic Dropdowns:**
   - Faculty selection in department forms
   - Dean selection filtered by faculty's departments
   - Head selection filtered by department's members

2. **Showcase Pages:**
   - Faculty showcase with tabs: Overview, Departments, Faculty Members, PhD Awardees, Students, Alumni, News & Events
   - Department showcase with similar structure
   - Responsive design for public-facing pages

3. **Admin Interface:**
   - Enhanced existing department management
   - New faculty and faculty member management interfaces
   - Student management with current/alumni filtering

### API Endpoints Structure

```
/api/faculties
  GET / - List all faculties
  POST / - Create faculty
  GET /{id} - Get faculty details
  PUT /{id} - Update faculty
  DELETE /{id} - Delete faculty
  GET /{id}/showcase - Get faculty showcase data
  GET /{id}/available-deans - Get eligible dean candidates

/api/departments (Enhanced existing)
  GET /{id}/available-heads - Get eligible head candidates
  GET /{id}/showcase - Get department showcase data

/api/faculty-members
  GET / - List all faculty members
  POST / - Create faculty member
  GET /{id} - Get faculty member details
  PUT /{id} - Update faculty member
  DELETE /{id} - Delete faculty member

/api/students
  GET / - List all students (with filters)
  POST / - Create student
  GET /{id} - Get student details
  PUT /{id} - Update student
  PUT /{id}/mark-alumni - Mark as alumni
  DELETE /{id} - Delete student
```

### Performance Considerations

1. **Database Indexing:**
   - Index on foreign key columns (faculty_id, department_id, dean_id, head_of_department_id)
   - Index on unique fields (employee_id, student_id)
   - Index on frequently queried fields (status, highest_qualification)

2. **Query Optimization:**
   - Use JOIN queries for data aggregation instead of multiple queries
   - Implement caching for frequently accessed showcase data
   - Paginate large result sets (faculty members, students lists)

3. **Data Loading:**
   - Lazy load showcase data only when needed
   - Implement AJAX loading for dynamic dropdowns
   - Use database views for complex aggregation queries if needed