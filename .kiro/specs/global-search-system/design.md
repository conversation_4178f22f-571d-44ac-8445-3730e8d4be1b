# Design Document

## Overview

The Global Search System will provide a unified search interface that allows users to quickly find and navigate to content across all major entities in the CMS. The system will be built using a service-oriented architecture with real-time AJAX search capabilities and comprehensive result categorization.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Search Input - Navbar] --> B[SearchController]
    B --> C[SearchService]
    C --> D[Model Layer]
    D --> E[Database]
    
    C --> F[Result Formatter]
    F --> G[JSON Response]
    G --> H[Frontend JavaScript]
    H --> I[Search Results UI]
    
    B --> J[Search Results Page]
    J --> K[Advanced Filters]
    K --> L[Paginated Results]
```

### Component Architecture

1. **Frontend Layer**
   - Enhanced navbar search input with AJAX
   - Real-time search results dropdown
   - Comprehensive search results page
   - Keyboard navigation support

2. **Controller Layer**
   - `SearchController` - Handles all search requests
   - RESTful API endpoints for different search operations

3. **Service Layer**
   - `SearchService` - Unified search logic across all models
   - Result formatting and categorization
   - Permission filtering

4. **Model Layer**
   - Enhanced existing models with search methods
   - Optimized database queries for search performance

## Components and Interfaces

### 1. SearchController

**Location:** `app/Controllers/SearchController.php`

**Methods:**
- `quickSearch()` - AJAX endpoint for real-time search
- `fullSearch()` - Comprehensive search results page
- `searchByCategory()` - Category-specific search
- `searchSuggestions()` - Search autocomplete suggestions

**API Endpoints:**
```
GET /dashboard/search/quick?q={query}&limit={limit}
GET /dashboard/search/full?q={query}&category={category}&page={page}
GET /dashboard/search/suggestions?q={query}
GET /dashboard/search/results (Search results page view)
```

### 2. SearchService

**Location:** `app/Services/SearchService.php`

**Core Methods:**
```php
public function performQuickSearch(string $query, int $limit = 20): array
public function performFullSearch(string $query, array $filters = []): array
public function searchByCategory(string $query, string $category): array
public function formatResults(array $results): array
public function filterByPermissions(array $results, $user): array
```

**Search Categories:**
- Pages
- Faculty Members
- Students
- Departments
- Faculties
- Events
- Notices
- Administrations

### 3. Enhanced Model Methods

Each model will have enhanced search methods:

**FacultyMembersModel:**
```php
public function searchForGlobalSearch(string $query, int $limit = null): array
```

**StudentsModel:**
```php
public function searchForGlobalSearch(string $query, int $limit = null): array
```

**DepartmentsModel:**
```php
public function searchForGlobalSearch(string $query, int $limit = null): array
```

### 4. Frontend Components

**Enhanced Search Bar:**
- Real-time search with debouncing (300ms delay)
- Loading indicators
- Keyboard navigation (arrow keys, enter, escape)
- Click-outside-to-close functionality

**Search Results Dropdown:**
- Categorized results display
- Preview information for each result
- Direct navigation links
- "View all" options for categories with many results

**Search Results Page:**
- Comprehensive results with pagination
- Advanced filtering options
- Sort by relevance/date
- Bookmarkable URLs

## Data Models

### Search Result Structure

```php
[
    'category' => 'Faculty Members',
    'results' => [
        [
            'id' => 123,
            'title' => 'Dr. John Smith',
            'description' => 'Professor, Computer Science Department',
            'url' => '/dashboard/editFacultyMember/123',
            'highlight' => 'Computer Science',
            'metadata' => [
                'department' => 'Computer Science',
                'designation' => 'Professor'
            ]
        ]
    ],
    'total_count' => 45,
    'showing_count' => 5
]
```

### Database Search Fields

**Faculty Members:**
- full_name, email, employee_id, designation, discipline, bio
- department_name (joined), faculty_name (joined)

**Students:**
- full_name, student_id, email, program_type
- department_name (joined), faculty_name (joined)

**Departments:**
- department_name, department_description, profile, contact_info
- faculty_name (joined), head_name (joined)

**Pages:**
- pageName, pageData (JSON content search)

**Events:**
- title, description, location, event_type

**Notices:**
- title, content, category

## Error Handling

### Search Service Error Handling

```php
try {
    $results = $this->performQuickSearch($query);
    return $this->respond($results);
} catch (DatabaseException $e) {
    log_message('error', 'Search database error: ' . $e->getMessage());
    return $this->failServerError('Search temporarily unavailable');
} catch (Exception $e) {
    log_message('error', 'Search error: ' . $e->getMessage());
    return $this->failServerError('An error occurred during search');
}
```

### Frontend Error Handling

- Network error handling with retry mechanism
- Graceful degradation when search is unavailable
- User-friendly error messages
- Fallback to basic navigation when search fails

## Testing Strategy

### Unit Tests

1. **SearchService Tests**
   - Test search across all categories
   - Test result formatting
   - Test permission filtering
   - Test edge cases (empty queries, special characters)

2. **SearchController Tests**
   - Test API endpoints
   - Test response formats
   - Test error handling
   - Test pagination

3. **Model Search Method Tests**
   - Test search accuracy
   - Test performance with large datasets
   - Test SQL injection prevention

### Integration Tests

1. **End-to-End Search Flow**
   - Test complete search workflow
   - Test AJAX functionality
   - Test keyboard navigation
   - Test result navigation

2. **Performance Tests**
   - Test search response times
   - Test with large datasets
   - Test concurrent search requests

### Frontend Tests

1. **JavaScript Unit Tests**
   - Test search input handling
   - Test result display
   - Test keyboard navigation
   - Test error handling

## Performance Considerations

### Database Optimization

1. **Indexing Strategy**
   ```sql
   -- Faculty Members
   CREATE INDEX idx_faculty_search ON faculty_members(full_name, email, employee_id);
   
   -- Students
   CREATE INDEX idx_student_search ON students(full_name, student_id, email);
   
   -- Departments
   CREATE INDEX idx_department_search ON departments(department_name);
   
   -- Pages
   CREATE INDEX idx_pages_search ON pages(pageName);
   ```

2. **Query Optimization**
   - Use LIMIT clauses for quick search
   - Implement proper JOIN strategies
   - Use UNION for cross-table searches
   - Consider full-text search for content-heavy fields

### Caching Strategy

1. **Search Result Caching**
   - Cache popular search queries for 5 minutes
   - Cache search suggestions for 1 hour
   - Implement cache invalidation on data updates

2. **Frontend Caching**
   - Cache search results in browser session storage
   - Implement debouncing to reduce server requests
   - Cache search suggestions locally

### Security Considerations

1. **Input Sanitization**
   - Sanitize all search inputs
   - Prevent SQL injection attacks
   - Limit query length and complexity

2. **Permission Filtering**
   - Filter results based on user permissions
   - Ensure users only see authorized content
   - Implement role-based result filtering

3. **Rate Limiting**
   - Implement search rate limiting per user
   - Prevent search abuse and DoS attacks
   - Log suspicious search patterns

## Implementation Phases

### Phase 1: Backend Infrastructure
- Create SearchController and SearchService
- Enhance model search methods
- Implement basic API endpoints
- Add database indexes

### Phase 2: Frontend Integration
- Enhance navbar search input
- Implement AJAX search functionality
- Create search results dropdown
- Add keyboard navigation

### Phase 3: Advanced Features
- Create comprehensive search results page
- Add advanced filtering options
- Implement search suggestions
- Add performance optimizations

### Phase 4: Testing and Optimization
- Comprehensive testing suite
- Performance optimization
- Security hardening
- Documentation and deployment