# Design Document

## Overview

The cache invalidation system will provide automatic and manual cache clearing capabilities to ensure API responses remain synchronized with database changes. The system will use a service-based approach with event-driven cache invalidation triggered by data modifications.

## Architecture

### Core Components

1. **CacheInvalidationService**: Central service for managing cache invalidation
2. **Controller Integration**: Hooks in existing controllers to trigger cache clearing
3. **Cache Key Management**: Organized cache key patterns for efficient clearing
4. **Admin Interface**: Manual cache management capabilities

### Cache Key Structure

```
navigation_faculty_hierarchy
navigation_faculty_complete_{faculty_id}
navigation_department_complete_{department_id}
navigation_university_stats
```

## Components and Interfaces

### CacheInvalidationService

```php
class CacheInvalidationService
{
    public function invalidateFacultyCache($facultyId = null)
    public function invalidateDepartmentCache($departmentId = null)
    public function invalidateNavigationCache()
    public function invalidateUniversityStats()
    public function clearAllNavigationCaches()
    protected function clearCacheKeys(array $keys)
    protected function logCacheAction($action, $keys, $success)
}
```

### Controller Integration Points

**FacultyController Methods:**
- `create()` - Clear faculty hierarchy and stats cache
- `update()` - Clear specific faculty cache and hierarchy
- `delete()` - Clear specific faculty cache and hierarchy
- `updateDean()` - Clear specific faculty cache and hierarchy

**DepartmentsController Methods:**
- `create()` - Clear parent faculty cache and hierarchy
- `update()` - Clear department and parent faculty cache
- `delete()` - Clear department and parent faculty cache

**FacultyMembersController Methods:**
- `create()` - Clear department and faculty cache
- `update()` - Clear department and faculty cache
- `delete()` - Clear department and faculty cache

### Admin Interface

**Cache Management Page:**
- Clear all navigation caches
- Clear specific faculty caches
- Clear specific department caches
- View cache status and statistics

## Data Models

### Cache Invalidation Events

```php
// Event data structure for cache invalidation
[
    'entity_type' => 'faculty|department|faculty_member|student',
    'entity_id' => integer,
    'action' => 'create|update|delete',
    'related_ids' => [
        'faculty_id' => integer,
        'department_id' => integer
    ]
]
```

### Cache Key Patterns

```php
// Cache key patterns for systematic clearing
[
    'navigation_hierarchy' => 'navigation_faculty_hierarchy',
    'faculty_complete' => 'navigation_faculty_complete_*',
    'department_complete' => 'navigation_department_complete_*',
    'university_stats' => 'navigation_university_stats'
]
```

## Error Handling

### Cache Invalidation Failures

1. **Log Error**: Record cache invalidation failures with context
2. **Continue Processing**: Don't block primary operations if cache clearing fails
3. **Retry Mechanism**: Implement simple retry for transient failures
4. **Fallback Strategy**: Provide manual cache clearing as backup

### Error Response Format

```php
[
    'cache_invalidation' => [
        'success' => false,
        'error' => 'Cache clearing failed',
        'keys_attempted' => ['key1', 'key2'],
        'fallback_available' => true
    ]
]
```

## Testing Strategy

### Unit Tests

1. **CacheInvalidationService Tests**
   - Test individual cache clearing methods
   - Test cache key pattern matching
   - Test error handling and logging

2. **Controller Integration Tests**
   - Test cache invalidation triggers on CRUD operations
   - Test cache clearing with valid and invalid IDs
   - Test error scenarios and fallback behavior

### Integration Tests

1. **End-to-End Cache Flow**
   - Create/update data and verify cache clearing
   - Test API responses before and after cache invalidation
   - Test manual cache clearing through admin interface

2. **Performance Tests**
   - Measure cache invalidation impact on response times
   - Test cache clearing with large datasets
   - Test concurrent cache operations

### Test Scenarios

```php
// Test cache invalidation on faculty update
1. Create faculty and verify cache is populated
2. Update faculty data
3. Verify specific faculty cache is cleared
4. Verify hierarchy cache is cleared
5. Verify API returns updated data

// Test error handling
1. Simulate cache service failure
2. Verify primary operation completes
3. Verify error is logged
4. Verify manual clearing still works
```

## Implementation Approach

### Phase 1: Core Service
1. Create CacheInvalidationService
2. Implement basic cache clearing methods
3. Add logging and error handling

### Phase 2: Controller Integration
1. Add cache invalidation to FacultyController
2. Add cache invalidation to DepartmentsController
3. Add cache invalidation to FacultyMembersController
4. Test integration points

### Phase 3: Admin Interface
1. Create cache management controller
2. Add cache management views
3. Implement manual cache clearing
4. Add cache status monitoring

### Phase 4: Testing & Optimization
1. Comprehensive testing
2. Performance optimization
3. Documentation updates
4. Monitoring and alerting setup