# Implementation Plan

- [ ] 1. Create core toast utility module
  - Create `public/assets/js/toast-utils.js` with centralized toast management functions
  - Implement standardized API for all notification types (success, error, warning, info)
  - Add configuration management system with default styling and behavior settings
  - _Requirements: 3.1, 3.2, 3.3, 4.4_

- [ ] 2. Implement confirmation dialog system
  - Create custom modal-based confirmation dialog component in toast-utils.js
  - Implement promise-based API for async confirmation handling
  - Add keyboard navigation support (Enter/Escape keys) and focus management
  - Include accessibility features with proper ARIA labels and screen reader support
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 4.1, 4.2_

- [ ] 3. Create migration wrapper functions
  - Implement `showAlert()` function to replace native alert() calls
  - Implement `showConfirm()` function to replace native confirm() calls with callback/promise support
  - Ensure backward compatibility with existing code patterns
  - Add fallback mechanisms for when Toastify is not available
  - _Requirements: 5.1, 5.2, 5.3_

- [ ] 4. Integrate toast utility into main layout
  - Add toast-utils.js script inclusion to main layout template
  - Ensure proper loading order after Toastify library
  - Add CSS customizations for confirmation dialogs and enhanced toast styling
  - _Requirements: 3.4, 4.3_

- [ ] 5. Replace alert() calls in hero banner management
  - Update `app/Views/header_settings/hero_banner.php` to use showAlert() instead of alert()
  - Replace all 8 alert() instances with appropriate toast notifications
  - Maintain existing validation and error handling logic
  - _Requirements: 1.1, 1.2, 1.4, 5.1, 5.4_

- [ ] 6. Replace alert() calls in page management
  - Update `app/Views/pages/manage_pages.php` to use showAlert() instead of alert()
  - Replace clipboard success/error alerts with toast notifications
  - Update page duplication and deletion success/error messages
  - _Requirements: 1.1, 1.2, 1.4, 5.1, 5.4_

- [ ] 7. Replace alert() calls in visual editor
  - Update `public/assets/js/visual-editor.js` to use showAlert() instead of alert()
  - Replace validation alerts and save confirmation messages
  - Maintain existing form validation behavior
  - _Requirements: 1.1, 1.2, 1.4, 5.1, 5.4_

- [ ] 8. Replace alert() calls in custom functions
  - Update `public/assets/js/custom-functions.js` to use showAlert() instead of alert()
  - Replace error handling alerts while preserving existing Toastify success messages
  - Ensure consistent notification styling across all functions
  - _Requirements: 1.1, 1.2, 1.4, 5.1, 5.4_

- [ ] 9. Replace confirm() calls in hero banner management
  - Update `app/Views/header_settings/hero_banner.php` to use showConfirm() instead of confirm()
  - Replace banner deletion confirmation with custom dialog
  - Maintain existing deletion workflow and error handling
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.2, 5.4_

- [ ] 10. Replace confirm() calls in page management
  - Update `app/Views/pages/manage_pages.php` to use showConfirm() instead of confirm()
  - Replace page duplication and deletion confirmations with custom dialogs
  - Preserve existing callback behavior and user workflow
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.2, 5.4_

- [ ] 11. Replace confirm() calls in visual editor components
  - Update visual editor files to use showConfirm() instead of confirm()
  - Replace section, row, and column deletion confirmations
  - Update page reset confirmation in edit_page.php
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.2, 5.4_

- [ ] 12. Replace confirm() calls in custom functions
  - Update `public/assets/js/custom-functions.js` to use showConfirm() instead of confirm()
  - Replace contact archiving, deletion, and staff management confirmations
  - Update global delete function to use custom confirmation dialog
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.2, 5.4_

- [ ] 13. Replace confirm() calls in events and footer settings
  - Update event management files to use showConfirm() instead of confirm()
  - Replace footer settings meta field removal confirmation
  - Update logout confirmation in index.js
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 5.2, 5.4_

- [ ] 14. Standardize existing Toastify usage
  - Update inconsistent Toastify implementations to use centralized toast utility
  - Ensure consistent styling, duration, and positioning across all existing toast messages
  - Refactor duplicate toast configuration code
  - _Requirements: 3.1, 3.3, 3.4, 1.4_

- [ ] 15. Add comprehensive error handling and testing
  - Implement fallback mechanisms for when toast system fails
  - Add browser compatibility checks and graceful degradation
  - Create test cases for toast display, confirmation dialogs, and accessibility features
  - Validate keyboard navigation and screen reader compatibility
  - _Requirements: 4.1, 4.2, 4.3, 5.3_