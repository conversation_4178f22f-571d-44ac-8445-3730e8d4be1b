# Design Document

## Overview

The Faculty Navigation APIs provide comprehensive, hierarchical access to university organizational data for frontend applications. The system leverages existing models and controllers while adding new specialized endpoints for navigation and showcase purposes. The design emphasizes performance, consistency, and ease of integration.

## Architecture

### API Structure
```
/api/v1/navigation/
├── faculties/                    # Faculty navigation endpoints
│   ├── hierarchy                 # Complete hierarchical structure
│   ├── {id}/complete            # Complete faculty data by ID
│   ├── {id}/showcase            # Faculty showcase data (cached)
│   └── name/{name}/complete     # Complete faculty data by name
├── departments/                  # Department navigation endpoints
│   ├── {id}/complete            # Complete department data by ID
│   ├── {id}/showcase            # Department showcase data (cached)
│   └── name/{name}/complete     # Complete department data by name
├── statistics/                   # University statistics
│   ├── overview                 # General university statistics
│   ├── academic                 # Academic-specific statistics
│   └── faculty/{id}/stats       # Faculty-specific statistics
└── filters/                     # Filtered entity lists
    ├── faculty-members          # Filtered faculty members
    ├── students                 # Filtered students
    └── departments              # Filtered departments
```

### Response Format
All APIs follow a consistent response structure:
```json
{
  "success": true,
  "data": { ... },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "cached": false,
    "cache_expires": "2024-01-01T01:00:00Z"
  }
}
```

## Components and Interfaces

### 1. NavigationController
**Purpose**: Handle all navigation-related API endpoints
**Location**: `app/Controllers/Api/NavigationController.php`

**Key Methods**:
- `getFacultyHierarchy()` - Complete hierarchical structure
- `getFacultyComplete($id)` - Complete faculty data by ID
- `getFacultyByName($name)` - Complete faculty data by name
- `getDepartmentComplete($id)` - Complete department data by ID
- `getDepartmentByName($name)` - Complete department data by name
- `getUniversityStatistics()` - University overview statistics
- `getFilteredFacultyMembers()` - Filtered faculty member lists
- `getFilteredStudents()` - Filtered student lists

### 2. NavigationService
**Purpose**: Business logic for data aggregation and caching
**Location**: `app/Services/NavigationService.php`

**Key Methods**:
- `buildFacultyHierarchy()` - Construct hierarchical data structure
- `aggregateFacultyData($id)` - Collect all faculty-related data
- `aggregateDepartmentData($id)` - Collect all department-related data
- `calculateStatistics()` - Generate university statistics
- `applyFilters($entity, $filters)` - Apply filtering logic
- `cacheData($key, $data, $duration)` - Handle caching operations

### 3. Enhanced Models
**Existing models will be extended with navigation-specific methods**:

#### FacultyModel Extensions:
- `getForNavigation()` - Optimized faculty data for navigation
- `getCompleteWithRelations($id)` - Faculty with all related data
- `findByNameSlug($name)` - Find faculty by name/slug

#### DepartmentsModel Extensions:
- `getForNavigation()` - Optimized department data for navigation
- `getCompleteWithRelations($id)` - Department with all related data
- `findByNameSlug($name)` - Find department by name/slug

## Data Models

### Faculty Hierarchy Response
```json
{
  "success": true,
  "data": {
    "faculties": [
      {
        "id": 1,
        "name": "Faculty of Science",
        "slug": "faculty-of-science",
        "description": "...",
        "dean": {
          "id": 123,
          "name": "Dr. John Doe",
          "designation": "Professor"
        },
        "departments": [
          {
            "id": 10,
            "name": "Department of Chemistry",
            "slug": "department-of-chemistry",
            "description": "...",
            "head": {
              "id": 456,
              "name": "Dr. Jane Smith",
              "designation": "Associate Professor"
            },
            "stats": {
              "faculty_members": 15,
              "current_students": 120,
              "alumni": 450
            }
          }
        ],
        "stats": {
          "total_departments": 5,
          "total_faculty_members": 75,
          "total_current_students": 600,
          "total_alumni": 2250
        }
      }
    ]
  },
  "meta": {
    "total_faculties": 8,
    "total_departments": 35,
    "cached": true,
    "cache_expires": "2024-01-01T02:00:00Z"
  }
}
```

### Complete Faculty Data Response
```json
{
  "success": true,
  "data": {
    "faculty": {
      "id": 1,
      "name": "Faculty of Science",
      "slug": "faculty-of-science",
      "description": "...",
      "profile": "...",
      "programmes_curriculum": "...",
      "research_groups": "...",
      "contact_info": "...",
      "faculty_image": "...",
      "dean": {
        "id": 123,
        "name": "Dr. John Doe",
        "designation": "Professor",
        "email": "<EMAIL>",
        "bio": "...",
        "photo": "..."
      },
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    },
    "departments": [
      {
        "id": 10,
        "name": "Department of Chemistry",
        "slug": "department-of-chemistry",
        "description": "...",
        "head": { ... },
        "stats": { ... }
      }
    ],
    "faculty_members": [
      {
        "id": 456,
        "name": "Dr. Jane Smith",
        "designation": "Associate Professor",
        "department": "Department of Chemistry",
        "email": "<EMAIL>",
        "qualification": "PhD",
        "discipline": "Organic Chemistry",
        "photo": "...",
        "bio": "..."
      }
    ],
    "current_students": [
      {
        "id": 789,
        "name": "Alice Johnson",
        "student_id": "SCI2024001",
        "department": "Department of Chemistry",
        "program_type": "Bachelor's",
        "admission_year": 2024,
        "current_semester": 1
      }
    ],
    "alumni": [
      {
        "id": 101,
        "name": "Bob Wilson",
        "student_id": "SCI2020001",
        "department": "Department of Chemistry",
        "program_type": "Bachelor's",
        "completion_year": 2023
      }
    ],
    "phd_awardees": [
      {
        "id": 456,
        "name": "Dr. Jane Smith",
        "department": "Department of Chemistry",
        "qualification": "PhD",
        "discipline": "Organic Chemistry"
      }
    ],
    "news_events": [
      {
        "id": 1,
        "title": "Chemistry Department Seminar",
        "description": "...",
        "department": "Department of Chemistry",
        "date": "2024-01-15",
        "type": "event"
      }
    ],
    "statistics": {
      "total_departments": 5,
      "total_faculty_members": 75,
      "total_phd_awardees": 45,
      "total_current_students": 600,
      "total_alumni": 2250,
      "total_news_events": 25
    }
  },
  "meta": {
    "cached": true,
    "cache_duration": 3600,
    "last_updated": "2024-01-01T00:00:00Z"
  }
}
```

### University Statistics Response
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_faculties": 8,
      "total_departments": 35,
      "total_faculty_members": 450,
      "total_current_students": 5200,
      "total_alumni": 15000
    },
    "academic": {
      "phd_holders": 280,
      "professors": 120,
      "associate_professors": 180,
      "assistant_professors": 150,
      "program_distribution": {
        "bachelors": 3500,
        "masters": 1200,
        "mphil": 300,
        "phd": 200
      }
    },
    "by_faculty": [
      {
        "faculty_name": "Faculty of Science",
        "departments": 5,
        "faculty_members": 75,
        "current_students": 600,
        "alumni": 2250
      }
    ]
  },
  "meta": {
    "generated_at": "2024-01-01T00:00:00Z",
    "cached": true,
    "cache_expires": "2024-01-01T06:00:00Z"
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "FACULTY_NOT_FOUND",
    "message": "Faculty with ID 999 not found",
    "details": {
      "requested_id": 999,
      "available_ids": [1, 2, 3, 4, 5]
    }
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "req_123456789"
  }
}
```

### Error Codes
- `FACULTY_NOT_FOUND` (404) - Faculty ID/name not found
- `DEPARTMENT_NOT_FOUND` (404) - Department ID/name not found
- `INVALID_PARAMETERS` (400) - Invalid query parameters
- `RATE_LIMIT_EXCEEDED` (429) - Too many requests
- `INTERNAL_ERROR` (500) - Server error

## Testing Strategy

### Unit Tests
- **NavigationController Tests**: Test all endpoint responses and error handling
- **NavigationService Tests**: Test data aggregation and caching logic
- **Model Extension Tests**: Test new navigation-specific methods

### Integration Tests
- **API Response Tests**: Verify complete response structures
- **Performance Tests**: Test caching effectiveness and query optimization
- **Error Handling Tests**: Verify proper error responses

### Test Data Requirements
- Sample faculties with departments and relationships
- Faculty members with various designations and qualifications
- Students with different statuses and program types
- News/events linked to departments

## Performance Considerations

### Caching Strategy
- **Faculty Hierarchy**: Cache for 1 hour, invalidate on structure changes
- **Complete Faculty Data**: Cache for 30 minutes, invalidate on related data changes
- **University Statistics**: Cache for 6 hours, invalidate on major data changes
- **Filtered Lists**: Cache for 15 minutes, invalidate on entity changes

### Database Optimization
- **Eager Loading**: Use joins to prevent N+1 queries
- **Selective Fields**: Only fetch required fields for navigation
- **Indexes**: Ensure proper indexing on frequently queried fields
- **Query Optimization**: Use database query builder efficiently

### Response Optimization
- **Pagination**: Implement for large datasets
- **Field Selection**: Allow clients to specify required fields
- **Compression**: Enable gzip compression for large responses
- **CDN Integration**: Cache static responses at CDN level

## Security Considerations

### Access Control
- **Public APIs**: No authentication required for navigation data
- **Rate Limiting**: Implement per-IP rate limiting
- **Input Validation**: Sanitize all input parameters
- **SQL Injection Prevention**: Use parameterized queries

### Data Privacy
- **Sensitive Data**: Exclude sensitive information from public APIs
- **Personal Information**: Limit personal data in responses
- **Contact Information**: Only include public contact details

## Implementation Notes

### Existing Code Integration
- Leverage existing `FacultyModel`, `DepartmentsModel`, `FacultyMembersModel`, and `StudentsModel`
- Extend existing showcase functionality from faculty management system
- Reuse caching mechanisms from existing search system

### URL Structure
- Use RESTful conventions for consistency
- Support both ID and name-based routing
- Implement proper HTTP status codes
- Use semantic versioning for API versions

### Documentation
- Generate OpenAPI/Swagger documentation
- Provide code examples for common use cases
- Include performance guidelines for frontend developers
- Document caching behavior and invalidation rules