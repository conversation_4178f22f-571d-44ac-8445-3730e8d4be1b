# Design Document

## Overview

The permanent page protection feature adds a safeguard mechanism to prevent deletion of critical pages in the JBU CMS. This is implemented by checking a `permanent` boolean field in the database before allowing delete operations to proceed.

## Architecture

The protection mechanism is implemented at the controller level, specifically in the `PagesController::delete()` method. This ensures that the check occurs before any database operations are attempted.

### Flow Diagram

```
Delete Request → Check if page exists → Check permanent flag → Allow/Deny deletion
```

## Components and Interfaces

### PagesController Modifications

**Method:** `delete($id = null)`

**Enhanced Logic:**
1. Verify page exists (existing functionality)
2. Check if page has `permanent = true`
3. If permanent, return 403 Forbidden with descriptive error
4. If not permanent, proceed with deletion (existing functionality)

**Error Response Format:**
```json
{
    "status": "error",
    "message": "Cannot delete permanent page: [Page Name]",
    "code": 403
}
```

### PagesModel Enhancements

**New Method:** `isPagePermanent($id)`
- Returns boolean indicating if page is marked as permanent
- Handles null values as false for backward compatibility

**Field Exclusion:**
- Ensure `permanent` field is NOT included in `$allowedFields`
- This prevents modification through standard CRUD operations

## Data Models

### Database Schema
The `pages` table already has the `permanent` column added:
```sql
ALTER TABLE `pages` ADD COLUMN `permanent` BOOLEAN DEFAULT FALSE;
```

### Field Behavior
- `permanent = true`: Page cannot be deleted
- `permanent = false`: Page can be deleted normally  
- `permanent = null`: Treated as false (backward compatibility)

## Error Handling

### Error Scenarios

1. **Permanent Page Deletion Attempt**
   - Status Code: 403 Forbidden
   - Message: "Cannot delete permanent page: [Page Name]"
   - Action: Block deletion, return error

2. **Page Not Found**
   - Status Code: 404 Not Found  
   - Message: "Page not found"
   - Action: Existing error handling (unchanged)

3. **Database Errors**
   - Status Code: 500 Internal Server Error
   - Message: "Failed to delete page: [Error Details]"
   - Action: Existing error handling (unchanged)

## Testing Strategy

### Unit Tests
1. Test deletion of permanent page returns 403 error
2. Test deletion of non-permanent page succeeds
3. Test deletion with null permanent value succeeds
4. Test error message format and content
5. Test that permanent field is not in allowedFields

### Integration Tests
1. Test complete delete workflow for permanent pages
2. Test API response format matches specification
3. Test backward compatibility with existing pages

### Manual Testing
1. Create test pages with different permanent values
2. Verify delete operations through API endpoints
3. Confirm error messages are user-friendly
4. Test that permanent field cannot be modified via API

## Implementation Notes

- The permanent field check should be performed after verifying the page exists to maintain consistent error handling order
- Error messages should include the page name for better user experience
- The implementation should maintain backward compatibility with existing pages where permanent is null
- No changes are needed to the frontend CMS interface since permanent pages are managed directly in the database