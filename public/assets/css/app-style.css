@import "https://fonts.googleapis.com/css?family=Roboto:400,500,700&display=swap";

html {
    font-family: 'Roboto', sans-serif;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -ms-overflow-style: scrollbar;
    -webkit-tap-highlight-color: transparent;
    height: 100%
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #111;
    font-family: 'Roboto', sans-serif;
    font-size: 15px;
    color: #e0e0e0;
    letter-spacing: .5px
}

[tabindex="-1"]:focus {
    outline: 0 !important
}

::selection {
    background: #ffab40;
    color: #000
}

select option {
    background: #111
}

::placeholder {
    color: #e0e0e0 !important;
    font-size: 13px;
    opacity: .5 !important
}

:-ms-input-placeholder {
    color: #e0e0e0 !important
}

::-ms-input-placeholder {
    color: #e0e0e0 !important
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    color: #fff
}

.h1,
h1 {
    font-size: 48px;
    line-height: 52px
}

.h2,
h2 {
    font-size: 38px;
    line-height: 42px
}

.h3,
h3 {
    font-size: 30px;
    line-height: 34px
}

.h4,
h4 {
    font-size: 24px;
    line-height: 28px
}

.h5,
h5 {
    font-size: 18px;
    line-height: 22px
}

.h6,
h6 {
    font-size: 14px;
    line-height: 18px
}

.display-1 {
    font-size: 6rem
}

.display-2 {
    font-size: 5.5rem
}

.display-3 {
    font-size: 4.5rem
}

.display-4 {
    font-size: 3.5rem
}

.line-height-0 {
    line-height: 0
}

.line-height-5 {
    line-height: 5px
}

.line-height-10 {
    line-height: 5px
}

code {
    font-size: 87.5%;
    color: #ffab40;
    word-break: break-word
}

.blockquote-footer {
    color: #a0a0a0
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    margin-top: 1rem;
    border: 0;
    border-top: 1px solid #333
}

p {
    margin-bottom: .65rem
}

:focus {
    outline: 0 !important
}

a {
    color: #ffab40;
    outline: none !important
}

a:hover {
    color: #ffd180;
    text-decoration: none
}

a.text-muted:focus,
a.text-muted:hover {
    color: #a0a0a0
}

hr {
    border-top: 1px solid #333
}

.small,
small {
    font-size: 75%;
    font-weight: 400
}

.small-font {
    font-size: 14px
}

.extra-small-font {
    font-size: 12px
}

.breadcrumb-item.active {
    color: #fff
}

.breadcrumb-item+.breadcrumb-item::before {
    color: #fff
}

.row {
    margin-right: -12.5px;
    margin-left: -12.5px
}

.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto,
.col-md,
.col-md-1,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-auto,
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-auto,
.col-xl,
.col-xl-1,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-auto {
    padding-right: 12.5px;
    padding-left: 12.5px
}

#wrapper {
    width: 100%;
    position: relative
}

#sidebar-wrapper {
    background-color: #1a1a1a;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    overflow: hidden;
    width: 250px;
    height: 100%;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
    box-shadow: 0 2px 10px #0000004d
}

#wrapper.toggled #sidebar-wrapper {
    position: fixed;
    left: -250px
}

#wrapper.toggled .menu-icon {
    margin-left: 0
}

#wrapper.toggled .content-wrapper {
    margin-left: 0
}

#wrapper.toggled .topbar-nav .navbar {
    margin-left: 0
}

.content-wrapper {
    margin-left: 250px;
    overflow-x: hidden;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
    padding: 70px 10px
}

.right-sidebar {
    width: 260px;
    height: 100%;
    max-height: 100%;
    position: fixed;
    overflow: scroll;
    overflow-x: hidden;
    top: 0;
    right: -300px;
    z-index: 999;
    text-align: center;
    padding: 10px;
    background: #1a1a1a;
    box-shadow: 0 16px 38px -12px #0000008f 0 4px 25px 0 #0000001f 0 8px 10px -5px #0003;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.switcher-icon {
    width: 40px;
    height: 40px;
    line-height: 40px;
    background: #1a1a1a;
    text-align: center;
    font-size: 22px;
    color: #fff;
    cursor: pointer;
    display: inline-block;
    box-shadow: 0 16px 38px -12px #0000008f 0 4px 25px 0 #0000001f 0 8px 10px -5px #0003;
    position: fixed;
    right: 0;
    top: 15rem;
    border-top-left-radius: .25rem;
    border-bottom-left-radius: .25rem;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -ms-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.right-sidebar.right-toggled {
    right: 0
}

.right-sidebar.right-toggled .switcher-icon {
    right: 260px
}

.bg-theme {
    background-size: 100% 100%;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    transition: background .3s
}

.switcher {
    list-style: none;
    margin: 0;
    padding: 0;
    overflow: hidden;
    margin-left: 20px
}

.switcher li {
    float: left;
    width: 85px;
    height: 75px;
    margin: 0 15px 15px 0;
    border-radius: 4px;
    border: 0 solid #000
}

.topbar-nav .navbar {
    padding: 0 15px;
    z-index: 999;
    height: 60px;
    background-color: #1a1a1a;
    -webkit-box-shadow: 0 2px 10px #0000004d;
    box-shadow: 0 2px 10px #0000004d;
    margin-left: 250px;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.toggle-menu {
    color: #fff;
    cursor: pointer;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.toggle-menu i {
    font-size: 18px;
    font-weight: 600;
    color: #fff
}

.right-nav-link a.nav-link {
    padding-right: .8rem !important;
    padding-left: .8rem !important;
    font-size: 20px;
    color: #fff
}

.dropdown-menu {
    border: 1px solid #333;
    -webkit-box-shadow: 0 2px 10px #0000004d !important;
    box-shadow: 0 2px 10px #0000004d !important;
    font-size: 15px;
    background-color: #1a1a1a;
    color: #fff
}

.dropdown-menu ul {
    margin-top: 0
}

.dropdown-divider {
    margin: 0;
    border-top: 1px solid #333
}

.dropdown-item {
    padding: .7rem 1.5rem;
    color: #e0e0e0
}

.dropdown-item:hover {
    background-color: #ffab40;
    color: #000
}

.dropdown-item.active,
.dropdown-item:active {
    color: #000;
    text-decoration: none;
    background-color: #ffab40
}

.dropdown-toggle-nocaret:after {
    display: none
}

.user-profile img {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    box-shadow: 0 16px 38px -12px #0000008f 0 4px 25px 0 #0000001f 0 8px 10px -5px #0003
}

.user-details .media .avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%
}

.user-details .media .media-body .user-title {
    font-size: 14px;
    color: #fff;
    font-weight: 600;
    margin-bottom: 2px
}

.user-details .media .media-body .user-subtitle {
    font-size: 13px;
    color: #e0e0e0;
    margin-bottom: 0
}

.brand-logo {
    width: 100%;
    height: 60px;
    line-height: 60px;
    text-align: center;
    border-bottom: 1px solid #dee2e661
}

.logo-text {
    color: #fff;
    font-size: 15px;
    display: inline-block;
    text-transform: uppercase;
    position: relative;
    top: 3px;
    font-weight: 400;
    text-align: center;
    line-height: 50px
}

.logo-icon {
    width: 35px;
    margin-right: 5px
}

.search-bar {
    margin-left: 20px;
    position: relative
}

.search-bar input {
    border: 1px solid #444;
    font-size: 15px;
    width: 530px;
    border-radius: .25rem;
    height: 34px;
    padding: .375rem 2rem .375rem .75rem;
    background-color: #2a2a2a;
    color: #fff
}

.search-bar input::placeholder {
    color: #e0e0e0 !important;
    font-size: 13px;
    opacity: .5 !important
}

.search-bar input:focus {
    background-color: #333;
    border: 1px solid #ffab40;
    box-shadow: 0 0 0 .2rem #ffab404d
}

.search-bar a i {
    position: absolute;
    top: 8px;
    right: 15px;
    color: #e0e0e0;
    font-size: 16px
}

.product-img {
    height: 32px
}

.skill-img {
    height: 35px
}

.page-title {
    font-size: 20px;
    line-height: 20px
}

.breadcrumb {
    padding: 0;
    background-color: transparent
}

.sidebar-menu li a i:first-child {
    margin-right: 10px;
    font-size: 18px
}

.sidebar-menu li a i:last-child {
    margin-right: 10px;
    font-size: 12px
}

.row.row-group>div {
    border-right: 1px solid #333
}

.row.row-group>div:last-child {
    border-right: none
}

.card {
    margin-bottom: 25px;
    box-shadow: 0 2px 10px #0000004d;
    background-color: #1a1a1a
}

.card-header {
    padding: .75rem 1.25rem;
    margin-bottom: 0;
    background: transparent;
    border-bottom: 1px solid #dee2e661;
    font-weight: 600;
    font-size: 14px;
    color: #fff
}

.card-title {
    margin-bottom: .75rem;
    font-weight: 600;
    font-size: 16px;
    color: #fff
}

.card-action {
    float: right
}

.card-action a i {
    color: #fff;
    border-radius: 50%
}

.card-footer {
    padding: .75rem 1.25rem;
    background-color: transparent;
    border-top: 1px solid #333
}

.card-deck {
    margin-bottom: 25px
}

@media (min-width: 576px) {
    .card-deck {
        margin-right: -12.5px;
        margin-left: -12.5px
    }

    .card-deck .card {
        margin-right: 12.5px;
        margin-left: 12.5px
    }
}

.card-group {
    box-shadow: 0 2px 10px #0000004d;
    margin-bottom: 25px
}

.card-group .card {
    box-shadow: none
}

.profile-card-2 .card-img-block {
    float: left;
    width: 100%;
    height: 150px;
    overflow: hidden
}

.profile-card-2 .card-body {
    position: relative
}

.profile-card-2 .profile {
    border-radius: 50%;
    position: absolute;
    top: -42px;
    left: 15%;
    max-width: 75px;
    border: 3px solid #1a1a1a;
    -webkit-transform: translate(-50%, 0%);
    transform: translate(-50%, 0%)
}

.profile-card-2 h5 {
    font-weight: 600
}

.profile-card-2 .card-text {
    font-weight: 300;
    font-size: 15px
}

.profile-card-2 .icon-block {
    float: left;
    width: 100%
}

.profile-card-2 .icon-block a {
    text-decoration: none
}

.profile-card-2 i {
    display: inline-block;
    text-align: center;
    width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    margin: 0 5px
}

.btn {
    font-size: .7rem;
    font-weight: 500;
    letter-spacing: 1px;
    padding: 9px 19px;
    border-radius: .25rem;
    text-transform: uppercase;
    box-shadow: 0 .125rem .25rem #00000013
}

.btn-link {
    color: #ffab40
}

.btn:focus {
    box-shadow: none
}

.btn-lg {
    padding: 12px 38px;
    font-size: .9rem
}

.btn-sm {
    font-size: 10px;
    font-weight: 500;
    padding: 6px 15px
}

.btn-group-sm>.btn {
    font-size: 10px
}

.btn-primary {
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.btn-primary:hover {
    color: #000;
    background-color: #ffd180;
    border-color: #ffc46d
}

.btn-primary.focus,
.btn-primary:focus {
    box-shadow: none
}

.btn-primary.disabled,
.btn-primary:disabled {
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active,
.show>.btn-primary.dropdown-toggle {
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.btn-secondary {
    color: #fff;
    background-color: #444;
    border-color: #444
}

.btn-secondary:hover {
    color: #fff;
    background-color: #555;
    border-color: #555
}

.btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-success:hover {
    color: #fff;
    background-color: #218838;
    border-color: #1e7e34
}

.btn-info {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-info:hover {
    color: #fff;
    background-color: #138496;
    border-color: #117a8b
}

.btn-warning {
    color: #000;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-warning:hover {
    color: #000;
    background-color: #e0a800;
    border-color: #d39e00
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130
}

.btn-light {
    color: #000;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-light:hover {
    color: #000;
    background-color: #e2e6ea;
    border-color: #dae0e5
}

.btn-dark {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}

.btn-dark:hover {
    color: #fff;
    background-color: #23272b;
    border-color: #1d2124
}

.btn-outline-primary {
    color: #ffab40;
    background-color: transparent;
    background-image: none;
    border-color: #ffab40
}

.btn-outline-primary:hover {
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.btn-outline-secondary {
    color: #aaa;
    background-color: transparent;
    background-image: none;
    border-color: #aaa
}

.btn-outline-secondary:hover {
    color: #fff;
    background-color: #aaa;
    border-color: #aaa
}

.btn-outline-success {
    color: #28a745;
    border-color: #28a745
}

.btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745
}

.btn-outline-info {
    color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-info:hover {
    color: #fff;
    background-color: #17a2b8;
    border-color: #17a2b8
}

.btn-outline-warning {
    color: #ffc107;
    border-color: #ffc107
}

.btn-outline-warning:hover {
    color: #000;
    background-color: #ffc107;
    border-color: #ffc107
}

.btn-outline-danger {
    color: #dc3545;
    border-color: #dc3545
}

.btn-outline-danger:hover {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545
}

.btn-outline-light {
    color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-outline-light:hover {
    color: #000;
    background-color: #f8f9fa;
    border-color: #f8f9fa
}

.btn-outline-dark {
    color: #343a40;
    border-color: #343a40
}

.btn-outline-dark:hover {
    color: #fff;
    background-color: #343a40;
    border-color: #343a40
}

.form-control {
    border: 1px solid #444;
    background-color: #2a2a2a;
    color: #fff !important
}

.form-control:focus {
    background-color: #333;
    border-color: #ffab40;
    box-shadow: 0 0 0 .2rem #ffab404d;
    color: #fff !important
}

.form-control:disabled,
.form-control[readonly] {
    background-color: #333;
    opacity: 1
}

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #e0e0e0
}

.table thead th {
    font-size: .72rem;
    padding-top: .75rem;
    padding-bottom: .75rem;
    letter-spacing: 1px;
    text-transform: uppercase;
    border-bottom: 1px solid #dee2e661
}

.table td,
.table th {
    border-top: 1px solid #333
}

.table-bordered {
    border: 1px solid #333
}

.table-bordered td,
.table-bordered th {
    border: 1px solid #333
}

.table-hover tbody tr:hover {
    background-color: #2a2a2a;
    color: #fff
}

.table .thead-primary th {
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.alert {
    position: relative;
    padding-inline: 9px;
    padding-block: 3px;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    background-color: #2a2a2a;
    box-shadow: 0 .125rem .25rem #00000013;
    border-radius: .25rem
}

.alert .alert-icon {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    width: 60px;
    font-size: 20px
}

.alert .alert-message {
    display: table-cell;
    padding: 20px 15px;
    font-size: 14px
}

.alert-dismissible .close {
    position: absolute;
    top: 8px;
    right: 0;
    font-weight: 300;
    padding: 10px 15px;
    color: inherit
}

.alert-success {
    color: #c3e6cb;
    background-color: #192c20;
    border-color: #28a745
}

.alert-info {
    color: #bee5eb;
    padding-inline: 9px;
    padding-block: 3px;
    border-radius: 9px;
    background-color: #1c2e33;
    border-color: #17a2b836
}

.alert-warning {
    color: #ffeeba;
    background-color: #332b00;
    border-color: #ffc107
}

.alert-danger {
    color: #f5c6cb;
    background-color: #301d20;
    border-color: #dc3545
}

.badge-primary {
    color: #000;
    background-color: #ffab40
}

.badge-secondary {
    color: #fff;
    background-color: #444
}

.badge-success {
    color: #fff;
    background-color: #28a745
}

.badge-danger {
    color: #fff;
    background-color: #dc3545
}

.badge-warning {
    color: #000;
    background-color: #ffc107
}

.badge-info {
    color: #fff;
    background-color: #17a2b8
}

.badge-light {
    color: #000;
    background-color: #f8f9fa
}

.badge-dark {
    color: #fff;
    background-color: #343a40
}

.pagination .page-link {
    color: #e0e0e0;
    background-color: #2a2a2a;
    border: 1px solid #444
}

.pagination .page-link:hover {
    background-color: #333
}

.page-item.active .page-link {
    z-index: 1;
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.list-group-item {
    background-color: #1a1a1a;
    border: 1px solid #333
}

.list-group-item-action:hover {
    background-color: #2a2a2a
}

.list-group-item.active {
    z-index: 2;
    color: #000;
    background-color: #ffab40;
    border-color: #ffab40
}

.nav-tabs .nav-link {
    color: #e0e0e0;
    border: 0 solid transparent
}

.nav-tabs .nav-link:hover {
    border: 0 solid transparent
}

.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
    color: #ffab40;
    background-color: transparent;
    border-bottom: 2px solid #ffab40
}

.progress {
    background-color: #2a2a2a
}

.progress-bar {
    background-color: #ffab40
}

.bg-primary {
    background-color: #ffab40 !important;
    color: #000 !important
}

.text-primary {
    color: #ffab40 !important
}

.footer {
    left: 0;
    color: #a0a0a0;
    border-top: 1px solid #333;
    -webkit-transition: all .3s ease;
    -moz-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease
}

.back-to-top {
    background-color: #444
}

.back-to-top:hover {
    background-color: #ffab40
}

/* Ad
ditional sidebar toggle styles */
#wrapper {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

/* Ensure proper positioning for mobile */
@media (max-width: 768px) {
    #sidebar-wrapper {
        left: -250px;
    }

    .content-wrapper {
        margin-left: 0;
    }

    .topbar-nav .navbar {
        margin-left: 0;
    }

    #wrapper.toggled #sidebar-wrapper {
        left: 0;
    }
}

/* Overlay for mobile when sidebar is open */
.overlay.toggle-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

@media (max-width: 768px) {
    #wrapper.toggled .overlay.toggle-menu {
        display: block;
    }
}

/*
 Global Search Styles */
.search-bar {
    margin-left: 20px;
    position: relative;
}

.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-bar input {
    border: 1px solid #444;
    font-size: 15px;
    width: 530px;
    border-radius: 0.25rem;
    height: 34px;
    padding: 0.375rem 2rem 0.375rem 0.75rem;
    background-color: #2a2a2a;
    color: #fff;
    transition: all 0.3s ease;
}

.search-bar input::placeholder {
    color: #e0e0e0 !important;
    font-size: 13px;
    opacity: 0.7;
}

.search-bar input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    background-color: #333;
}

.search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #e0e0e0;
    cursor: pointer;
    padding: 4px;
    border-radius: 3px;
    transition: color 0.3s ease;
}

.search-btn:hover {
    color: #007bff;
}

.search-loading {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    padding: 4px;
}

/* Search Results Dropdown */
.search-results-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #2a2a2a;
    border: 1px solid #434343;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1050;
    margin-top: 2px;
}

.search-results-content {
    padding: 0;
}

.search-category {
    border-bottom: 1px solid #727272;
}

.search-category:last-child {
    border-bottom: none;
}

.search-category-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #323232;
    border-bottom: 1px solid #414141;
    font-weight: 600;
    color: #b8c0c7;
    font-size: 13px;
}

.search-category-header i {
    margin-right: 8px;
    color: #6c757d;
}

.category-name {
    flex: 1;
}

.category-count {
    background-color: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
}

.search-category-results {
    padding: 0;
}

.search-result-item {
    padding: 10px 12px;
    cursor: pointer;
    border-bottom: 1px solid #454545;
    transition: background-color 0.2s ease;
}

.search-result-item:hover,
.search-result-item.selected {
    background-color: #ffffff20;
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-title {
    font-weight: 500;
    color: #c6d2df;
    margin-bottom: 2px;
    font-size: 14px;
}

.result-title mark {
    background-color: #fff3cd;
    padding: 1px 2px;
    border-radius: 2px;
}

.result-description {
    color: #6c757d;
    font-size: 13px;
    margin-bottom: 4px;
}

.result-metadata {
    font-size: 12px;
    color: #868e96;
}

.metadata-item {
    display: inline-block;
}

.search-view-all {
    padding: 8px 12px;
    background-color: #323232;
    border-top: 1px solid #e9ecef24;
}

.view-all-link {
    color: #007bff;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
}

.view-all-link:hover {
    text-decoration: underline;
}

.search-no-results {
    padding: 20px;
    text-align: center;
}

.no-results-message {
    color: #6c757d;
}

.no-results-message i {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.no-results-message p {
    margin: 0 0 4px 0;
    font-weight: 500;
}

.no-results-message small {
    font-size: 12px;
    color: #868e96;
}

.search-error {
    padding: 15px;
    text-align: center;
    color: #dc3545;
}

.search-error i {
    font-size: 20px;
    margin-bottom: 8px;
    display: block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .search-bar input {
        width: 300px;
    }
}

@media (max-width: 576px) {
    .search-bar input {
        width: 250px;
    }

    .search-results-dropdown {
        left: -50px;
        right: -50px;
    }
}

/* Search Results Page Styles */
.search-results-page {
    padding: 20px;
}

.search-results-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.search-query {
    font-size: 24px;
    font-weight: 300;
    color: #495057;
    margin-bottom: 8px;
}

.search-meta {
    color: #6c757d;
    font-size: 14px;
}

.search-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.search-filter {
    display: flex;
    flex-direction: column;
}

.search-filter label {
    font-size: 13px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
}

.search-filter select {
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    font-size: 14px;
}

.search-results-list {
    margin-top: 20px;
}

.search-result-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 15px;
    margin-bottom: 15px;
    transition: box-shadow 0.2s ease;
}

.search-result-card:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-result-card .result-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.search-result-card .result-title a {
    color: #007bff;
    text-decoration: none;
}

.search-result-card .result-title a:hover {
    text-decoration: underline;
}

.search-pagination {
    margin-top: 30px;
    text-align: center;
}